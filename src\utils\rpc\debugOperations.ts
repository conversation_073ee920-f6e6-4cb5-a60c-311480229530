// 调试操作工具文件 - 专门处理调试功能
declare const chrome: any;

/**
 * 调试操作结果类型
 */
export interface DebugOperationResult {
  success: boolean;
  message: string;
  method: 'background' | 'direct';
  data?: any;
}

/**
 * 方式1: 通过Background调用executeScriptInTab实现页面大小切换
 * 这种方式不直接在content script中操作DOM，而是通过background的RPC机制
 */
export async function setPageSize100ViaBackground(): Promise<DebugOperationResult> {
  return new Promise((resolve) => {
    try {
      console.log('调试: 开始通过Background方式设置页面大小为100条')
      
      // 发送消息到background，请求执行新的简化DOM操作
      chrome.runtime.sendMessage({
        funType: 'rpcDomOperationNew'
      }, (response: any) => {
        if (chrome.runtime.lastError) {
          console.error('调试: Background调用失败:', chrome.runtime.lastError)
          resolve({
            success: false,
            message: `Background调用失败: ${chrome.runtime.lastError.message}`,
            method: 'background'
          });
          return;
        }
        
        console.log('调试: Background响应:', response)
        resolve({
          success: response?.success || false,
          message: response?.message || '未知响应',
          method: 'background',
          data: response
        });
      });
      
    } catch (error: any) {
      console.error('调试: Background方式异常:', error)
      resolve({
        success: false,
        message: `Background方式异常: ${error.message}`,
        method: 'background'
      });
    }
  });
}

/**
 * 方式2: 直接在content script中操作DOM实现页面大小切换
 * 这种方式直接操作当前页面的DOM元素
 */
export async function setPageSize100ViaDirect(): Promise<DebugOperationResult> {
  return new Promise((resolve) => {
    try {
      console.log('调试: 开始通过直接DOM操作方式设置页面大小为100条')
      
      // 查找页面大小选择器
      const selectContainer = document.querySelector('.simpleSelect') || 
                            document.querySelector('[class*="select"]') ||
                            document.querySelector('.page-size-selector');
      
      if (!selectContainer) {
        resolve({
          success: false,
          message: '未找到页面大小选择器',
          method: 'direct'
        });
        return;
      }

      console.log('调试: 找到选择器容器:', selectContainer)

      // 查找下拉按钮
      const dropdownButton = selectContainer.querySelector('button') ||
                            selectContainer.querySelector('.dropdown-toggle') ||
                            selectContainer.querySelector('[role="button"]');
      
      if (!dropdownButton) {
        resolve({
          success: false,
          message: '未找到下拉按钮',
          method: 'direct'
        });
        return;
      }

      console.log('调试: 找到下拉按钮:', dropdownButton)

      // 点击下拉按钮
      const buttonElement = dropdownButton as HTMLElement;
      buttonElement.click();
      console.log('调试: 已点击下拉按钮')
      
              // 等待下拉菜单出现
        setTimeout(() => {
          console.log('调试: 开始查找100选项')
          
          // 根据实际DOM结构查找100选项
          // 先找到下拉内容容器 - 根据提供的DOM结构更精确查找
          const dropdownContent = document.querySelector('.dropdown-content .simpleSelect-content') ||
                                 document.querySelector('.simpleSelect-content') ||
                                 document.querySelector('.dropdown-content');
          
          if (!dropdownContent) {
            resolve({
              success: false,
              message: '未找到下拉内容容器(.simpleSelect-content)',
              method: 'direct'
            });
            return;
          }
          
          console.log('调试: 找到下拉内容容器:', dropdownContent)
          
          // 查找所有选项按钮 - 更精确的选择器
          const optionButtons = dropdownContent.querySelectorAll('li button[type="button"]') ||
                               dropdownContent.querySelectorAll('button');
          let option100Button: Element | null = null;
          
          console.log('调试: 找到选项按钮数量:', optionButtons.length)
          
          for (const button of Array.from(optionButtons)) {
            const text = button.textContent?.trim();
            console.log('调试: 检查按钮文本:', text)
            // 精确匹配土耳其语的"100 Ürün"
            if (text === '100 Ürün' || text === '100' || (text && text.includes('100'))) {
              option100Button = button;
              console.log('调试: 找到100选项按钮:', button)
              break;
            }
          }
          
          if (!option100Button) {
             // 备用查找方式
             const foundButton = Array.from(document.querySelectorAll('button')).find(btn => 
               btn.textContent?.includes('100')
             );
             option100Button = foundButton || null;
             
             console.log('调试: 备用查找结果:', option100Button)
           }
          
          if (option100Button) {
            console.log('调试: 准备点击100选项按钮:', option100Button)
            const buttonElement = option100Button as HTMLElement;
            buttonElement.click();
          
                      // 等待页面更新
            setTimeout(() => {
              console.log('调试: 直接DOM操作完成')
              resolve({
                success: true,
                message: '成功通过直接DOM操作设置每页显示100条',
                method: 'direct'
              });
            }, 1000);
          } else {
            console.log('调试: 未找到100条选项按钮')
            resolve({
              success: false,
              message: '未找到100条选项按钮',
              method: 'direct'
            });
          }
      }, 500);
      
    } catch (error: any) {
      console.error('调试: 直接DOM操作异常:', error)
      resolve({
        success: false,
        message: `直接DOM操作异常: ${error.message}`,
        method: 'direct'
      });
    }
  });
}

/**
 * 调试功能主入口 - 可以选择使用哪种方式
 */
export async function debugSetPageSize100(method: 'background' | 'direct' | 'both' = 'both'): Promise<DebugOperationResult[]> {
  const results: DebugOperationResult[] = [];
  
  console.log(`调试: 开始执行页面大小设置，方式: ${method}`)
  
  if (method === 'background' || method === 'both') {
    console.log('调试: 执行Background方式')
    const backgroundResult = await setPageSize100ViaBackground();
    results.push(backgroundResult);
    
    // 如果是both模式，等待一段时间再执行下一个方式
    if (method === 'both') {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  if (method === 'direct' || method === 'both') {
    console.log('调试: 执行直接DOM操作方式')
    const directResult = await setPageSize100ViaDirect();
    results.push(directResult);
  }
  
  console.log('调试: 所有方式执行完成，结果:', results)
  return results;
}

/**
 * 获取当前页面大小设置（用于调试验证）
 */
export function getCurrentPageSize(): { pageSize: number; method: string } {
  try {
    // 查找页面大小显示元素
    const pageSizeElement = document.querySelector('.page-size') ||
                           document.querySelector('[class*="page-size"]') ||
                           document.querySelector('.simpleSelect button');
    
    if (pageSizeElement) {
      const text = pageSizeElement.textContent || '';
      const match = text.match(/\d+/);
      if (match) {
        return {
          pageSize: parseInt(match[0]),
          method: 'element_text'
        };
      }
    }
    
    // 备用方法：检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const pageSizeParam = urlParams.get('pageSize') || urlParams.get('size') || urlParams.get('limit');
    if (pageSizeParam) {
      return {
        pageSize: parseInt(pageSizeParam),
        method: 'url_param'
      };
    }
    
    return {
      pageSize: 20, // 默认值
      method: 'default'
    };
  } catch (error) {
    console.error('获取当前页面大小失败:', error);
    return {
      pageSize: 20,
      method: 'error'
    };
  }
} 