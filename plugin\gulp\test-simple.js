const gulp = require('gulp');
const gulpJavaScriptObfuscator = require('gulp-javascript-obfuscator');
const path = require('path');
const fs = require('fs');

// 配置路径 - 相对于项目根目录
const rootPath = path.resolve(__dirname, '../../');
const paths = {
  build: path.join(rootPath, 'tsa_build'),
  backup: path.join(rootPath, 'tsa_build_backup'),
  js: path.join(rootPath, 'tsa_build/**/*.js'),
  exclude: [
    `!${path.join(rootPath, 'tsa_build/node_modules/**')}`,
    `!${path.join(rootPath, 'tsa_build/web/node_modules/**')}`,
    `!${path.join(rootPath, 'tsa_build/static/**')}`
  ]
};

console.log('🔍 检查路径配置:');
console.log('根目录:', rootPath);
console.log('构建目录:', paths.build);
console.log('JS文件模式:', paths.js);
console.log('构建目录是否存在:', fs.existsSync(paths.build));

// 简单的备份任务
gulp.task('simple-backup', function(cb) {
  console.log('🔄 开始简单备份...');
  
  // 如果备份目录存在，先删除
  if (fs.existsSync(paths.backup)) {
    console.log('🗑️ 清理旧备份...');
    fs.rmSync(paths.backup, { recursive: true, force: true });
  }
  
  // 复制整个构建目录作为备份
  return gulp.src([paths.build + '/**/*'])
    .pipe(gulp.dest(paths.backup))
    .on('end', () => {
      console.log('✅ 备份完成！');
      console.log('📁 备份位置: ' + paths.backup);
      cb();
    });
});

// 简单的混淆任务
gulp.task('simple-obfuscate', function(cb) {
  console.log('🔒 开始简单混淆...');
  
  return gulp.src([paths.js, ...paths.exclude])
    .pipe(gulpJavaScriptObfuscator({
      compact: true,
      selfDefending: false,
      controlFlowFlattening: false,
      deadCodeInjection: false,
      stringArray: false,
      numbersToExpressions: false,
      identifierNamesGenerator: 'mangled',
      renameGlobals: false,
      reservedNames: [
        'chrome', 'browser', 'manifest', 'background', 'popup', 'content',
        'options', 'tabs', 'storage', 'runtime', 'extension'
      ],
      target: 'browser',
      sourceMap: false
    }))
    .pipe(gulp.dest(paths.build))
    .on('end', () => {
      console.log('✅ 混淆完成！');
      cb();
    });
});

// 组合任务
gulp.task('simple-test', gulp.series('simple-backup', 'simple-obfuscate'));

// 默认任务
gulp.task('default', gulp.task('simple-test')); 