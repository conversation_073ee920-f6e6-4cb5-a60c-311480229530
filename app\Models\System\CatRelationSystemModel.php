<?php

namespace App\Models\System;

use App\Models\BaseModel;
use App\Models\N11\ProductCategoryN11Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class CatRelationSystemModel extends BaseModel
{
    protected $table = 'cat_relation_system';
    
    protected $fillable = [
        'platform_id',
        'cat_platform_id',
        'third_platform_id',
        'cat_third_ids',
    ];


    // 关联N11分类表 - 通过cat_third_ids JSON数组关联
    public function n11Categories()
    {
        // 由于cat_third_ids是JSON数组，需要在Service层处理关联
        return null;
    }

    // 获取N11分类信息的辅助方法
    public function getN11CategoriesAttribute()
    {
        if ($this->third_platform_id == 2 && !empty($this->cat_third_ids)) {
            $categoryIds = is_array($this->cat_third_ids) ? $this->cat_third_ids : json_decode($this->cat_third_ids, true);
            if (is_array($categoryIds)) {
                return ProductCategoryN11Model::whereIn('id', $categoryIds)
                    ->select('id', 'name', 'name_tl', 'path_name', 'path_name_tl')
                    ->get();
            }
        }
        return collect();
    }
}
