@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    TSA Chrome扩展代码混淆工具 v1.0
echo ==========================================
echo.

:: 切换到plugin/gulp目录
cd /d "%~dp0"

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js，请先安装Node.js
    pause
    exit /b 1
)

:: 检查是否已安装gulp依赖
if not exist "node_modules\gulp" (
    echo 📦 首次运行，正在安装依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
    echo.
)

:: 检查构建目录是否存在
if not exist "..\..\tsa_build" (
    echo ❌ 错误: 构建目录 tsa_build 不存在
    echo 请先运行 npm run build 进行项目构建
    pause
    exit /b 1
)

echo 🚀 开始执行混淆流程...
echo.

:: 显示菜单
echo 请选择混淆模式:
echo.
echo 🚀 混淆模式:
echo   1. 超快速混淆 (⚡ 仅基础混淆，速度最快)
echo   2. 平衡混淆   (⚖️ 速度与安全平衡，推荐)
echo   3. 完整混淆   (🔒 JS + HTML + CSS)
echo   4. 高强度混淆 (🔐 最高安全性，速度较慢)
echo   5. 关键文件混淆 (🎯 仅混淆 background.main.js)
echo.
echo 🛠️ 其他操作:
echo   6. 恢复备份
echo   7. 清理备份文件
echo   8. 简单测试 (调试用)
echo   9. 检查 background.main.js
echo   0. 退出
echo.

set /p choice=请输入选择 (0-9): 

if "%choice%"=="1" (
    echo.
    echo ⚡ 执行超快速混淆（约10-30秒）...
    echo 🔍 调试信息: 当前目录 = %CD%
    echo 🔍 调试信息: 构建目录 = ..\..\tsa_build
    call npx gulp obfuscate-fast
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 超快速混淆失败，尝试简单测试...
        call npx gulp -f test-simple.js simple-test
    )
    goto :end
)

if "%choice%"=="2" (
    echo.
    echo ⚖️ 执行平衡混淆（约30秒-2分钟）...
    echo 🔍 调试信息: 当前目录 = %CD%
    echo 🔍 调试信息: 构建目录 = ..\..\tsa_build
    call npx gulp obfuscate-quick
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 平衡混淆失败，尝试简单测试...
        call npx gulp -f test-simple.js simple-test
    )
    goto :end
)

if "%choice%"=="3" (
    echo.
    echo 🔒 执行完整混淆（约1-3分钟）...
    echo 🔍 调试信息: 当前目录 = %CD%
    echo 🔍 调试信息: 构建目录 = ..\..\tsa_build
    call npx gulp obfuscate
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 完整混淆失败，尝试简单测试...
        call npx gulp -f test-simple.js simple-test
    )
    goto :end
)

if "%choice%"=="4" (
    echo.
    echo 🔐 执行高强度混淆（约3-10分钟）...
    echo 🔍 调试信息: 当前目录 = %CD%
    echo 🔍 调试信息: 构建目录 = ..\..\tsa_build
    call npx gulp obfuscate-strong
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 高强度混淆失败，尝试简单测试...
        call npx gulp -f test-simple.js simple-test
    )
    goto :end
)

if "%choice%"=="5" (
    echo.
    echo 🎯 执行关键文件混淆（仅 background.main.js）...
    echo 🔍 调试信息: 当前目录 = %CD%
    echo 🔍 调试信息: 目标文件 = ..\..\tsa_build\background.main.js
    call npx gulp obfuscate-background
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 关键文件混淆失败
    )
    goto :end
)

if "%choice%"=="6" (
    echo.
    echo 🔄 恢复备份...
    call npx gulp restore
    goto :end
)

if "%choice%"=="7" (
    echo.
    echo 🧹 清理备份文件...
    call npx gulp clean-backup
    goto :end
)

if "%choice%"=="8" (
    echo.
    echo 🧪 执行简单测试...
    call npx gulp -f test-simple.js simple-test
    goto :end
)

if "%choice%"=="9" (
    echo.
    echo 🔍 检查 background.main.js 文件...
    call check-background.bat
    goto :end
)

if "%choice%"=="0" (
    echo 👋 再见!
    exit /b 0
)

echo ❌ 无效选择，请重新运行脚本
pause
exit /b 1

:end
echo.
if %errorlevel% equ 0 (
    echo ✅ 操作完成!
    echo.
    echo 💡 提示:
    echo   - 混淆后的文件在 ../../tsa_build 目录
    echo   - 备份文件在 ../../tsa_build_backup 目录
    echo   - 如有问题可使用选项3恢复备份
) else (
    echo ❌ 操作失败，错误代码: %errorlevel%
    echo 请检查错误信息并重试
)

echo.
pause 