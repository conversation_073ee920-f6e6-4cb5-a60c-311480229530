<?php

namespace App\Utils;

trait GoodsNameTrait
{
    /**
     * 截取商品名称，确保长度不超过指定限制
     * 
     * @param string|null $goodsName 商品名称
     * @param int $maxLength 最大长度限制
     * @return string 截取后的商品名称
     */
    protected function truncateGoodsName(?string $goodsName, int $maxLength = 255): string
    {
        // 如果商品名称为空或null，返回空字符串
        if (empty($goodsName)) {
            return '';
        }
        
        // 如果长度未超过限制，直接返回
        if (mb_strlen($goodsName, 'UTF-8') <= $maxLength) {
            return $goodsName;
        }
        
        // 定义分隔符优先级：逗号 > 竖线 > 空格
        $separators = [',', '|', ' '];
        
        $currentName = $goodsName;
        
        // 循环截取直到长度满足要求
        while (mb_strlen($currentName, 'UTF-8') > $maxLength) {
            $truncated = false;
            
            // 按优先级查找分隔符
            foreach ($separators as $separator) {
                // 从后面开始查找分隔符
                $lastPos = mb_strrpos($currentName, $separator, 0, 'UTF-8');
                
                if ($lastPos !== false) {
                    // 截取到分隔符位置（不包含分隔符）
                    $currentName = mb_substr($currentName, 0, $lastPos, 'UTF-8');
                    $truncated = true;
                    break;
                }
            }
            
            // 如果没有找到任何分隔符，直接截取到最大长度
            if (!$truncated) {
                $currentName = mb_substr($currentName, 0, $maxLength, 'UTF-8');
                break;
            }
        }
        
        return empty($currentName) ? '' : trim($currentName);
    }
} 