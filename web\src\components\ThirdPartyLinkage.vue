<template>
  <div class="third-party-linkage">
    <!-- 当前操作的Temu分类信息 -->
    <div class="current-category-info">
      <h3>当前操作的Temu分类</h3>
      <div class="category-info">
        <span class="category-id">ID: {{ temuCategory.id }}</span>
        <span class="category-name">{{ temuCategory.name }}</span>
        <span class="category-path">{{ temuCategory.path_name.replace(/,/g, '->') }}</span>
      </div>
    </div>

    <!-- 已关联的分类信息 -->
    <div v-if="linkedCategories.length > 0" class="linked-categories-info">
      <h3>已关联的分类信息</h3>
      <div class="linked-categories-list">
        <el-table :data="linkedCategories" stripe border size="small">
          <el-table-column label="ID" prop="id" width="80" align="center" />
          <el-table-column label="分类名称" min-width="250" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="linked-category-name">
                {{ row.name }}
                <template v-if="row.path_name">
                  <span class="path-display">【{{ row.path_name.replace(/,/g, '->') }}】</span>
                </template>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="分类名称" min-width="250" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="linked-category-name-tl">
                {{ row.name_tl }}
                <template v-if="row.path_name_tl">
                  <span class="path-display">【{{ row.path_name_tl.replace(/,/g, '->') }}】</span>
                </template>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 管理员选择关联类型 -->
    <div v-if="userInfo?.is_admin === 1" class="admin-section">
      <el-form :model="linkForm" label-width="100px">
        <el-form-item label="选择类型">
          <el-radio-group v-model="linkForm.linkType">
            <el-radio :label="1">系统关联</el-radio>
            <el-radio :label="2">用户关联</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

    <!-- 选择平台 -->
    <div class="platform-section">
      <el-form :model="linkForm" label-width="100px">
        <el-form-item label="选择平台">
          <el-radio-group v-model="linkForm.platformId" @change="handlePlatformChange">
            <el-radio 
              v-for="platform in platforms" 
              :key="platform.id" 
              :label="platform.id"
            >
              {{ platform.name }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

    <!-- 检索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="分类名称">
          <el-input 
            v-model="searchForm.name" 
            placeholder="请输入分类名称" 
            clearable 
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            检索
          </el-button>
          <el-button type="success" @click="handleConfirmLink" :disabled="selectedCategories.length === 0">
            确认关联分类
          </el-button>
          <template v-if="userInfo?.is_admin === 1">
            <el-button type="warning" @click="handleSetUnlinked">
              设置为未关联
            </el-button>
            <el-button type="danger" @click="handleSetNoLink">
              设置为无需关联
            </el-button>
          </template>
        </el-form-item>
      </el-form>
    </div>

    <!-- 第三方平台分类表格 -->
    <div class="table-section">
      <el-table 
        :data="thirdPartyCategoryList" 
        v-loading="loading"
        stripe
        border
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
        ref="tableRef"
        @select="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :selectable="isSelectable">
          <template #header>
            <span style="color: #909399; font-size: 12px;">选择</span>
          </template>
        </el-table-column>
        <el-table-column label="ID" prop="id" width="120" align="right" />
        <el-table-column label="分类名称" prop="name" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="category-name" :class="{ [`level-${row.level}`]: row.level }">
              {{ row.name }}
              <template v-if="row.path_name">
                <span class="path-display">【{{ row.path_name.replace(/,/g, '->') }}】</span>
              </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="分类名称" prop="name_tl" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="category-name" :class="{ [`level-${row.level}`]: row.level }">
              {{ row.name_tl }}
              <template v-if="row.path_name_tl">
                <span class="path-display">【{{ row.path_name_tl.replace(/,/g, '->') }}】</span>
              </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="层级" prop="level" width="60" />
        <el-table-column label="末级" width="60">
          <template #default="{ row }">
            <el-tag :type="row.is_leaf ? 'success' : 'info'">
              {{ row.is_leaf ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updated_at" width="160" />
        <el-table-column label="操作" width="120" fixed="right" v-if="userInfo?.is_admin === 1">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleEditCategory(row)"
              :loading="editingCategoryId === row.id"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- N11分类编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑N11分类"
      width="600px"
      :close-on-click-modal="false"
      :z-index="3000"
      append-to-body
      class="edit-category-dialog"
    >
      <div v-if="editingCategory" class="edit-category-form">
        <!-- 分类基本信息展示 -->
        <div class="category-info-section">
          <h4>分类信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">分类ID:</span>
              <span class="value">{{ editingCategory.id }}</span>
            </div>
            <div class="info-item">
              <span class="label">父级ID:</span>
              <span class="value">{{ editingCategory.parent_id }}</span>
            </div>
            <div class="info-item">
              <span class="label">层级:</span>
              <span class="value">第{{ editingCategory.level }}级</span>
            </div>
            <div class="info-item">
              <span class="label">是否叶子:</span>
              <el-tag :type="editingCategory.is_leaf ? 'success' : 'info'" size="small">
                {{ editingCategory.is_leaf ? '是' : '否' }}
              </el-tag>
            </div>
            <div class="info-item full-width">
              <span class="label">路径:</span>
              <span class="value">{{ editingCategory.path }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">当前路径名称:</span>
              <div class="value path-names">
                <div class="path-name-primary">{{ displayPathName }}</div>
                <div v-if="displayPathNameTl" class="path-name-secondary">{{ displayPathNameTl }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑表单 -->
        <div class="edit-form-section">
          <h4>编辑分类名称</h4>
          <el-form :model="editForm" :rules="editFormRules" ref="editFormRef" label-width="120px">
            <el-form-item label="分类名称" prop="name">
              <el-input 
                v-model="editForm.name" 
                placeholder="请输入分类名称"
                maxlength="200"
                show-word-limit
                clearable
              />
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                修改后将自动更新相关的路径名称
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 影响范围预览 -->
        <div v-if="editingCategory && editForm.name !== editingCategory.name" class="impact-preview-section">
          <h4>影响范围预览</h4>
          <div class="impact-info">
            <div class="impact-item">
              <span class="impact-label">当前分类:</span>
              <span class="impact-value">
                {{ editingCategory.name }} → {{ editForm.name }}
              </span>
            </div>
            <div class="impact-item">
              <span class="impact-label">路径名称:</span>
              <div class="impact-value">
                <div class="path-change">
                  <div class="path-before">{{ displayPathName }}</div>
                  <div class="path-arrow">→</div>
                  <div class="path-after">{{ getPreviewPathName() }}</div>
                </div>
              </div>
            </div>
            <div v-if="!editingCategory.is_leaf" class="impact-item">
              <span class="impact-label">影响子分类:</span>
              <span class="impact-value warning">
                <el-icon><Warning /></el-icon>
                将同时更新所有子分类的路径名称
              </span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false" :disabled="editLoading">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmEdit" 
            :loading="editLoading"
            :disabled="!editForm.name || editForm.name === editingCategory?.name"
          >
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ElTable, ElForm } from 'element-plus'
import { Search, InfoFilled, Warning } from '@element-plus/icons-vue'
import { LINK_TYPES } from '../utils/apiConfig'
import { 
  getUserInfo as fetchUserInfo,
  getN11CategoryList,
  getN11CategoryDetail,
  updateN11Category,
  linkThirdPartyCategory,
  type ThirdPartyCategory,
  type UserInfo
} from '../utils/thirdPartyApi'
import { 
  getLinkedCategoryList,
  type LinkedCategory
} from '../utils/categoryApi'

// Props定义
interface Platform {
  id: number
  name: string
  code: string
  apiUrl: string
}

interface TemuCategory {
  id: number
  name: string
  path_name: string
  level: number
  is_leaf: boolean
}

const props = defineProps<{
  temuCategory: TemuCategory
  platforms: Platform[]
}>()

const emit = defineEmits<{
  close: []
  success: [updatedCategory?: TemuCategory]
}>()

// 响应式数据
const loading = ref(false)
const userInfo = ref<UserInfo | null>(null)
const thirdPartyCategoryList = ref<ThirdPartyCategory[]>([])
const selectedCategories = ref<ThirdPartyCategory[]>([])
const linkedCategories = ref<LinkedCategory[]>([])
const tableRef = ref<InstanceType<typeof ElTable>>()

// 编辑功能相关数据
const editDialogVisible = ref(false)
const editingCategory = ref<ThirdPartyCategory | null>(null)
const editingCategoryId = ref<number | null>(null)
const editLoading = ref(false)
const editFormRef = ref<InstanceType<typeof ElForm>>()

// 编辑表单数据
const editForm = reactive({
  name: ''
})

// 编辑表单验证规则
const editFormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 200, message: '分类名称长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

// 表单数据
const linkForm = reactive({
  linkType: 1, // 1: 系统关联, 2: 用户关联
  platformId: 1 // 默认选择第一个平台
})

const searchForm = reactive({
  name: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 计算属性 - 当前选择的平台
const currentPlatform = computed(() => {
  return props.platforms.find(p => p.id === linkForm.platformId)
})

// 计算属性 - 格式化显示的路径名称
const displayPathName = computed(() => {
  if (!editingCategory.value?.path_name) return ''
  return editingCategory.value.path_name.replace(/,/g, '->')
})

// 计算属性 - 格式化显示的翻译路径名称
const displayPathNameTl = computed(() => {
  if (!editingCategory.value?.path_name_tl) return ''
  return editingCategory.value.path_name_tl.replace(/,/g, '->')
})

// 预览路径名称
const getPreviewPathName = () => {
  if (!editingCategory.value || !editForm.name) return ''
  
  const pathParts = editingCategory.value.path_name.split(',')
  const pathIds = editingCategory.value.path.split(',')
  
  // 找到当前分类在path中的位置
  const currentIndex = pathIds.findIndex(id => parseInt(id) === editingCategory.value!.id)
  if (currentIndex !== -1) {
    // 创建新的路径名称数组，替换对应位置的名称
    const newPathParts = [...pathParts]
    newPathParts[currentIndex] = editForm.name
    return newPathParts.join('->') // 返回格式化后的显示结果
  }
  
  return displayPathName.value
}

// 获取用户信息
const getUserInfo = async () => {
  try {
    const response = await fetchUserInfo()
    userInfo.value = response
    
    // 如果不是管理员，设置为用户关联
    if (response.is_admin !== 1) {
      linkForm.linkType = LINK_TYPES.USER
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 获取第三方平台分类列表
const getThirdPartyCategoryList = async () => {
  if (!currentPlatform.value) return
  
  loading.value = true
  try {
    // 目前只支持N11平台
    if (currentPlatform.value.code === 'n11') {
      const response = await getN11CategoryList({
        page: pagination.currentPage,
        pageSize: pagination.pageSize,
        ...(searchForm.name && { name: searchForm.name })
      })
      
      thirdPartyCategoryList.value = response.list || []
      pagination.total = response.pagination?.total || 0
    }
  } catch (error) {
    console.error('获取第三方平台分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 获取已关联的分类列表
const getLinkedCategories = async () => {
  try {
    const response = await getLinkedCategoryList(props.temuCategory.id)
    linkedCategories.value = response.list || []
    console.log('获取已关联分类列表成功:', response)
  } catch (error) {
    console.error('获取已关联分类列表失败:', error)
    // 不显示错误消息，因为可能是正常的没有关联分类的情况
  }
}

// 平台变化处理
const handlePlatformChange = () => {
  // 清空选择和搜索
  selectedCategories.value = []
  searchForm.name = ''
  pagination.currentPage = 1
  
  // 重新加载数据
  getThirdPartyCategoryList()
}

// 检索
const handleSearch = () => {
  pagination.currentPage = 1
  selectedCategories.value = []
  getThirdPartyCategoryList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getThirdPartyCategoryList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getThirdPartyCategoryList()
}

// 判断行是否可选择
const isSelectable = (row: ThirdPartyCategory) => {
  // 兼容布尔值和数字类型
  return row.is_leaf === true || (row.is_leaf as any) === 1
}

// 处理选择变化
const handleSelectionChange = (selection: ThirdPartyCategory[], row: ThirdPartyCategory) => {
  console.log('handleSelectionChange:', {
    row_id: row.id,
    row_is_leaf: row.is_leaf,
    selection_length: selection.length
  })
  
  // 只处理末级分类的选择
  if (row.is_leaf !== true && (row.is_leaf as any) !== 1) {
    return
  }
  
  // 更新选中的分类列表
  updateSelectedCategories()
}

// 处理全选 
const handleSelectAll = (selection: ThirdPartyCategory[]) => {
  console.log('handleSelectAll:', {
    total_selection: selection.length,
    selection_items: selection.map(item => ({ id: item.id, is_leaf: item.is_leaf }))
  })
  
  // 只保留末级分类（兼容布尔值和数字类型）
  selectedCategories.value = selection.filter(item => item.is_leaf === true || (item.is_leaf as any) === 1)
  
  console.log('过滤后的选择:', {
    filtered_count: selectedCategories.value.length,
    filtered_items: selectedCategories.value.map(item => ({ id: item.id, is_leaf: item.is_leaf }))
  }) 
}

// 更新选中的分类列表
const updateSelectedCategories = () => {
  if (tableRef.value) {
    const allSelected = tableRef.value.getSelectionRows()
    console.log('updateSelectedCategories - 所有选中项:', {
      count: allSelected.length,
      items: allSelected.map(item => ({ id: item.id, is_leaf: item.is_leaf, type: typeof item.is_leaf }))
    })
    
    // 只保留末级分类（兼容布尔值和数字类型）
    selectedCategories.value = allSelected.filter(item => item.is_leaf === true || (item.is_leaf as any) === 1)
    
    console.log('过滤后的选中项:', {
      count: selectedCategories.value.length,
      items: selectedCategories.value.map(item => ({ id: item.id, is_leaf: item.is_leaf }))
    })
  }
}

// 确认关联分类
const handleConfirmLink = async () => {
  if (selectedCategories.value.length === 0) {
    ElMessage.warning('请选择要关联的分类')
    return
  }

  // 去重并整理数据
  const uniqueCategories = Array.from(
    new Map(selectedCategories.value.map(cat => [cat.id, cat])).values()
  )

  // 对比已关联分类和新选择的分类
  const currentLinkedIds = new Set(linkedCategories.value.map(cat => cat.id))
  const newSelectedIds = new Set(uniqueCategories.map(cat => cat.id))
  
  // 计算增加和减少的分类
  const addedCategories = uniqueCategories.filter(cat => !currentLinkedIds.has(cat.id))
  const removedCategories = linkedCategories.value.filter(cat => !newSelectedIds.has(cat.id))
  
  // 如果没有任何变化，直接返回
  if (addedCategories.length === 0 && removedCategories.length === 0) {
    ElMessage.info('关联分类没有任何变化，无需提交')
    return
  }

  // 构建确认消息
  const buildConfirmMessage = () => {
    const platformName = currentPlatform.value?.name || '第三方平台'
    
    let htmlMessage = `
      <div style="font-size: 14px; line-height: 1.6;">
        <div style="font-size: 16px; font-weight: bold; color: #333; margin-bottom: 20px; text-align: center; padding-bottom: 10px; border-bottom: 2px solid #e6a23c;">确认更新关联分类吗？</div>
        
        <!-- TEMU分类信息 -->
        <div style="margin-bottom: 20px; padding: 15px; background-color: #e6f7ff; border: 1px solid #91d5ff; border-radius: 6px;">
          <div style="font-weight: bold; font-size: 15px; margin-bottom: 10px; color: #1890ff;">
            📦 TEMU分类信息：
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background-color: #fff; border-radius: 4px; border: 1px solid #d9d9d9;">
            <span style="font-weight: 600; color: #1890ff; font-size: 15px;">${props.temuCategory.name}</span>
            <span style="font-size: 12px; color: #666; background-color: #f0f0f0; padding: 3px 8px; border-radius: 3px; font-family: monospace;">ID: ${props.temuCategory.id}</span>
          </div>
          <div style="margin-top: 8px; font-size: 12px; color: #666;">
            路径：${props.temuCategory.path_name}
          </div>
        </div>
    `
    
    if (linkedCategories.value.length > 0) {
      htmlMessage += `
        <div style="margin-bottom: 20px;">
          <div style="font-weight: bold; font-size: 15px; margin-bottom: 10px; padding: 8px 12px; border-radius: 4px; background-color: #fff7e6; color: #fa8c16; border-left: 4px solid #fa8c16;">
            🔗 当前已关联的${platformName}分类 (${linkedCategories.value.length} 个)：
          </div>
          <div style="border: 1px solid #ebeef5; border-radius: 4px; overflow: hidden;">
      `
      linkedCategories.value.forEach(cat => {
        htmlMessage += `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; border-bottom: 1px solid #f0f0f0; background-color: #fffbf0;">
              <div style="flex: 1;">
                <div style="font-weight: 500; color: #fa8c16; margin-bottom: 4px;">${cat.name}</div>
                <div style="font-size: 12px; color: #999;">${cat.name_tl || ''}</div>
              </div>
              <span style="font-size: 12px; color: #999; background-color: #f5f7fa; padding: 2px 6px; border-radius: 3px; font-family: monospace;">ID: ${cat.id}</span>
            </div>
        `
      })
      htmlMessage += `
          </div>
        </div>
      `
    }
    
    htmlMessage += `
      <div style="margin-bottom: 20px;">
        <div style="font-weight: bold; font-size: 15px; margin-bottom: 10px; padding: 8px 12px; border-radius: 4px; background-color: #f6ffed; color: #52c41a; border-left: 4px solid #52c41a;">
          ✅ 本次选择的${platformName}分类 (${uniqueCategories.length} 个)：
        </div>
        <div style="border: 1px solid #ebeef5; border-radius: 4px; overflow: hidden;">
    `
    uniqueCategories.forEach(cat => {
      htmlMessage += `
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; border-bottom: 1px solid #f0f0f0; background-color: #f6ffed;">
            <div style="flex: 1;">
              <div style="font-weight: 500; color: #52c41a; margin-bottom: 4px;">${cat.name}</div>
              <div style="font-size: 12px; color: #999;">${cat.name_tl || ''}</div>
            </div>
            <span style="font-size: 12px; color: #999; background-color: #f5f7fa; padding: 2px 6px; border-radius: 3px; font-family: monospace;">ID: ${cat.id}</span>
          </div>
      `
    })
    htmlMessage += `
        </div>
      </div>
    `
    
    if (addedCategories.length > 0) {
      htmlMessage += `
        <div style="margin-bottom: 20px;">
          <div style="font-weight: bold; font-size: 15px; margin-bottom: 10px; padding: 8px 12px; border-radius: 4px; background-color: #f0f9ff; color: #1890ff; border-left: 4px solid #1890ff;">
            ➕ 新增关联的${platformName}分类 (${addedCategories.length} 个)：
          </div>
          <div style="border: 1px solid #ebeef5; border-radius: 4px; overflow: hidden;">
      `
      addedCategories.forEach(cat => {
        htmlMessage += `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; border-bottom: 1px solid #f0f0f0; background-color: #f0f9ff; border-left: 3px solid #1890ff;">
              <div style="flex: 1;">
                <div style="font-weight: 500; color: #1890ff; margin-bottom: 4px;">${cat.name}</div>
                <div style="font-size: 12px; color: #999;">${cat.name_tl || ''}</div>
              </div>
              <span style="font-size: 12px; color: #999; background-color: #f5f7fa; padding: 2px 6px; border-radius: 3px; font-family: monospace;">ID: ${cat.id}</span>
            </div>
        `
      })
      htmlMessage += `
          </div>
        </div>
      `
    }
    
    if (removedCategories.length > 0) {
      htmlMessage += `
        <div style="margin-bottom: 20px;">
          <div style="font-weight: bold; font-size: 15px; margin-bottom: 10px; padding: 8px 12px; border-radius: 4px; background-color: #fff2f0; color: #ff4d4f; border-left: 4px solid #ff4d4f;">
            ➖ 移除关联的${platformName}分类 (${removedCategories.length} 个)：
          </div>
          <div style="border: 1px solid #ebeef5; border-radius: 4px; overflow: hidden;">
      `
      removedCategories.forEach(cat => {
        htmlMessage += `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; border-bottom: 1px solid #f0f0f0; background-color: #fff2f0; border-left: 3px solid #ff4d4f;">
              <div style="flex: 1;">
                <div style="font-weight: 500; color: #ff4d4f; margin-bottom: 4px;">${cat.name}</div>
                <div style="font-size: 12px; color: #999;">${cat.name_tl || ''}</div>
              </div>
              <span style="font-size: 12px; color: #999; background-color: #f5f7fa; padding: 2px 6px; border-radius: 3px; font-family: monospace;">ID: ${cat.id}</span>
            </div>
        `
      })
      htmlMessage += `
          </div>
        </div>
      `
    }
    
    // 添加关联关系说明
    htmlMessage += `
      <div style="margin-top: 20px; padding: 12px; background-color: #f0f2f5; border-radius: 6px; border-left: 4px solid #722ed1;">
        <div style="font-weight: bold; color: #722ed1; margin-bottom: 8px;">🔄 关联关系说明：</div>
        <div style="color: #666; font-size: 13px; line-height: 1.5;">
          TEMU分类 "<strong style="color: #1890ff;">${props.temuCategory.name}</strong>" (ID: ${props.temuCategory.id}) 
          将关联到 ${uniqueCategories.length} 个${platformName}分类
        </div>
      </div>
    `
    
    htmlMessage += `
      </div>
    `
    
    return htmlMessage
  }

  try {
    await ElMessageBox.confirm(
      buildConfirmMessage(),
      '确认关联',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customStyle: {
          width: '600px',
          maxWidth: '90vw'
        }
      }
    )

    const linkData = {
      temu_category_id: props.temuCategory.id,
      temu_category_level: props.temuCategory.level,
      temu_category_is_leaf: props.temuCategory.is_leaf,
      link_type: linkForm.linkType,
      platform_id: linkForm.platformId,
      platform_code: currentPlatform.value?.code || '',
      categories: uniqueCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        level: cat.level,
        is_leaf: cat.is_leaf,
        path_name: cat.path_name
      }))
    }

    console.log('linkData', linkData)

    await linkThirdPartyCategory(linkData)
    
    // 重新加载已关联分类列表
    await getLinkedCategories()
    
    // 清空选择的分类
    selectedCategories.value = []
    if (tableRef.value) {
      tableRef.value.clearSelection()
    }
    
    emit('success', props.temuCategory)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('关联分类失败:', error)
      ElMessage.error('关联分类失败')
    }
  }
}

// 设置为未关联
const handleSetUnlinked = async () => {
  const message = `
    <div style="font-size: 14px; line-height: 1.6;">
      <div style="font-size: 16px; font-weight: bold; color: #333; margin-bottom: 20px; text-align: center; padding-bottom: 10px; border-bottom: 2px solid #e6a23c;">确认设置为未关联吗？</div>
      <div style="margin-bottom: 20px; padding: 15px; background-color: #e6f7ff; border: 1px solid #91d5ff; border-radius: 6px;">
        <div style="font-weight: bold; font-size: 15px; margin-bottom: 10px; color: #1890ff;">
          📦 TEMU分类信息：
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background-color: #fff; border-radius: 4px; border: 1px solid #d9d9d9;">
          <span style="font-weight: 600; color: #1890ff; font-size: 15px;">${props.temuCategory.name}</span>
          <span style="font-size: 12px; color: #666; background-color: #f0f0f0; padding: 3px 8px; border-radius: 3px; font-family: monospace;">ID: ${props.temuCategory.id}</span>
        </div>
        <div style="margin-top: 8px; font-size: 12px; color: #666;">
          路径：${props.temuCategory.path_name}
        </div>
      </div>
      <div style="margin-top: 20px; padding: 12px; background-color: #fff2f0; border-radius: 6px; border-left: 4px solid #ff4d4f;">
        <div style="font-weight: bold; color: #ff4d4f; margin-bottom: 8px;">⚠️ 注意：</div>
        <div style="color: #666; font-size: 13px; line-height: 1.5;">
          当前操作会将该分类设置为 <strong style="color: #ff4d4f;">未关联</strong> 状态，未关联分类的商品暂不支持采集。
        </div>
      </div>
    </div>
  `
  try {
    await ElMessageBox.confirm(
      message,
      '设置为未关联',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customStyle: {
          width: '600px',
          maxWidth: '90vw'
        }
      }
    )

    const linkData = {
      temu_category_id: props.temuCategory.id,
      temu_category_level: props.temuCategory.level,
      temu_category_is_leaf: props.temuCategory.is_leaf,
      link_type: linkForm.linkType,
      relation_status: 1 // 1表示未关联
    }

    await linkThirdPartyCategory(linkData)
    ElMessage.success('已成功设置为未关联')
    emit('success', props.temuCategory)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置为未关联失败:', error)
      ElMessage.error('设置为未关联失败')
    }
  }
}

// 设置为无需关联
const handleSetNoLink = async () => {
  const message = `
    <div style="font-size: 14px; line-height: 1.6;">
      <div style="font-size: 16px; font-weight: bold; color: #333; margin-bottom: 20px; text-align: center; padding-bottom: 10px; border-bottom: 2px solid #e6a23c;">确认设置为无需关联吗？</div>
      <div style="margin-bottom: 20px; padding: 15px; background-color: #e6f7ff; border: 1px solid #91d5ff; border-radius: 6px;">
        <div style="font-weight: bold; font-size: 15px; margin-bottom: 10px; color: #1890ff;">
          📦 TEMU分类信息：
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background-color: #fff; border-radius: 4px; border: 1px solid #d9d9d9;">
          <span style="font-weight: 600; color: #1890ff; font-size: 15px;">${props.temuCategory.name}</span>
          <span style="font-size: 12px; color: #666; background-color: #f0f0f0; padding: 3px 8px; border-radius: 3px; font-family: monospace;">ID: ${props.temuCategory.id}</span>
        </div>
        <div style="margin-top: 8px; font-size: 12px; color: #666;">
          路径：${props.temuCategory.path_name}
        </div>
      </div>
      <div style="margin-top: 20px; padding: 12px; background-color: #fff2f0; border-radius: 6px; border-left: 4px solid #ff4d4f;">
        <div style="font-weight: bold; color: #ff4d4f; margin-bottom: 8px;">⚠️ 注意：</div>
        <div style="color: #666; font-size: 13px; line-height: 1.5;">
          当前操作会将该分类设置为 <strong style="color: #ff4d4f;">无需关联</strong> 状态，无需关联分类的商品暂不支持采集。
        </div>
      </div>
    </div>
  `
  try {
    await ElMessageBox.confirm(
      message,
      '设置为无需关联',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customStyle: {
          width: '600px',
          maxWidth: '90vw'
        }
      }
    )

    const linkData = {
      temu_category_id: props.temuCategory.id,
      temu_category_level: props.temuCategory.level,
      temu_category_is_leaf: props.temuCategory.is_leaf,
      link_type: linkForm.linkType,
      relation_status: 2 // 2表示无需关联
    }

    await linkThirdPartyCategory(linkData)
    ElMessage.success('已成功设置为无需关联')
    emit('success', props.temuCategory)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置为无需关联失败:', error)
      ElMessage.error('设置为无需关联失败')
    }
  }
}

// 编辑分类
const handleEditCategory = async (category: ThirdPartyCategory) => {
  try {
    editingCategoryId.value = category.id
    
    // 获取分类详情
    const categoryDetail = await getN11CategoryDetail(category.id)
    editingCategory.value = categoryDetail
    editForm.name = categoryDetail.name
    
    editDialogVisible.value = true
  } catch (error) {
    console.error('获取分类详情失败:', error)
    ElMessage.error('获取分类详情失败')
  } finally {
    editingCategoryId.value = null
  }
}

// 确认编辑
const handleConfirmEdit = async () => {
  if (!editFormRef.value || !editingCategory.value) return
  
  try {
    // 表单验证
    await editFormRef.value.validate()
    
    editLoading.value = true
    
    // 调用更新接口
    await updateN11Category(editingCategory.value.id, {
      name: editForm.name
    })
    
    ElMessage.success('分类更新成功')
    editDialogVisible.value = false
    
    // 重新加载分类列表
    await getThirdPartyCategoryList()
    
  } catch (error) {
    console.error('更新分类失败:', error)
    ElMessage.error('更新分类失败')
  } finally {
    editLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  getUserInfo()
  getThirdPartyCategoryList()
  getLinkedCategories()
})

// 定义组件名称
defineOptions({
  name: 'ThirdPartyLinkage'
})
</script>

<script lang="ts">
export default {};
</script>

<style scoped>
.third-party-linkage {
  padding: 20px 0;
}

.current-category-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.current-category-info h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.category-id {
  background-color: #f0f2f5;
  padding: 2px 8px;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  font-family: monospace;
}

.category-name {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.category-path {
  color: #666;
  font-size: 14px;
}

.admin-section,
.platform-section {
  background: #fff;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-section {
  background: #fff;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

/* 分类名称样式 */
.category-name.level-1 {
  font-weight: bold;
  color: #409eff;
}

.category-name.level-2 {
  color: #67c23a;
}

.category-name.level-3 {
  color: #e6a23c;
}

.category-name.level-4 {
  color: #f56c6c;
}

.path-display {
  color: #999;
  margin-left: 10px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

/* 隐藏表头的全选框 */
:deep(.el-table__header-wrapper .el-checkbox) {
  display: none !important;
}

/* 或者使用更具体的选择器 */
:deep(.el-table .el-table__header .el-table-column--selection .el-checkbox) {
  display: none !important;
}

.linked-categories-info {
  background: #fff9e6;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #ffd666;
}

.linked-categories-info h3 {
  margin: 0 0 15px 0;
  color: #e6a23c;
  font-size: 16px;
}

.linked-categories-list {
  max-height: 200px;
  overflow-y: auto;
}

/* 已关联分类名称样式 */
.linked-category-name {
  font-weight: bold;
  color: #409eff;
}

.linked-category-name-tl {
  font-weight: bold;
  color: #67c23a;
}

/* 编辑对话框样式 */
.edit-category-dialog {
  z-index: 3000 !important;
}

.edit-category-form {
  max-height: 70vh;
  overflow-y: auto;
}

.category-info-section,
.edit-form-section,
.impact-preview-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
}

.category-info-section {
  background-color: #f8f9fa;
}

.edit-form-section {
  background-color: #fff;
}

.impact-preview-section {
  background-color: #fff9e6;
  border-color: #ffd666;
}

.category-info-section h4,
.edit-form-section h4,
.impact-preview-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
  word-break: break-all;
}

.path-names {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.path-name-primary {
  color: #303133;
  font-weight: 500;
}

.path-name-secondary {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.path-change {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.path-before {
  color: #909399;
  text-decoration: line-through;
}

.path-arrow {
  color: #409eff;
  font-weight: bold;
}

.path-after {
  color: #67c23a;
  font-weight: 500;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.impact-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.impact-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.impact-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.impact-value {
  color: #303133;
  flex: 1;
}

.impact-value.warning {
  color: #e6a23c;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .impact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .impact-label {
    min-width: auto;
  }
  
  .path-change {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .path-arrow {
    align-self: center;
  }
}
</style>