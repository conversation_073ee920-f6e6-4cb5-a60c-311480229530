import { getNextPendingProduct, updateProductStatus, PendingProduct } from './rejectedProductApi'
import { deleteN11ProductByStockCode } from './n11ProductDeleter'
import { findTaskDetailByStockCode } from './taskDetailApi'
import { processSingleRetryUpload, type ProductUploadResult } from './taskProcessor'

// 商品信息接口
export interface ProductInfo {
  productId: string
  title: string
  imageUrl?: string
  price: string
  stockCode: string
}

// 处理过的商品接口
export interface ProcessedProduct extends ProductInfo {
  success: boolean
  message: string
  processTime: number
}

// 详细进度回调类型
export interface DetailedProgressCallback {
  (progress: {
    currentIndex: number
    totalCount: number
    currentProduct: ProductInfo | null
    currentProcessStep: 1 | 2 | 3 | 4
    stepMessage: string
    successCount: number
    failureCount: number
    processHistory: ProcessedProduct[]
    speed: number // 商品/分钟
    estimatedTime: number // 秒
  }): void
}

// 进度回调类型（向后兼容）
export interface ProgressCallback {
  (current: number, total: number, message: string): void
}

// 处理回调类型
export interface ProcessCallbacks {
  onStart?: () => void
  onProgress?: (current: number, total: number, message: string) => void
  onDetailedProgress?: DetailedProgressCallback
  onComplete?: (successCount: number, failureCount: number) => void
  onError?: (error: Error) => void
}

// 处理结果类型
export interface ProcessResult {
  success: boolean
  processedCount: number
  successCount: number
  failureCount: number
  message: string
}

/**
 * 处理被拒绝商品重新上传的主要流程（详细版本）
 * @param callbacks 回调函数集合
 * @returns 处理结果
 */
export async function processRejectedProductReuploadDetailed(
  callbacks?: ProcessCallbacks
): Promise<ProcessResult> {
  let processedCount = 0
  let successCount = 0
  let failureCount = 0
  let isCancelled = false
  const processHistory: ProcessedProduct[] = []
  const startTime = Date.now()

  try {
    if (callbacks?.onStart) {
      callbacks.onStart()
    }

    // 循环处理所有待处理商品
    while (!isCancelled) {
      // 获取下一个待处理商品
      const pendingProduct = await getNextPendingProduct()

      if (!pendingProduct) {
        // 没有更多待处理商品，结束循环
        break
      }

      const productInfo: ProductInfo = {
        productId: pendingProduct.product_id.toString(),
        title: pendingProduct.title,
        imageUrl: undefined, // PendingProduct接口中没有image_url字段
        price: pendingProduct.sales_price?.toString() || '0',
        stockCode: pendingProduct.stock_code
      }

      const productStartTime = Date.now()

      try {
        // 更新详细进度
        if (callbacks?.onDetailedProgress) {
          const elapsedMinutes = (Date.now() - startTime) / 60000
          const speed = processedCount > 0 ? processedCount / elapsedMinutes : 0
          const estimatedTime = speed > 0 ? (1 / speed) * 60 : 0

          callbacks.onDetailedProgress({
            currentIndex: processedCount + 1,
            totalCount: processedCount + 1, // 动态总数，因为我们不知道确切总数
            currentProduct: productInfo,
            currentProcessStep: 1,
            stepMessage: `正在处理商品: ${pendingProduct.title}`,
            successCount,
            failureCount,
            processHistory: [...processHistory],
            speed,
            estimatedTime
          })
        }

        // 处理单个被拒绝商品，传递详细进度回调
        const result = await processSingleRejectedProduct(pendingProduct, {
          ...callbacks,
          onProgress: (current, total, message) => {
            // 更新详细进度中的步骤信息
            if (callbacks?.onDetailedProgress) {
              const elapsedMinutes = (Date.now() - startTime) / 60000
              const speed = processedCount > 0 ? processedCount / elapsedMinutes : 0
              const estimatedTime = speed > 0 ? (1 / speed) * 60 : 0

              callbacks.onDetailedProgress({
                currentIndex: processedCount + 1,
                totalCount: processedCount + 1,
                currentProduct: productInfo,
                currentProcessStep: current as 1 | 2 | 3 | 4,
                stepMessage: message,
                successCount,
                failureCount,
                processHistory: [...processHistory],
                speed,
                estimatedTime
              })
            }

            // 调用原始的进度回调
            if (callbacks?.onProgress) {
              callbacks.onProgress(current, total, message)
            }
          }
        })

        const processTime = Math.floor((Date.now() - productStartTime) / 1000)
        const processedProduct: ProcessedProduct = {
          ...productInfo,
          success: result.success,
          message: result.message,
          processTime
        }

        processHistory.unshift(processedProduct) // 添加到历史记录开头
        if (processHistory.length > 20) { // 只保留最近20条记录
          processHistory.pop()
        }

        if (result.success) {
          successCount++
        } else {
          failureCount++
        }

        processedCount++

        // 最终进度更新
        if (callbacks?.onDetailedProgress) {
          const elapsedMinutes = (Date.now() - startTime) / 60000
          const speed = processedCount / elapsedMinutes

          callbacks.onDetailedProgress({
            currentIndex: processedCount,
            totalCount: processedCount,
            currentProduct: null,
            currentProcessStep: 4,
            stepMessage: result.success ? '商品处理完成' : '商品处理失败',
            successCount,
            failureCount,
            processHistory: [...processHistory],
            speed,
            estimatedTime: 0
          })
        }

        // 向后兼容的进度回调
        if (callbacks?.onProgress) {
          const message = result.success
            ? `商品处理成功: ${pendingProduct.title}`
            : `商品处理失败: ${pendingProduct.title} - ${result.message}`
          callbacks.onProgress(processedCount, processedCount, message)
        }

        // 添加小延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error('处理单个商品失败:', error)
        failureCount++
        processedCount++

        const processTime = Math.floor((Date.now() - productStartTime) / 1000)
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        const processedProduct: ProcessedProduct = {
          ...productInfo,
          success: false,
          message: errorMessage,
          processTime
        }

        processHistory.unshift(processedProduct)
        if (processHistory.length > 20) {
          processHistory.pop()
        }

        if (callbacks?.onProgress) {
          callbacks.onProgress(processedCount, processedCount, `商品处理异常: ${pendingProduct.title} - ${errorMessage}`)
        }

        if (callbacks?.onDetailedProgress) {
          const elapsedMinutes = (Date.now() - startTime) / 60000
          const speed = processedCount / elapsedMinutes

          callbacks.onDetailedProgress({
            currentIndex: processedCount,
            totalCount: processedCount,
            currentProduct: null,
            currentProcessStep: 4,
            stepMessage: '商品处理异常',
            successCount,
            failureCount,
            processHistory: [...processHistory],
            speed,
            estimatedTime: 0
          })
        }
      }
    }

    if (callbacks?.onComplete) {
      callbacks.onComplete(successCount, failureCount)
    }

    return {
      success: failureCount === 0,
      processedCount,
      successCount,
      failureCount,
      message: `处理完成: 成功 ${successCount} 个，失败 ${failureCount} 个`
    }

  } catch (error) {
    console.error('批量处理失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    if (callbacks?.onError) {
      callbacks.onError(error instanceof Error ? error : new Error(errorMessage))
    }

    return {
      success: false,
      processedCount,
      successCount,
      failureCount,
      message: `批量处理失败: ${errorMessage}`
    }
  }
}

/**
 * 处理被拒绝商品重新上传的主要流程（原版本，向后兼容）
 * @param onProgress 进度回调函数
 * @returns 处理结果
 */
export async function processRejectedProductReupload(
  onProgress?: ProgressCallback
): Promise<ProcessResult> {
  let processedCount = 0
  let successCount = 0
  let failureCount = 0
  let isCancelled = false

  try {
    if (onProgress) {
      onProgress(0, 0, '开始处理被拒绝商品重新上传...')
    }

    // 循环处理所有待处理商品
    while (!isCancelled) {
      // 获取下一个待处理商品
      const pendingProduct = await getNextPendingProduct()
      
      if (!pendingProduct) {
        // 没有更多待处理商品，结束循环
        break
      }

      try {
        if (onProgress) {
          onProgress(processedCount, processedCount + 1, `正在处理商品: ${pendingProduct.title}`)
        }

        // 处理单个被拒绝商品
        const result = await processSingleRejectedProduct(pendingProduct)
        
        if (result.success) {
          successCount++
          if (onProgress) {
            onProgress(processedCount + 1, processedCount + 1, `商品处理成功: ${pendingProduct.title}`)
          }
        } else {
          failureCount++
          if (onProgress) {
            onProgress(processedCount + 1, processedCount + 1, `商品处理失败: ${pendingProduct.title} - ${result.message}`)
          }
        }

        processedCount++

        // 添加小延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error('处理单个商品失败:', error)
        failureCount++
        processedCount++
        
        if (onProgress) {
          const errorMessage = error instanceof Error ? error.message : '未知错误'
          onProgress(processedCount, processedCount, `商品处理异常: ${pendingProduct.title} - ${errorMessage}`)
        }
      }
    }

    const message = `处理完成！成功: ${successCount}，失败: ${failureCount}`
    if (onProgress) {
      onProgress(processedCount, processedCount, message)
    }

    return {
      success: true,
      processedCount,
      successCount,
      failureCount,
      message
    }

  } catch (error) {
    console.error('处理被拒绝商品重新上传失败:', error)
    
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    const message = `处理异常: ${errorMessage}`
    if (onProgress) {
      onProgress(processedCount, processedCount, message)
    }

    return {
      success: false,
      processedCount,
      successCount,
      failureCount,
      message
    }
  }
}

/**
 * 处理单个被拒绝商品
 * @param product 待处理商品
 * @param callbacks 处理回调
 * @returns 处理结果
 */
export async function processSingleRejectedProduct(
  product: PendingProduct,
  callbacks?: ProcessCallbacks
): Promise<{ success: boolean; message: string }> {
  try {
    if (callbacks?.onStart) {
      callbacks.onStart()
    }

    // 第一步：删除N11商品
    if (callbacks?.onProgress) {
      callbacks.onProgress(1, 4, '正在通过浏览器操作删除N11商品...')
    }

    const deleteResult = await deleteN11ProductByStockCode(product.stock_code)
    
    if (!deleteResult.success) {
      throw new Error(`删除N11商品失败: ${deleteResult.message}`)
    }

    // 第二步：根据stockCode查找任务详情
    if (callbacks?.onProgress) {
      callbacks.onProgress(2, 4, '正在查找任务详情...')
    }

    const taskDetail = await findTaskDetailByStockCode(product.stock_code)
    
    if (!taskDetail) {
      throw new Error(`未找到stockCode为 ${product.stock_code} 的任务详情`)
    }

    // 第三步和第四步：执行重新上传（包含获取参数和上传）
    if (callbacks?.onProgress) {
      callbacks.onProgress(3, 4, '正在执行重新上传...')
    }

    // 使用正确的重新上传逻辑
    const uploadResult = await processSingleRetryUpload(taskDetail.id, {
      onProductStart: (product: ProductUploadResult) => {
        console.log('开始处理商品:', product.productName)
      },
      onProductComplete: (product: ProductUploadResult) => {
        console.log('商品处理完成:', product.productName, '成功:', product.success)
      },
      onError: (error: string) => {
        console.error('商品处理失败:', error)
      }
    })

    if (!uploadResult.success) {
      throw new Error(uploadResult.message || '重新上传失败')
    }

    // 第五步：更新商品状态为已完成
    await updateProductStatus(product.product_id, 1)

    if (callbacks?.onComplete) {
      callbacks.onComplete(1, 0)
    }

    return {
      success: true,
      message: '商品重新上传成功'
    }

  } catch (error) {
    console.error('处理单个被拒绝商品失败:', error)
    
    if (callbacks?.onError) {
      callbacks.onError(error instanceof Error ? error : new Error(String(error)))
    }

    // 即使处理失败，也要更新状态为已完成，避免重复处理
    try {
      //await updateProductStatus(product.product_id, 1)
    } catch (updateError) {
      console.error('更新商品状态失败:', updateError)
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : '处理失败'
    }
  }
}

/**
 * 取消处理流程
 */
export function cancelProcessing(): void {
  // 这里可以实现取消逻辑
  console.log('取消处理被拒绝商品重新上传')
} 