<template>
  <div class="goods-list-by-directory">
    <!-- 面包屑导航和页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <div class="custom-breadcrumb">
          <el-button type="text" @click="goBackToDirectoryList" class="breadcrumb-back-button">
            <el-icon><ArrowLeft /></el-icon>
            <span>商品目录</span>
          </el-button>
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-current-page">{{ currentDirectoryName }}</span>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="warning" @click="showBatchDialog" :disabled="selectedGoods.length === 0">
          <el-icon><Setting /></el-icon>
          批量设置
        </el-button>
        <!-- <el-button type="success" @click="showMoveDialog" :disabled="selectedGoods.length === 0">
          <el-icon><FolderOpened /></el-icon>
          移动到目录
        </el-button> -->
      </div>
    </div>

    <!-- 固定统计信息卡片 -->
    <div 
      ref="statisticsCardRef"
      class="draggable-statistics-card" 
      :class="{ 'collapsed': isCollapsed }"
      :style="cardPosition"
      @mousedown="startDrag"
    >
      <div class="statistics-header">
        <div class="drag-handle">
          <el-icon><Rank /></el-icon>
        </div>
        <div class="collapse-toggle" @click="toggleCollapse">
          <el-icon v-if="isCollapsed"><DArrowRight /></el-icon>
          <el-icon v-else><DArrowLeft /></el-icon>
        </div>
      </div>
      
      <div class="statistics-container" v-show="!isCollapsed">
        <div v-if="statisticsLoading" class="statistics-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>统计中...</span>
        </div>
        <div v-else-if="statisticsData" class="statistics-content">
          <!-- 目录信息显示 -->
          <div class="directory-info">
            <el-icon><Folder /></el-icon>
            <span class="directory-label">目录：</span>
            <span class="directory-name">{{ currentDirectoryName }}</span>
          </div>
          
          <!-- 统计信息 -->
          <div class="statistics-info">
            <div class="statistics-item">
              <el-icon><Goods /></el-icon>
              <span class="label">商品：</span>
              <span class="value">{{ statisticsData.goods_count }}</span>
            </div>
            <div class="statistics-item">
              <el-icon><SuitcaseLine /></el-icon>
              <span class="label">SKU：</span>
              <span class="value">{{ statisticsData.sku_count }}</span>
            </div>
          </div>
          
          <!-- 快捷操作按钮 -->
          <div class="quick-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleQuickCollectionSettings"
              class="quick-btn"
            >
              <span>采集设置</span>
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              @click="handleQuickCheckImages"
              :loading="checkingImages"
              class="quick-btn"
            >
              <span>检查图片</span>
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="handleQuickPublishGoods"
              class="quick-btn"
            >
              <span>发布商品</span>
            </el-button>
          </div>
        </div>
        <div v-else class="statistics-empty">
          <el-icon><DataAnalysis /></el-icon>
          <span>暂无统计数据</span>
        </div>
      </div>
    </div>

    <!-- 采集设置卡片 -->
    <div class="collection-settings-section">
      <CollectionSettingsCard 
        ref="collectionSettingsCardRef" 
        :auto-load="false" 
        @settings-updated="handleSettingsUpdated" 
      />
    </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="商品ID">
          <el-input v-model="searchForm.goods_id" placeholder="请输入商品ID" clearable />
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.goods_name" placeholder="请输入商品名称" clearable />
        </el-form-item>
        <el-form-item label="状态" class="status-item">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select> 
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" style="margin-right: 20px;">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="info" @click="handleCheckImages" :loading="checkingImages">
            <el-icon><Picture /></el-icon>
            检查商品图片
          </el-button>
          <el-button type="warning" @click="showPublishDialog">
            <el-icon><Upload /></el-icon>
            发布商品 
          </el-button>
        </el-form-item>
      </el-form>
    </div>

            <!-- 数据表格 -->
    <div class="table-section">
      <el-table 
        :data="goodsList" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" :index="getIndex" />
        <el-table-column label="商品分类" min-width="250">
          <template #default="{ row }">
            <div class="category-container">
              <div class="category-name">{{ row.cat_name }}</div>
              <div class="category-path">{{ row.front_cat_2_path_name }}</div>
              
              <!-- 平台关联分类信息 -->
              <div class="platform-relations" v-if="hasValidPlatformCategories(row)">
                <div class="platform-title">可发布分类</div>
                <div 
                  v-for="relation in getPlatformRelations(row)" 
                  :key="relation.id"
                  class="platform-item"
                >
                  <!-- 平台名称标签 - 右上角显示 -->
                  <div class="platform-badge">{{ getPlatformName(relation.third_platform_id) }}</div>
                  
                  <div class="platform-categories"> 
                    <!-- 只显示第一个分类 -->
                    <div 
                      v-if="relation.third_platform_categories.length > 0"
                      class="category-item"
                    >
                      <div class="category-names">
                        <span class="name-cn">{{ relation.third_platform_categories[0].name }}</span>
                        <span class="name-tl">{{ relation.third_platform_categories[0].name_tl }}</span>
                      </div>
                      <div class="category-paths">
                        <span class="path-cn">{{ relation.third_platform_categories[0].path_name }}</span>
                        <span class="path-tl">{{ relation.third_platform_categories[0].path_name_tl }}</span>
                      </div>
                    </div>
                    
                    <!-- 如果超过1个分类，显示更多按钮 -->
                    <el-button 
                      v-if="relation.third_platform_categories.length > 1"
                      type="text" 
                      size="small" 
                      @click="showPlatformCategoriesDialog(relation)"
                      class="more-categories-btn"
                    >
                      更多 ({{ relation.third_platform_categories.length - 1 }})
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品图片" prop="goods_pic" width="120">
          <template #default="{ row }">
            <div class="image-container">
              <el-image
                :src="row.goods_thumb"
                fit="cover"
                style="width: 60px; height: 60px; border-radius: 4px; cursor: pointer;"
                @click="handleImagePreview(row)"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="image-count" v-if="getImageCount(row) > 0">
                <el-icon><Picture /></el-icon>
                <span>{{ getImageCount(row) }}</span>
              </div>
            </div>
          </template>  
        </el-table-column>
        <el-table-column label="商品名称" min-width="250">
          <template #default="{ row }">
            <div class="goods-name-container">
              <div class="goods-name-text" :title="row.goods_name">{{ row.goods_name }}</div>
              <div class="goods-stats">
                <div class="sales-info" v-if="row.goods_sold_quantity !== undefined">
                  <el-icon><ShoppingCart /></el-icon>
                  <span>销量: {{ row.goods_sold_quantity || 0 }}</span>
                </div>
                <div class="rating-info" v-if="row.goods_score !== undefined">
                  <el-icon><Star /></el-icon>
                  <span>评分: {{ row.goods_score || 0 }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="SKU数量" width="100" prop="goods_sku_num"/>
        <el-table-column label="SKU和价格" min-width="280" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="sku-container">
              <template v-if="row.formatted_skus && row.formatted_skus.length > 0">
                <!-- 显示前3个SKU -->
                <div 
                  v-for="(skuItem, index) in row.formatted_skus.slice(0, 3)" 
                  :key="index"
                  class="sku-item"
                >
                  <div class="sku-content">
                    <div class="sku-image" v-if="skuItem.thumb_url">
                      <el-image
                        :src="skuItem.thumb_url"
                        fit="cover"
                        style="width: 30px; height: 30px; border-radius: 4px; cursor: pointer;"
                        @click="handleSkuImagePreview(skuItem)"
                      >
                        <template #error>
                          <div class="sku-image-slot">
                            <el-icon><Picture /></el-icon>
                          </div>
                        </template>
                      </el-image>
                    </div>
                    <div class="sku-info">
                      <div class="sku-text">{{ skuItem.sku }}</div>
                      <div class="sku-price">{{ skuItem.price }} {{ skuItem.currentcy }}</div>
                    </div>
                  </div>
                </div>
                <!-- 如果超过3个，显示更多按钮 -->
                <el-button 
                  v-if="row.formatted_skus.length > 3"
                  type="text" 
                  size="small" 
                  @click="showSkuDialog(row)"
                  class="more-sku-btn"
                >
                  更多 ({{ row.formatted_skus.length - 3 }})
                </el-button>
              </template>
              <span v-else class="no-sku">暂无SKU</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updated_at" width="160" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="userInfo.isAdmin" 
              type="warning" 
              size="small" 
              @click="handleCategoryLink(row)"
              style="margin-right: 8px;"
            >
              关联商品分类
            </el-button>
            <el-button type="primary" size="small" @click="handleViewOriginal(row)">
              查看原商品
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量设置对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量设置"
      width="500px"
    >
      <el-form
        ref="batchFormRef"
        :model="batchForm"
        label-width="100px"
      >
        <el-form-item label="状态">
          <el-select v-model="batchForm.status" placeholder="请选择状态" clearable style="width: 100%">
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <div class="batch-info">
          <p>将对以下 {{ selectedGoods.length }} 个商品进行批量设置：</p>
          <ul class="selected-goods">
            <li v-for="goods in selectedGoods" :key="goods.id">
              {{ goods.goods_name }}
            </li>
          </ul>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchSubmit" :loading="batchSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 移动到目录对话框 -->
    <el-dialog
      v-model="moveDialogVisible"
      title="移动到目录"
      width="500px"
    >
      <el-form
        ref="moveFormRef"
        :model="moveForm"
        :rules="moveFormRules"
        label-width="100px"
      >
        <el-form-item label="目标目录" prop="directory_id">
          <el-select v-model="moveForm.directory_id" placeholder="请选择目标目录" style="width: 100%">
            <el-option label="未分类" :value="0" />
            <el-option 
              v-for="directory in availableDirectories" 
              :key="directory.id"
              :label="directory.name"
              :value="directory.id"
            />
          </el-select>
        </el-form-item>
        <div class="batch-info">
          <p>将移动以下 {{ selectedGoods.length }} 个商品：</p>
          <ul class="selected-goods">
            <li v-for="goods in selectedGoods" :key="goods.id">
              {{ goods.goods_name }}
            </li>
          </ul>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleMoveSubmit" :loading="moveSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- SKU详情对话框 -->
    <el-dialog
      v-model="skuDialogVisible"
      title="SKU详情"
      width="700px"
    >
      <div class="sku-detail-container">
        <el-table :data="currentSkuList" stripe>
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column label="缩略图" width="80">
            <template #default="{ row }">
              <div class="sku-detail-image" v-if="row.thumb_url">
                <el-image
                  :src="row.thumb_url"
                  fit="cover"
                  style="width: 50px; height: 50px; border-radius: 4px; cursor: pointer;"
                  @click="handleSkuImagePreview(row)"
                >
                  <template #error>
                    <div class="sku-image-slot">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <span v-else class="no-image">无图片</span>
            </template>
          </el-table-column>
          <el-table-column label="SKU" prop="sku" min-width="200" show-overflow-tooltip />
          <el-table-column label="价格" width="120">
            <template #default="{ row }">
              <span class="price-text">{{ row.price }} {{ row.currentcy }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="skuDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      :title="`${currentGoodsName} - 商品图片`"
      width="90%"
      top="3vh"
      class="image-preview-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <div class="image-preview-container" v-if="currentImageList.length > 0">
        <!-- 主图片显示区域 -->
        <div class="main-image-container">
          <el-image
            :src="currentImageList[currentImageIndex]"
            fit="contain"
            class="main-image"
            :lazy="true"
            loading="eager"
          >
            <template #placeholder>
              <div class="image-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>图片加载中...</span>
              </div>
            </template>
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
          
          <!-- 图片计数器 -->
          <div class="image-counter">
            {{ currentImageIndex + 1 }} / {{ currentImageList.length }}
          </div>
          
          <!-- 左右切换按钮 -->
          <div class="nav-buttons" v-if="currentImageList.length > 1">
            <el-button 
              class="nav-btn prev-btn" 
              @click="prevImage"
              circle
              size="large"
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <el-button 
              class="nav-btn next-btn" 
              @click="nextImage"
              circle
              size="large"
            >
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
        
        <!-- 缩略图导航 -->
        <div class="thumbnail-nav" v-if="currentImageList.length > 1">
          <div 
            v-for="(image, index) in currentImageList" 
            :key="index"
            class="thumbnail-item"
            :class="{ active: index === currentImageIndex }"
            @click="goToImage(index)"
          >
            <el-image
              :src="image"
              fit="cover"
              class="thumbnail-image"
            >
              <template #error>
                <div class="thumbnail-error">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="imagePreviewVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 平台分类详情对话框 -->
    <el-dialog
      v-model="platformCategoriesDialogVisible"
      title="平台分类详情"
      width="700px"
    >
      <div class="platform-categories-detail" v-if="currentPlatformRelation">
        <div class="platform-info">
          <h3>{{ getPlatformName(currentPlatformRelation.third_platform_id) }} 平台分类</h3>
        </div>
        <el-table :data="currentPlatformRelation.third_platform_categories" stripe>
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column label="分类名称" min-width="150">
            <template #default="{ row }">
              <div class="category-names-detail">
                <div class="name-cn">{{ row.name }}</div>
                <div class="name-tl">{{ row.name_tl }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="分类路径" min-width="200">
            <template #default="{ row }">
              <div class="category-paths-detail">
                <div class="path-cn">{{ row.path_name }}</div>
                <div class="path-tl">{{ row.path_name_tl }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="platformCategoriesDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 商品发布设置对话框 -->
    <el-dialog
      v-model="publishDialogVisible"
      title="商品发布设置"
      width="850px"
      :close-on-click-modal="false"
    >
      <GoodsPublishSettings
        v-if="publishDialogVisible"
        :selected-goods="selectedGoods"
        :current-directory-name="currentDirectoryName"
        :directory-id="currentDirectoryId"
        @close="publishDialogVisible = false"
        @success="handlePublishSuccess"
      />
    </el-dialog>

    <!-- SKU图片预览对话框 -->
    <el-dialog
      v-model="skuImagePreviewVisible"
      :title="`${currentSkuName} - SKU图片`"
      width="60%"
      top="10vh"
      class="sku-image-preview-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <div class="sku-image-preview-container">
        <el-image
          :src="currentSkuImageUrl"
          fit="contain"
          class="sku-preview-image"
          :lazy="true"
          loading="eager"
        >
          <template #placeholder>
            <div class="image-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>图片加载中...</span>
            </div>
          </template>
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </template>
        </el-image>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="skuImagePreviewVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 关联商品分类对话框 -->
    <el-dialog
      v-model="categoryLinkDialogVisible"
      title="关联商品分类"
      width="80%"
      :close-on-click-modal="false"
      class="category-link-dialog"
    >
      <div v-if="currentGoodsForCategoryLink" class="goods-info-section">
        <h3>当前商品信息</h3>
        <div class="goods-info">
          <div class="goods-basic">
            <el-image
              :src="currentGoodsForCategoryLink.goods_thumb"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px; margin-right: 15px;"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="goods-details">
              <div class="goods-name">{{ currentGoodsForCategoryLink.goods_name }}</div>
              <div class="goods-meta">
                <span class="goods-id">商品ID: {{ currentGoodsForCategoryLink.goods_id }}</span>
                <span class="category-path">分类: {{ currentGoodsForCategoryLink.front_cat_2_path_name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <ThirdPartyLinkage
        v-if="categoryLinkDialogVisible && currentTemuCategory"
        :temu-category="currentTemuCategory"
        :platforms="availablePlatforms"
        @close="categoryLinkDialogVisible = false"
        @success="handleCategoryLinkSuccess"
      />
    </el-dialog>

    <!-- 采集设置对话框 -->
    <CollectionSettingsDialog
      v-model="showSettingsDialog"
      :available-directories="settingsDialogDirectories"
      :loading="settingsDialogLoading"
      :initial-data="settingsDialogInitialData"
      @confirm="handleSettingsConfirm"
      @create-directory="handleCreateDirectory"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { ArrowLeft, Setting, Search, Refresh, Picture, FolderOpened, ArrowRight, Loading, Upload, Goods, ShoppingCart, Star, SuitcaseLine, DataAnalysis, Rank, DArrowRight, DArrowLeft, Folder } from '@element-plus/icons-vue' // 添加 Goods, ShoppingCart, Star, SuitcaseLine, DataAnalysis, Rank, DArrowRight, DArrowLeft, Folder 图标
import { useRouter, useRoute } from 'vue-router'
import {
  getGoodsList,
  batchUpdateGoods,
  type Goods as GoodsType,
  type GoodsListParams 
} from '../utils/goodsApi'
import { getDirectoryList, type Directory } from '../utils/directoryApi'
import { getCategoryDetail } from '../utils/categoryApi'
import { userInfo } from '../utils/userStore'
import GoodsPublishSettings from './GoodsPublishSettings.vue'
import ThirdPartyLinkage from './ThirdPartyLinkage.vue'
import CollectionSettingsCard from './CollectionSettingsCard.vue'
import CollectionSettingsDialog from './CollectionSettingsDialog.vue'
import { getNeedImageProcessGoods, processGoodsImages } from '../utils/imageProcessApi'
import { getGoodsStatistics, type GoodsStatisticsParams, type GoodsStatisticsResponse } from '../utils/goodsStatisticsApi'
import { 
  getUserCollectionSettings, 
  saveUserCollectionSettings, 
  getUserAvailableDirectories,
  type Directory as SettingsDirectory
} from '../utils/collectionSettingsApi'

// 接口定义
interface Goods extends GoodsType {
  id: number
  platform_relations?: Array<{
    id: number
    platform_id: number
    third_platform_id: number
    cat_third_ids: number[]
    third_platform_categories: Array<{
      id: number
      name: string
      name_tl: string
      path_name: string
      path_name_tl: string
    }>
  }>
}

interface SearchForm {
  goods_name: string
  goods_id: number | null | undefined
  status: number | null | undefined
}

interface BatchForm {
  status: number | null | undefined
}

interface MoveForm {
  directory_id: number | null | undefined
}

// 响应式数据
const loading = ref(false)
const batchSubmitting = ref(false)
const moveSubmitting = ref(false)
const goodsList = ref<Goods[]>([])
const selectedGoods = ref<Goods[]>([])
const availableDirectories = ref<Directory[]>([])

// 采集设置相关数据
const collectionSettingsCardRef = ref()
const showSettingsDialog = ref(false)
const settingsDialogDirectories = ref<SettingsDirectory[]>([])
const settingsDialogLoading = ref(false)
const settingsDialogInitialData = ref<any>(null)

// 统计相关数据
const statisticsLoading = ref(false)
const statisticsData = ref<GoodsStatisticsResponse | null>(null)
let statisticsTimer: ReturnType<typeof setTimeout> | null = null

// 统计卡片拖拽和折叠相关数据
const statisticsCardRef = ref<HTMLElement>()
const isCollapsed = ref(false)
const isDragging = ref(false)
const cardPosition = ref<{
  position: 'fixed'
  top?: string
  right?: string
  left?: string
  bottom?: string
  zIndex: number
}>({
  position: 'fixed',
  bottom: '20px',  // 改为底部定位
  left: '20px',    // 改为左侧定位
  zIndex: 1000
})

// 拖拽相关数据
let dragStart = { x: 0, y: 0 }
let cardStart = { x: 0, y: 0 }

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  // 保存折叠状态到本地存储
  localStorage.setItem('statisticsCardCollapsed', isCollapsed.value.toString())
}

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  if ((e.target as HTMLElement).closest('.collapse-toggle')) {
    return // 如果点击的是折叠按钮，不启动拖拽
  }
  
  isDragging.value = true
  dragStart.x = e.clientX
  dragStart.y = e.clientY
  
  // 获取当前卡片位置
  const rect = statisticsCardRef.value?.getBoundingClientRect()
  if (rect) {
    cardStart.x = rect.left
    cardStart.y = rect.top
  }
  
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  e.preventDefault()
}

// 拖拽过程中
const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return
  
  const deltaX = e.clientX - dragStart.x
  const deltaY = e.clientY - dragStart.y
  
  const newLeft = cardStart.x + deltaX
  const newTop = cardStart.y + deltaY
  
  // 边界检查
  const maxX = window.innerWidth - 240 // 卡片宽度240px
  const maxY = window.innerHeight - 200 // 卡片高度约200px
  
  cardPosition.value = {
    position: 'fixed' as const,
    left: Math.max(0, Math.min(newLeft, maxX)) + 'px',
    top: Math.max(0, Math.min(newTop, maxY)) + 'px',
    right: 'auto',
    bottom: 'auto',
    zIndex: 1000
  }
}

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  
  // 保存位置到本地存储
  localStorage.setItem('statisticsCardPosition', JSON.stringify(cardPosition.value))
}

// 初始化卡片位置和状态
const initializeCardState = () => {
  // 恢复折叠状态
  const savedCollapsed = localStorage.getItem('statisticsCardCollapsed')
  if (savedCollapsed !== null) {
    isCollapsed.value = savedCollapsed === 'true'
  }
  
  // 恢复位置
  const savedPosition = localStorage.getItem('statisticsCardPosition')
  if (savedPosition) {
    try {
      const position = JSON.parse(savedPosition)
      cardPosition.value = { ...cardPosition.value, ...position }
    } catch (error) {
      console.warn('Failed to parse saved card position:', error)
    }
  }
}

const router = useRouter()
const route = useRoute()

// SKU相关数据
const currentSkuList = ref<any[]>([])
const skuDialogVisible = ref(false)

// 图片预览相关数据
const currentImageList = ref<string[]>([])
const currentImageIndex = ref(0)
const currentGoodsName = ref('')
const imagePreviewVisible = ref(false)

// SKU图片预览相关数据
const skuImagePreviewVisible = ref(false)
const currentSkuImageUrl = ref('')
const currentSkuName = ref('')

// 平台分类相关数据
const currentPlatformRelation = ref<any>(null)
const platformCategoriesDialogVisible = ref(false)

// 分类关联相关数据
const currentGoodsForCategoryLink = ref<Goods | null>(null)
const currentTemuCategory = ref<any>(null)
const categoryLinkDialogVisible = ref(false)

// 发布商品相关数据
const publishDialogVisible = ref(false)

// 图片处理相关数据
const checkingImages = ref(false)
const processingImages = ref(false)
let currentImageProcessNotification: any = null

// 可用平台列表
const availablePlatforms = ref([
  {
    id: 1,
    name: 'N11',
    code: 'n11',
    apiUrl: 'apiCatN11ListUrl'
  }
])

// 当前目录信息
const currentDirectoryId = ref<number>(0)
const currentDirectoryName = ref<string>('')

// 搜索表单
const searchForm = reactive<SearchForm>({
  goods_name: '',
  goods_id: null,
  status: null
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 对话框状态
const batchDialogVisible = ref(false)
const moveDialogVisible = ref(false)

// 表单数据
const batchForm = reactive<BatchForm>({
  status: undefined
})

const moveForm = reactive<MoveForm>({
  directory_id: undefined
})

const batchFormRef = ref()
const moveFormRef = ref()

// 表单验证规则
const moveFormRules = {
  directory_id: [
    { required: true, message: '请选择目标目录', trigger: 'change' }
  ]
}

// 获取序号
const getIndex = (index: number) => {
  return (pagination.currentPage - 1) * pagination.pageSize + index + 1
}

// 获取状态名称
const getStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '正常',
    0: '禁用'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'danger'
}

// 获取图片数量
const getImageCount = (row: Goods) => {
  if (!row.goods_pic) return 0
  if (Array.isArray(row.goods_pic)) {
    return row.goods_pic.length
  }
  return 0
}

// 返回目录列表
const goBackToDirectoryList = () => {
  router.push({ name: 'GoodsDirectoryList' })
}

// 加载可用目录列表
const loadAvailableDirectories = async () => {
  try {
    const response = await getDirectoryList({ page: 1, pageSize: 100, status: 1 })
    availableDirectories.value = response.list
  } catch (error) {
    console.error('获取目录列表失败:', error)
  }
}

// 加载商品列表
const loadGoodsList = async () => {
  loading.value = true
  try {
    const params: GoodsListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      goods_name: searchForm.goods_name || undefined,
      goods_id: searchForm.goods_id || undefined,
      status: (searchForm.status === null || searchForm.status === undefined) ? undefined : searchForm.status,
      directory_id: currentDirectoryId.value
    }
    
    const response = await getGoodsList(params)
    goodsList.value = response.list as Goods[]
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.log('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadGoodsList()
  loadGoodsStatistics() // 搜索后更新统计
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    goods_name: '',
    goods_id: null,
    status: null
  })
  pagination.currentPage = 1
  loadGoodsList()
  loadGoodsStatistics() // 重置后更新统计
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadGoodsList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadGoodsList()
}

// 选择变化
const handleSelectionChange = (selection: Goods[]) => {
  selectedGoods.value = selection
}

// 显示批量设置对话框
const showBatchDialog = () => {
  batchForm.status = undefined
  batchDialogVisible.value = true
}

// 显示移动对话框
const showMoveDialog = () => {
  moveForm.directory_id = undefined
  moveDialogVisible.value = true
}

// 查看原商品
const handleViewOriginal = async (row: Goods) => {
  if (!row.source_url) {
    ElMessage.warning('该商品没有原始链接')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '如果您设置了自动采集商品并且已经选择了目录，浏览该商品将会自动采集入库。您可以先设置为不采集商品再浏览商品详情。您确定要浏览该商品吗？',
      '浏览商品确认',
      {
        confirmButtonText: '确定浏览',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )
    
    window.open(row.source_url, '_blank')
  } catch (error) {
    // 用户取消了操作
    console.log('用户取消了浏览商品')
  }
}

// 批量设置提交
const handleBatchSubmit = async () => {
  try {
    if (batchForm.status === undefined || batchForm.status === null) {
      ElMessage.warning('请选择要设置的状态')
      return
    }

    batchSubmitting.value = true

    const ids = selectedGoods.value.map(goods => goods.id)
    const updateData: any = {
      ids,
      status: batchForm.status
    }

    await batchUpdateGoods(updateData)

    const statusText = batchForm.status === 1 ? '正常' : '禁用'
    const message = `已成功设置 ${selectedGoods.value.length} 个商品的状态为${statusText}。`

    ElNotification({
      title: '批量设置成功',
      message,
      type: 'success'
    })

    batchDialogVisible.value = false
    selectedGoods.value = []
    loadGoodsList()
  } catch (error) {
    console.error('批量设置失败:', error)
    ElMessage.error('批量设置失败')
  } finally {
    batchSubmitting.value = false
  }
}

// 移动商品提交
const handleMoveSubmit = async () => {
  try {
    await moveFormRef.value.validate()

    moveSubmitting.value = true

    const ids = selectedGoods.value.map(goods => goods.id)
    const updateData: any = {
      ids,
      directory_id: moveForm.directory_id
    }

    await batchUpdateGoods(updateData)

    const targetDirectoryName = moveForm.directory_id === 0 
      ? '未分类' 
      : availableDirectories.value.find(d => d.id === moveForm.directory_id)?.name || '未知目录'
    
    const message = `已成功移动 ${selectedGoods.value.length} 个商品到"${targetDirectoryName}"。`

    ElNotification({
      title: '移动成功',
      message,
      type: 'success'
    })

    moveDialogVisible.value = false
    selectedGoods.value = []
    loadGoodsList()
  } catch (error) {
    console.error('移动商品失败:', error)
    ElMessage.error('移动商品失败')
  } finally {
    moveSubmitting.value = false
  }
}

// 获取商品的平台关联信息
const getPlatformRelations = (row: Goods) => {
  if (!row.platform_relations || !Array.isArray(row.platform_relations)) {
    return []
  }
  // 只获取 third_platform_id == 2 的数据（N11平台）
  return row.platform_relations.filter((relation: any) => relation.third_platform_id === 2)
}

// 检查是否有有效的平台关联分类
const hasValidPlatformCategories = (row: Goods) => {
  const relations = getPlatformRelations(row);
  return relations.some((relation: any) => relation.third_platform_categories && relation.third_platform_categories.length > 0);
}

// 获取平台名称
const getPlatformName = (platformId: number) => {
  const platformMap: Record<number, string> = {
    2: 'N11'
  }
  return platformMap[platformId] || '未知平台'
}

// 显示平台分类详情对话框
const showPlatformCategoriesDialog = (relation: any) => {
  currentPlatformRelation.value = relation
  platformCategoriesDialogVisible.value = true
}

// 处理图片预览点击
const handleImagePreview = (row: Goods) => {
  if (row.goods_pic && row.goods_pic.length > 0) {
    currentImageList.value = row.goods_pic
    currentImageIndex.value = 0
    currentGoodsName.value = row.goods_name
    imagePreviewVisible.value = true
  }
}

// 切换到上一张图片
const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  } else {
    currentImageIndex.value = currentImageList.value.length - 1
  }
}

// 切换到下一张图片
const nextImage = () => {
  if (currentImageIndex.value < currentImageList.value.length - 1) {
    currentImageIndex.value++
  } else {
    currentImageIndex.value = 0
  }
}

// 跳转到指定图片
const goToImage = (index: number) => {
  currentImageIndex.value = index
}

// 显示SKU详情对话框
const showSkuDialog = (row: Goods) => {
  currentSkuList.value = row.formatted_skus || []
  skuDialogVisible.value = true
}

// 处理SKU图片预览
const handleSkuImagePreview = (skuItem: any) => {
  if (skuItem.thumb_url_h500 || skuItem.thumb_url) {
    currentSkuImageUrl.value = skuItem.thumb_url_h500 || skuItem.thumb_url
    currentSkuName.value = skuItem.sku || 'SKU图片'
    skuImagePreviewVisible.value = true
  }
}

// 处理关联商品分类
const handleCategoryLink = async (row: Goods) => {
  try {
    // 根据商品的cat_id获取Temu分类信息
    const categoryInfo = await getCategoryDetail(row.cat_id)
    if (categoryInfo) {
      currentGoodsForCategoryLink.value = row
      currentTemuCategory.value = {
        id: categoryInfo.id,
        name: categoryInfo.name,
        path_name: categoryInfo.path_name,
        level: categoryInfo.level,
        is_leaf: categoryInfo.is_leaf
      }
      categoryLinkDialogVisible.value = true
    } else {
      ElMessage.warning('无法获取商品分类信息')
    }
  } catch (error) {
    console.error('获取商品分类信息失败:', error)
    ElMessage.error('获取商品分类信息失败')
  }
}

// 处理关联商品分类成功后的回调
const handleCategoryLinkSuccess = () => {
  categoryLinkDialogVisible.value = false
  currentGoodsForCategoryLink.value = null
  currentTemuCategory.value = null
  ElMessage.success('商品分类关联成功')
  // 刷新商品列表以显示最新的关联分类信息
  loadGoodsList()
}

// 检查商品图片
const handleCheckImages = async () => {
  try {
    checkingImages.value = true
    
    // 获取需要处理图片的商品ID列表
    const response = await getNeedImageProcessGoods(currentDirectoryId.value)
    console.log('获取需要处理图片的商品列表响应:', response)
    
    // 根据实际返回的数据结构获取商品ID列表
    let goodsIds = []
    if (response && response.goods_ids) {
      goodsIds = response.goods_ids
    }
    
    if (goodsIds.length === 0) {
      ElMessage.success('没有商品图片需要处理')
      return
    }
    
    // 开始处理图片
    await startBatchImageProcessing(goodsIds)
    
  } catch (error) {
    console.error('检查商品图片失败:', error)
    ElMessage.error(`检查商品图片失败: ${error.message || error}`)
  } finally {
    checkingImages.value = false
  }
}

// 批量处理商品图片
const startBatchImageProcessing = async (goodsIds: number[]) => {
  processingImages.value = true
  let processedCount = 0
  console.log('开始批量处理商品图片------------:', goodsIds)
  try {
    for (let i = 0; i < goodsIds.length; i++) {
      const goodsId = goodsIds[i]
      const currentIndex = i + 1
      const remainingCount = goodsIds.length - currentIndex
      
      // 关闭之前的通知
      if (currentImageProcessNotification) {
        currentImageProcessNotification.close()
        currentImageProcessNotification = null
      }
      
      // 显示当前处理的商品信息
      currentImageProcessNotification = ElNotification({
        title: '批量处理商品图片',
        message: `共${goodsIds.length}个商品，当前处理第${currentIndex}个，商品ID：${goodsId}，剩余${remainingCount}个`,
        type: 'info',
        duration: 0
      })
      
      // 处理单个商品的图片
      await processSingleGoodsImages(goodsId, currentIndex, goodsIds.length)
      processedCount++
    }
    
    // 处理完成
    if (currentImageProcessNotification) {
      currentImageProcessNotification.close()
      currentImageProcessNotification = null
    }
    
    ElNotification({
      title: '图片处理完成',
      message: `已成功处理${processedCount}个商品的图片`,
      type: 'success',
      duration: 0
    })
    
    // 刷新商品列表
    loadGoodsList()
    
  } catch (error) {
    console.error('批量处理图片失败------------:', error)
    if (currentImageProcessNotification) {
      currentImageProcessNotification.close()
      currentImageProcessNotification = null
    }
    ElMessage.error('批量处理图片失败')
  } finally {
    processingImages.value = false
  }
}

// 处理单个商品的图片
const processSingleGoodsImages = async (goodsId: number, currentIndex: number, totalCount: number) => {
  try {
    // 第一次请求获取处理信息
    const initialResult = await processGoodsImages(goodsId, 1)
    console.log(`商品${goodsId}初始处理结果:`, initialResult)
    
    // 根据实际返回的数据结构获取处理信息
    let processInfo = null
    if (initialResult && initialResult.directory_name !== undefined) {
      // 直接返回的业务数据
      processInfo = initialResult
    } 
    
    if (!processInfo) {
      console.error(`商品${goodsId}处理信息获取失败，返回数据:`, initialResult)
      throw new Error('获取图片处理信息失败：返回数据格式不正确')
    }

    let currentStep = 2
    let isCompleted = processInfo.img_local_status === 1

    // 如果已经处理完成，直接返回
    if (isCompleted) {
      console.log(`商品${goodsId}图片已处理完成`)
      return
    }

    // 更新通知显示处理信息
    if (currentImageProcessNotification) {
      currentImageProcessNotification.close()
    }
    
    const mediaInfo = []
    if (processInfo.total_images > 0) {
      mediaInfo.push(`${processInfo.total_images}张图片`)
    }
    if (processInfo.total_videos > 0) {
      mediaInfo.push(`${processInfo.total_videos}个视频`)
    }
    
    const mediaText = mediaInfo.length > 0 ? `，包含${mediaInfo.join('和')}` : ''
    
    currentImageProcessNotification = ElNotification({
      title: '批量处理商品图片',
      message: `共${totalCount}个商品，当前处理第${currentIndex}个，商品ID：${goodsId}${mediaText}`,
      type: 'info',
      duration: 0
    })

    // 循环处理直到完成
    const maxSteps = processInfo.estimated_steps || 10
    let processedSteps = 0

    while (!isCompleted && processedSteps < maxSteps + 5) {
      try {
        const result = await processGoodsImages(goodsId, currentStep)
        console.log(`商品${goodsId}步骤${currentStep}处理结果:`, result)
        
        // 根据实际返回的数据结构获取处理结果
        let data = null
        if (result && result.directory_name !== undefined) {
          // 直接返回的业务数据
          data = result
        } 
        
        if (!data) {
          console.error(`商品${goodsId}步骤${currentStep}处理失败，返回数据:`, result)
          throw new Error('图片处理请求失败：返回数据格式不正确')
        }

        isCompleted = data.img_local_status === 1
        
        // 更新进度通知
        if (!isCompleted) {
          const totalMedia = (data.total_images || 0) + (data.total_videos || 0)
          const processedMedia = (data.processed_images || 0) + (data.processed_videos || 0)
          const progress = totalMedia > 0 ? Math.min(100, Math.round(processedMedia / totalMedia * 100)) : 0
          
          if (currentImageProcessNotification) {
            currentImageProcessNotification.close()
          }
          
          currentImageProcessNotification = ElNotification({
            title: '批量处理商品图片',
            message: `共${totalCount}个商品，当前处理第${currentIndex}个，商品ID：${goodsId}，进度：${progress}% (${data.processed_images || 0}/${data.total_images || 0}张图片, ${data.processed_videos || 0}/${data.total_videos || 0}个视频)`,
            type: 'info',
            duration: 0
          })
        }

        currentStep++
        processedSteps++

        if (isCompleted) {
          console.log(`商品${goodsId}图片处理完成`)
          break
        }

        // 短暂延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (stepError) {
        console.error(`商品${goodsId}图片处理步骤 ${currentStep} 失败:`, stepError)
        currentStep++
        processedSteps++
        
        if (processedSteps >= maxSteps + 3) {
          console.warn(`商品${goodsId}处理步骤超过最大限制，停止处理`)
          break
        }
      }
    }

  } catch (error) {
    console.error(`处理商品${goodsId}图片失败:`, error)
    throw error
  }
}

// 显示发布设置对话框（修改为先检查图片）
const showPublishDialog = async () => {
  try {
    // 先检查是否有需要处理的图片
    const response = await getNeedImageProcessGoods(currentDirectoryId.value)
    console.log('发布前检查图片响应:', response)
    
    // 根据实际返回的数据结构获取商品ID列表
    let goodsIds = []
    if (response && response.goods_ids) {
      goodsIds = response.goods_ids
    } else if (response && response.data && response.data.goods_ids) {
      goodsIds = response.data.goods_ids
    }
    
    if (goodsIds.length > 0) {
      // 强制用户先处理图片
      await ElMessageBox.alert(
        `检测到当前目录下有${goodsIds.length}个商品的图片未处理完成，需要先处理图片才能发布商品。`,
        '图片处理提醒',
        {
          confirmButtonText: '确定处理图片',
          type: 'warning',
          showCancelButton: false // 明确不显示取消按钮
        }
      );
      
      // 用户已点击"确定处理图片"，开始处理
      await startBatchImageProcessing(goodsIds)
      return // 强制返回，用户需等待处理完成后再次点击发布
    }
    
    // 没有需要处理的图片或用户选择跳过（此路径在强制处理后将不再通过），直接显示发布对话框
    publishDialogVisible.value = true
    
  } catch (error) {
    console.log('检查图片状态失败:', error)
    return false;
    /* console.error('检查图片状态失败:', error)
    ElMessage.warning(`检查图片状态失败: ${error.message || error}，将直接进入发布流程`)
    // 检查失败，直接显示发布对话框
    publishDialogVisible.value = true */
  }
}

// 发布成功回调
const handlePublishSuccess = () => {
  publishDialogVisible.value = false
  // 使用 router.push 进行路由跳转
  setTimeout(() => {
    router.push('/tasks')
  }, 1000)
}

// 处理采集设置更新
const handleSettingsUpdated = (settings: any) => {
  console.log('采集设置已更新:', settings)
}

// 快捷操作方法
const handleQuickCollectionSettings = async () => {
  try {
    // 直接显示采集设置对话框
    settingsDialogLoading.value = true
    
    // 加载目录数据
    const response = await getUserAvailableDirectories()
    settingsDialogDirectories.value = response.list || []
    
    // 加载当前设置
    const settings = await getUserCollectionSettings()
    settingsDialogInitialData.value = settings
    
    // 显示对话框
    showSettingsDialog.value = true
  } catch (error) {
    console.error('加载采集设置失败:', error)
    ElMessage.warning('加载采集设置失败，将滚动到设置区域')
    
    // 备用方案：滚动到采集设置区域
    const collectionSettingsSection = document.querySelector('.collection-settings-section')
    if (collectionSettingsSection) {
      collectionSettingsSection.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      })
      // 添加高亮效果
      collectionSettingsSection.classList.add('highlight-flash')
      setTimeout(() => {
        collectionSettingsSection.classList.remove('highlight-flash')
      }, 2000)
    }
  } finally {
    settingsDialogLoading.value = false
  }
}

const handleQuickCheckImages = () => {
  // 直接调用原有的检查图片方法
  handleCheckImages()
}

const handleQuickPublishGoods = () => {
  // 直接调用原有的发布商品方法
  showPublishDialog()
}

// 加载商品统计数据
const loadGoodsStatistics = async () => {
  // 清除之前的定时器
  if (statisticsTimer) {
    clearTimeout(statisticsTimer)
  }

  // 防抖处理，避免频繁请求
  statisticsTimer = setTimeout(async () => {
    try {
      statisticsLoading.value = true
      statisticsData.value = null

      // 构建统计参数 - 按全部获取该目录下的总数据
      const params: GoodsStatisticsParams = {
        directory_id: currentDirectoryId.value,
        time_range: 'all'
      }

      const response = await getGoodsStatistics(params)
      statisticsData.value = response
    } catch (error) {
      console.error('获取商品统计失败:', error)
      statisticsData.value = null
    } finally {
      statisticsLoading.value = false
    }
  }, 300) // 300ms 防抖
}

// 处理采集设置确认
const handleSettingsConfirm = async (data: any) => {
  try {
    settingsDialogLoading.value = true
    const result = await saveUserCollectionSettings(data)
    
    ElMessage.success('设置保存成功')
    showSettingsDialog.value = false
    
    // 重新加载设置
    const settings = await getUserCollectionSettings()
    handleSettingsUpdated(settings)
    if (collectionSettingsCardRef.value && typeof collectionSettingsCardRef.value.loadCollectionSettings === 'function') {
      collectionSettingsCardRef.value.loadCollectionSettings()
    }
  } catch (error) {
    console.error('保存采集设置失败:', error)
    ElMessage.error('保存采集设置失败')
  } finally {
    settingsDialogLoading.value = false
  }
}

// 处理创建目录
const handleCreateDirectory = () => {
  // 这里可以添加创建目录的逻辑，或者使用已有的目录创建对话框
  ElMessage.info('请使用目录管理功能创建新目录')
}

// 组件挂载时初始化
onMounted(() => {
  // 获取路由参数
  currentDirectoryId.value = parseInt(route.params.directoryId as string) || 0
  currentDirectoryName.value = (route.query.directoryName as string) || '未知目录'
  
  // 初始化卡片状态
  initializeCardState()
  
  // 加载数据
  loadAvailableDirectories()
  loadGoodsList()
  loadGoodsStatistics()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (statisticsTimer) {
    clearTimeout(statisticsTimer)
    statisticsTimer = null
  }
})
</script>

<style scoped>
.category-container {
  padding: 8px 0;
}
.category-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}
.category-path {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  word-break: break-all;
  white-space: normal;
}

.goods-list-by-directory {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
}

.custom-breadcrumb {
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-right: 20px; /* 与右侧信息隔开一些距离 */
}

.breadcrumb-back-button {
  font-size: 18px; /* 统一返回按钮字体大小 */
  color: #409EFF; /* Element Plus 主题蓝色 */
  padding: 0; /* 移除默认padding */
  margin-right: 8px;
}

.breadcrumb-back-button .el-icon {
  margin-right: 4px;
}

.breadcrumb-back-button:hover,
.breadcrumb-back-button:focus {
  color: #66b1ff; /* 悬停颜色变浅 */
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #C0C4CC; /* 分隔符颜色 */
}

.breadcrumb-current-page {
  font-weight: 600; /* 当前页面名称加粗 */
  color: #303133; /* 当前页面文字颜色 */
  line-height: 1.2; /* 调整行高，减少占据的高度 */
  max-height: 24px; /* 限制最大高度 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超长文本显示省略号 */
  white-space: nowrap; /* 不换行 */
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 可拖拽统计信息卡片样式 */
.draggable-statistics-card {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  width: 240px;
  backdrop-filter: blur(10px);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  cursor: move;
  user-select: none;
}

.draggable-statistics-card:hover {
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.draggable-statistics-card.collapsed {
  width: 60px;
}

.draggable-statistics-card.collapsed .statistics-header {
  border-radius: 12px;
}

/* 统计卡片头部 */
.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px 12px 0 0;
}

.drag-handle {
  display: flex;
  align-items: center;
  color: #94a3b8;
  font-size: 14px;
  cursor: move;
}

.drag-handle .el-icon {
  font-size: 16px;
}

.collapse-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.collapse-toggle:hover {
  background: #66b1ff;
  transform: scale(1.1);
}

.collapse-toggle .el-icon {
  font-size: 12px;
}

.statistics-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px 20px;
}

/* 统计内容区域 */
.statistics-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 目录信息显示 */
.directory-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border: 1px solid #bae6fd;
  margin-bottom: 4px;
}

.directory-info .el-icon {
  font-size: 16px;
  color: #0ea5e9;
  flex-shrink: 0;
}

.directory-label {
  color: #0369a1;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.directory-name {
  color: #0c4a6e;
  font-size: 12px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* 快捷操作按钮区域 */
.quick-actions {
  display: flex;
  flex-direction: row;
  gap: 4px;
  margin-top: 8px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.quick-btn {
  flex: 1;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 11px;
  transition: all 0.3s ease;
  padding: 0 4px;
}

.quick-btn .el-icon {
  font-size: 14px;
}

.quick-btn span {
  font-weight: 500;
}

/* 高亮闪烁效果 */
@keyframes highlight-flash {
  0% { 
    background-color: transparent;
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  50% { 
    background-color: rgba(64, 158, 255, 0.1);
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0.2);
  }
  100% { 
    background-color: transparent;
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.highlight-flash {
  animation: highlight-flash 1s ease-in-out 2;
  border-radius: 8px;
}

.statistics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
  padding: 8px 0;
}

.statistics-loading .el-icon {
  font-size: 16px;
}

.statistics-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.statistics-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border-left: 4px solid #409eff;
  transition: all 0.3s ease;
}

.statistics-item:hover {
  background: linear-gradient(135deg, #ecf5ff 0%, #d9ecff 100%);
  transform: translateX(4px);
}

.statistics-item .el-icon {
  font-size: 18px;
  color: #409eff;
  flex-shrink: 0;
}

.statistics-item .label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.statistics-item .value {
  font-weight: bold;
  color: #409eff;
  font-size: 18px;
  margin-left: auto;
  text-shadow: 0 1px 2px rgba(64, 158, 255, 0.1);
}

.statistics-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
  padding: 16px 0;
}

.statistics-empty .el-icon {
  font-size: 24px;
  color: #c0c4cc;
}

.collection-settings-section {
  margin-bottom: 20px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.batch-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.batch-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-weight: bold;
}

.selected-goods {
  margin: 0;
  padding-left: 20px;
  max-height: 150px;
  overflow-y: auto;
}

.selected-goods li {
  color: #409eff;
  margin-bottom: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.image-container {
  position: relative;
  display: inline-block;
}

.image-count {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

/* 商品名称相关样式 */
.goods-name-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.goods-name-text {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sales-info,
.rating-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  width: fit-content;
}

.sales-info {
  background: #e8f4fd;
  color: #409eff;
}

.rating-info {
  background: #fdf6ec;
  color: #e6a23c;
}

.sales-info .el-icon,
.rating-info .el-icon {
  font-size: 12px;
}

/* SKU相关样式 */
.sku-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.sku-item {
  padding: 6px 8px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.sku-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sku-image {
  flex-shrink: 0;
}

.sku-image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  background: #f0f0f0;
  color: #909399;
  font-size: 14px;
  border-radius: 4px;
}

.sku-info {
  flex: 1;
  min-width: 0;
}

.sku-text {
  font-size: 12px;
  color: #333;
  margin-bottom: 2px;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sku-price {
  font-size: 11px;
  color: #e6a23c;
  font-weight: bold;
}

.more-sku-btn {
  margin-top: 4px;
  padding: 2px 8px;
  font-size: 12px;
}

.no-sku {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.sku-detail-container {
  max-height: 400px;
  overflow-y: auto;
}

.sku-detail-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-image {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.price-text {
  color: #e6a23c;
  font-weight: bold;
}

/* 图片预览相关样式 */
.image-preview-dialog {
  margin-top: 3vh !important;
}

.image-preview-dialog .el-dialog {
  max-height: 94vh;
  display: flex;
  flex-direction: column;
}

.image-preview-dialog .el-dialog__header {
  flex-shrink: 0;
  padding: 15px 20px 10px;
}

.image-preview-dialog .el-dialog__body {
  padding: 0 20px 10px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.image-preview-dialog .el-dialog__footer {
  flex-shrink: 0;
  padding: 10px 20px 15px;
}

.image-preview-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  min-height: 0;
  max-height: calc(94vh - 100px);
}

.main-image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 200px;
  max-height: calc(94vh - 200px);
  background: #f5f7fa;
  border-radius: 8px;
  overflow: visible;
  padding: 15px;
  box-sizing: border-box;
}

.main-image {
  max-width: 100%;
  max-height: 500px;
  width: auto; 
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  display: block;
}

.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
}

.image-loading .el-icon {
  font-size: 48px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
}

.image-error .el-icon {
  font-size: 48px;
}

.image-counter {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
}

.nav-buttons {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.nav-btn {
  pointer-events: all;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-btn:hover {
  background: white;
  transform: scale(1.1);
}

.thumbnail-nav {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
  max-height: 80px;
  overflow-y: auto;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 8px;
  flex-shrink: 0;
}

.thumbnail-item {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  border-color: #409eff;
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.thumbnail-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  color: #909399;
}

.thumbnail-error .el-icon {
  font-size: 20px;
}

/* 平台关联分类样式 */
.platform-relations {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e4e7ed;
}

.platform-title {
  font-size: 12px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.platform-item {
  position: relative;
  margin-bottom: 8px;
  padding-top: 20px; /* 为右上角标签留出空间 */
}

.platform-badge {
  position: absolute;
  right: 0;
  font-size: 10px;
  font-weight: bold;
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3px 8px;
  border-radius: 0 4px 0 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
  letter-spacing: 0.5px;
}

.platform-categories {
  padding-left: 8px;
}

.category-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 6px 8px;
  margin-bottom: 4px;
  border-left: 3px solid #28a745;
  position: relative;
}

.category-names {
  margin-bottom: 3px;
}

.name-cn {
  font-size: 11px;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 1px;
}

.name-tl {
  font-size: 10px;
  color: #666;
  font-style: italic;
  display: block;
}

.category-paths {
  font-size: 10px;
  line-height: 1.3;
}

.path-cn {
  color: #666;
  display: block;
  margin-bottom: 1px;
}

.path-tl {
  color: #999;
  font-style: italic;
  display: block;
}

.more-categories-btn {
  margin-top: 4px;
  padding: 2px 8px;
  font-size: 11px;
  color: #409eff;
}

/* 平台分类详情对话框样式 */
.platform-categories-detail {
  max-height: 500px;
  overflow-y: auto;
}

.platform-info h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-names-detail {
  line-height: 1.4;
}

.category-names-detail .name-cn {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  margin-bottom: 4px;
}

.category-names-detail .name-tl {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.category-paths-detail {
  line-height: 1.4;
}

.category-paths-detail .path-cn {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.category-paths-detail .path-tl {
  font-size: 11px;
  color: #999;
  font-style: italic;
}

/* 商品分类关联对话框样式 */
.category-link-dialog {
  min-height: 600px;
}

.goods-info-section {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.goods-info-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.goods-basic {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.goods-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.goods-id {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  width: fit-content;
}

.category-path {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* SKU图片预览对话框样式 */
.sku-image-preview-dialog {
  margin-top: 10vh !important;
}

.sku-image-preview-dialog .el-dialog {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.sku-image-preview-dialog .el-dialog__body {
  padding: 20px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sku-image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  max-height: 60vh;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
}

.sku-preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .image-preview-dialog {
    margin: 0 !important;
  }

  .image-preview-dialog .el-dialog {
    width: 100% !important;
    height: 100vh !important;
    max-height: 100vh !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .thumbnail-nav {
    max-height: 60px;
  }

  .thumbnail-item {
    width: 40px;
    height: 40px;
  }

  /* 移动端可拖拽统计卡片调整 */
  .draggable-statistics-card {
    position: fixed;
    bottom: 10px;
    left: 10px;
    right: 10px;
    width: auto;
    border-radius: 8px;
  }

  .draggable-statistics-card .statistics-container {
    padding: 8px 12px;
  }

  .statistics-info {
    flex-direction: row;
    gap: 8px;
  }

  .statistics-item {
    flex: 1;
    padding: 6px 8px;
    flex-direction: column;
    text-align: center;
    gap: 4px;
  }

  .statistics-item .label {
    font-size: 12px;
  }

  .statistics-item .value {
    font-size: 16px;
    margin-left: 0;
  }

  /* 移动端快捷操作按钮调整 */
  .quick-actions {
    gap: 3px;
  }

  .quick-btn {
    height: 26px;
    font-size: 10px;
    padding: 0 3px;
  }

  .directory-info {
    padding: 6px 8px;
  }

  .directory-label,
  .directory-name {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .store-selection .el-checkbox-group {
    grid-template-columns: 1fr;
  }
  
  .store-selection .el-checkbox-group .el-checkbox {
    justify-content: flex-start;
  }

  /* 超小屏幕统计卡片调整 */
  .draggable-statistics-card {
    bottom: 5px;
    left: 5px;
    right: 5px;
  }

  .draggable-statistics-card .statistics-container {
    padding: 8px 12px;
  }

  .statistics-item .el-icon {
    font-size: 14px;
  }

  .statistics-item .label {
    font-size: 11px;
  }

  .statistics-item .value {
    font-size: 14px;
  }

  /* 超小屏幕快捷操作按钮调整 */
  .quick-btn {
    height: 24px;
    font-size: 9px;
    padding: 0 2px;
  }

  .directory-info {
    padding: 4px 6px;
  }

  .directory-label,
  .directory-name {
    font-size: 10px;
  }
}
</style>