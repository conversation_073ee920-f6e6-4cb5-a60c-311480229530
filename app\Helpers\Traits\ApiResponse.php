<?php
declare(strict_types=1);

namespace App\Helpers\Traits;
use Symfony\Component\HttpFoundation\Response as FoundationResponse;

trait ApiResponse
{

    protected $statusCode = FoundationResponse::HTTP_OK;
    protected $token = '';

    /**
     * @return int
     */
    public function getStatusCode()
    {
        return $this->statusCode;
    }

    /**
     * @param $statusCode
     * @return $this
     */
    public function setStatusCode($statusCode)
    {
        $this->statusCode = $statusCode;
        return $this;
    }

    /**
     * @param $token
     * @return $this
     */
    public function setToken($token)
    {
        $this->token = $token;
        return $this;
    }

    /**
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function respond($data)
    {
        $response = response()->json($data, $this->getStatusCode())->setEncodingOptions(JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        if ($this->token) {
            $response->headers->set('Authorization', 'Bearer ' . $this->token);
        }
        return $response;
    }

    /**
     * @param $status
     * @param array $data
     * @param null $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function status($status, array $data, $code = null)
    {
        if ($code) {
            $this->setStatusCode($code);
        }
        $status = [
            'status' => (int)$status,
            'code' => $this->statusCode
        ];
        //增加系统默认返回信息
        $system = [
            'system' => request()->system ?? new \stdClass(),
            'user'   => request()->user   ?? new \stdClass(),
            'token'  => request()->token  ?? ''
        ];
        $data = array_merge($system,$status, $data);
        return $this->respond($data);
    }

    /**
     * @param $message
     * @param int $code
     * @param string $status
     * @return mixed
     */
    public function failed($message="暂无数据", $code = FoundationResponse::HTTP_OK, $status =500)
    {
        return $this->setStatusCode($code)->message($message, $status);
    }

    /**
     * @param $message
     * @param string $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function message($message, $status = 200)
    {
        $status = [
            'status' => (int)$status,
            'code' => $this->statusCode,
            'message' => $message,
        ];
        return $this->respond($status);
    }

    /**
     * @param string $message
     * @return mixed
     */
    public function internalError($message = "Internal Error!")
    {
        return $this->failed($message, FoundationResponse::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function created($message = "created")
    {
        return $this->setStatusCode(FoundationResponse::HTTP_CREATED)
            ->message($message);
    }

    /**
     * @param $data
     * @param string $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function success($data=[], $status = 200, $message = "操作成功")
    {
        $data = array_merge(compact('data'), ['message' => $message]);
        return $this->status($status, $data);
    }

    /**
     * @param string $message
     * @return mixed
     */
    public function notFond($message = 'Not Fond!')
    {
        return $this->failed($message, Foundationresponse::HTTP_NOT_FOUND);
    }

    public function apiSuccess($data=[], $status = 200, $message = "success"){
        // 获取当前请求
        $request = request();

        // 获取当前请求方法和应用URL的主机名
        $isPostRequest = $request->isMethod('post');
        $appUrl = config('app.url');
        //是否强制返回加密的数据
        $responseEncrypto = config('ad.response_encrypto');
        $appHost = parse_url($appUrl, PHP_URL_HOST);
        $currentHost = $request->getHost();

        // 检查是否需要加密（只有在POST请求且当前域名不是配置的app.url主机名时才加密）
        if ( !empty($data) && ($responseEncrypto == 1 || ($isPostRequest && $currentHost !== $appHost) )) {
            $appKey = config('ad.appkey');
            $encryptedData = \App\Utils\AdSecurity::encrypt(['data' => $data], $appKey);
            $data = $encryptedData;
        }
        // 不需要加密，保持原有逻辑
        $data = array_merge(compact('data'), ['msg' => $message]);
        return $this->apiStatus($status, $data);
    }

    public function apiError($message = "error", $status = 200){
        return $this->apiStatus($status, ['msg'=>$message], 0);
    }

    public function apiStatus($status, array $data, $code = 1,$status_code = 200)
    {
        if ($status_code) {
            $this->setStatusCode($status_code);
        }
        $status = [
            'status' => (int)$status,
            'code' => $code
        ];
        $data = array_merge($status, $data);
        if(isset($data['data']) && is_array($data['data']) && count($data['data'])==1 && array_key_exists(0, $data['data'])){
            $data = array_merge($data, ['data'=>$data['data'][0]]);
        }
        return $this->respond($data);
    }

    /**
     * @param $message
     * @param int $code
     * @param string $status
     * @return mixed
     */
    public function apiFailed($message="暂无数据", $code = 0, $status_code =200)
    {
        return $this->setStatusCode($status_code)->apiMessage($message, $code,$status_code);
    }

    /**
     * @param $message
     * @param string $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiMessage($message, $code = 0,$status_code = 200)
    {
        $status = [
            'status' => (int)$status_code,
            'code' => $code,
            'msg' => $message,
            'data' => [],
        ];
        return $this->respond($status);
    }

}
