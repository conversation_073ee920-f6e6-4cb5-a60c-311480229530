const gulp = require('gulp');
const gulpJavaScriptObfuscator = require('gulp-javascript-obfuscator');
const htmlmin = require('gulp-html-minifier-terser');
const cleanCSS = require('gulp-clean-css');
const sourcemaps = require('gulp-sourcemaps');
const path = require('path');
const fs = require('fs');
const through2 = require('through2');

// 生成随机密钥，用于控制混淆强度
const secretKey = Number((((Math.random() * 100) / 100).toFixed(2)));
console.log('%c混淆密钥: ' + secretKey, 'color: #2563fc');

// 进度跟踪工具
class ProgressTracker {
  constructor(taskName, mode = 'normal') {
    this.taskName = taskName;
    this.mode = mode;
    this.startTime = Date.now();
    this.totalFiles = 0;
    this.processedFiles = 0;
    this.currentFile = '';
    this.estimatedTimePerFile = this.getEstimatedTimePerFile(mode);
  }

  getEstimatedTimePerFile(mode) {
    // 根据混淆模式估算每个文件的处理时间（毫秒）
    const timeEstimates = {
      'fast': 50,      // 超快速：50ms/文件
      'normal': 200,   // 平衡：200ms/文件
      'full': 500,     // 完整：500ms/文件
      'strong': 1200   // 高强度：1200ms/文件
    };
    return timeEstimates[mode] || timeEstimates['normal'];
  }

  setTotalFiles(count) {
    this.totalFiles = count;
    console.log(`\n📊 ${this.taskName} - 总计 ${count} 个文件需要处理`);
  }

  updateProgress(fileName) {
    this.processedFiles++;
    this.currentFile = path.basename(fileName);
    
    // 防止处理文件数超过总数
    if (this.processedFiles > this.totalFiles) {
      this.totalFiles = this.processedFiles;
    }
    
    const elapsed = Date.now() - this.startTime;
    const elapsedSeconds = Math.floor(elapsed / 1000);
    const remaining = Math.max(0, this.totalFiles - this.processedFiles);
    const estimatedRemaining = Math.ceil((remaining * this.estimatedTimePerFile) / 1000);
    
    // 根据实际处理速度调整估算
    if (this.processedFiles > 1 && elapsed > 0) {
      const actualTimePerFile = elapsed / this.processedFiles;
      const adjustedEstimate = Math.ceil((remaining * actualTimePerFile) / 1000);
      const finalEstimate = Math.max(0, Math.min(estimatedRemaining, adjustedEstimate));
      
      console.log(`🔄 [${this.processedFiles}/${this.totalFiles}] 正在处理: ${this.currentFile}`);
      console.log(`   ⏱️  已用时: ${this.formatTime(elapsedSeconds)} | 预计剩余: ${this.formatTime(finalEstimate)}`);
    } else {
      console.log(`🔄 [${this.processedFiles}/${this.totalFiles}] 正在处理: ${this.currentFile}`);
      console.log(`   ⏱️  已用时: ${this.formatTime(elapsedSeconds)} | 预计剩余: ${this.formatTime(Math.max(0, estimatedRemaining))}`);
    }
    
    // 显示进度条
    this.showProgressBar();
  }

  showProgressBar() {
    // 防止除零错误和无效值
    if (this.totalFiles === 0) {
      console.log(`   📈 进度: [░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░] 0%\n`);
      return;
    }
    
    const percentage = Math.floor((this.processedFiles / this.totalFiles) * 100);
    const barLength = 30;
    const filledLength = Math.max(0, Math.min(barLength, Math.floor((percentage / 100) * barLength)));
    const emptyLength = Math.max(0, barLength - filledLength);
    
    // 确保重复次数为有效值
    const bar = '█'.repeat(filledLength) + '░'.repeat(emptyLength);
    console.log(`   📈 进度: [${bar}] ${Math.max(0, Math.min(100, percentage))}%\n`);
  }

  formatTime(seconds) {
    // 确保seconds为有效的正数
    const validSeconds = Math.max(0, Math.floor(seconds));
    
    if (validSeconds < 60) {
      return `${validSeconds}秒`;
    } else if (validSeconds < 3600) {
      const minutes = Math.floor(validSeconds / 60);
      const remainingSeconds = validSeconds % 60;
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      const hours = Math.floor(validSeconds / 3600);
      const minutes = Math.floor((validSeconds % 3600) / 60);
      return `${hours}小时${minutes}分钟`;
    }
  }

  complete() {
    const totalTime = Date.now() - this.startTime;
    const totalSeconds = Math.floor(totalTime / 1000);
    console.log(`✅ ${this.taskName} 完成！`);
    console.log(`   📊 处理了 ${this.processedFiles} 个文件`);
    console.log(`   ⏱️  总用时: ${this.formatTime(totalSeconds)}`);
    
    // 防止除零错误
    if (this.processedFiles > 0) {
      console.log(`   📈 平均速度: ${Math.floor(totalTime / this.processedFiles)}ms/文件\n`);
    } else {
      console.log(`   📈 平均速度: 0ms/文件\n`);
    }
  }
}

// 创建进度跟踪插件
function createProgressPlugin(tracker) {
  let fileCount = 0;
  
  return through2.obj(function(file, enc, callback) {
    if (file.isNull()) {
      return callback(null, file);
    }
    
    if (file.isStream()) {
      return callback(new Error('Streaming not supported'));
    }
    
    // 第一次调用时统计总文件数
    if (fileCount === 0) {
      // 这里我们需要先统计文件数量
      fileCount++;
    }
    
    tracker.updateProgress(file.path);
    callback(null, file);
  });
}

// 统计文件数量的辅助函数
function countFiles(globPattern, excludePatterns = []) {
  try {
    const glob = require('glob');
    const files = glob.sync(globPattern);
    
    // 过滤排除的文件
    const filteredFiles = files.filter(file => {
      return !excludePatterns.some(pattern => {
        const cleanPattern = pattern.replace('!', '');
        return file.includes(cleanPattern.replace('/**', '').replace('**/', ''));
      });
    });
    
    return Math.max(0, filteredFiles.length);
  } catch (error) {
    console.warn(`⚠️ 统计文件时出错: ${error.message}`);
    return 1; // 返回1避免除零错误
  }
}

// 配置路径 - 相对于项目根目录
const rootPath = path.resolve(__dirname, '../../');
const paths = {
  build: path.join(rootPath, 'tsa_build'),
  backup: path.join(rootPath, 'tsa_build_backup'),
  js: path.join(rootPath, 'tsa_build/**/*.js'),
  html: path.join(rootPath, 'tsa_build/**/*.html'),
  css: path.join(rootPath, 'tsa_build/**/*.css'),
  backgroundMain: path.join(rootPath, 'tsa_build/background.main.js'),
  exclude: [
    `!${path.join(rootPath, 'tsa_build/node_modules/**')}`,
    `!${path.join(rootPath, 'tsa_build/web/node_modules/**')}`,
    `!${path.join(rootPath, 'tsa_build/static/**')}`
  ]
};

// 备份原始文件
gulp.task('backup', function(cb) {
  console.log('🔄 正在备份原始文件...');
  
  // 如果备份目录存在，先删除
  if (fs.existsSync(paths.backup)) {
    console.log('🗑️ 清理旧备份...');
    fs.rmSync(paths.backup, { recursive: true, force: true });
  }
  
  // 创建进度跟踪器
  const tracker = new ProgressTracker('文件备份', 'fast');
  const fileCount = countFiles(paths.build + '/**/*', []);
  tracker.setTotalFiles(fileCount);
  
  // 复制整个构建目录作为备份
  return gulp.src([paths.build + '/**/*'])
    .pipe(createProgressPlugin(tracker))
    .pipe(gulp.dest(paths.backup))
    .on('end', () => {
      tracker.complete();
      console.log('📁 备份位置: ' + paths.backup);
      cb();
    });
});

// 混淆 JavaScript 文件
gulp.task('obfuscate-js', function(cb) {
  console.log('🔒 正在混淆 JavaScript 文件...');
  
  // 创建进度跟踪器
  const tracker = new ProgressTracker('JavaScript 混淆', 'normal');
  const fileCount = countFiles(paths.js, paths.exclude);
  tracker.setTotalFiles(fileCount);
  
  return gulp.src([paths.js, ...paths.exclude])
    .pipe(createProgressPlugin(tracker))
    .pipe(gulpJavaScriptObfuscator({
      // 基础混淆配置
      compact: true,                    // 压缩代码
      selfDefending: false,             // 关闭自我保护（提升速度）
      
      // 控制流混淆 - 优化性能
      controlFlowFlattening: true,      // 启用控制流扁平化
      controlFlowFlatteningThreshold: Math.min(secretKey, 0.3), // 限制最大阈值提升速度
      
      // 死代码注入 - 减少注入量
      deadCodeInjection: true,          // 启用死代码注入
      deadCodeInjectionThreshold: 0.2,  // 降低死代码注入阈值（提升速度）
      
      // 标识符混淆 - 使用更快的生成器
      identifierNamesGenerator: 'mangled', // 使用更快的标识符生成器
      renameGlobals: false,             // 不重命名全局变量（重要：避免破坏Chrome扩展API）
      
      // 字符串混淆 - 优化性能
      stringArray: true,                // 启用字符串数组
      stringArrayThreshold: 0.5,        // 降低字符串数组阈值（提升速度）
      stringArrayEncoding: [],          // 关闭字符串编码（大幅提升速度）
      stringArrayWrappersCount: 1,      // 减少包装器数量
      stringArrayWrappersChainedCalls: false, // 关闭链式调用（提升速度）
      
      // 数字混淆 - 关闭以提升速度
      numbersToExpressions: false,      // 关闭数字转表达式（提升速度）
      
      // 其他配置 - 优化性能
      splitStrings: false,              // 关闭字符串分割（提升速度）
      splitStringsChunkLength: 10,      // 分割长度
      
      // 性能优化配置
      simplify: true,                   // 启用简化（提升速度）
      
      // 保留重要的标识符（Chrome扩展相关）
      reservedNames: [
        'chrome',
        'browser',
        'manifest',
        'background',
        'popup',
        'content',
        'options',
        'tabs',
        'storage',
        'runtime',
        'extension',
        'webRequest',
        'cookies',
        'permissions',
        'activeTab',
        'scripting',
        'host_permissions'
      ],
      
      // 保留字符串（API相关）
      reservedStrings: [
        'chrome-extension://',
        'moz-extension://',
        'content_scripts',
        'background',
        'popup',
        'options'
      ],
      
      // 性能优化
      target: 'browser',                // 目标环境
      disableConsoleOutput: false,      // 保留console（开发阶段）
      
      // 调试配置
      sourceMap: false,                 // 生产环境不生成source map
      sourceMapMode: 'separate'
    }))
    .pipe(gulp.dest(paths.build))
    .on('end', () => {
      tracker.complete();
      cb();
    });
});

// 压缩 HTML 文件
gulp.task('minify-html', function(cb) {
  console.log('🗜️ 正在压缩 HTML 文件...');
  
  // 创建进度跟踪器
  const tracker = new ProgressTracker('HTML 压缩', 'fast');
  const fileCount = countFiles(paths.html, paths.exclude);
  
  if (fileCount === 0) {
    console.log('📝 没有找到 HTML 文件，跳过压缩');
    return cb();
  }
  
  tracker.setTotalFiles(fileCount);
  
  return gulp.src([paths.html, ...paths.exclude])
    .pipe(createProgressPlugin(tracker))
    .pipe(htmlmin({
      removeComments: true,             // 移除注释
      collapseWhitespace: true,         // 折叠空白字符
      collapseBooleanAttributes: true,  // 折叠布尔属性
      removeEmptyAttributes: true,      // 移除空属性
      removeScriptTypeAttributes: true, // 移除script type属性
      removeStyleLinkTypeAttributes: true, // 移除style link type属性
      minifyJS: true,                   // 压缩内联JS
      minifyCSS: true,                  // 压缩内联CSS
      minifyURLs: true,                 // 压缩URL
      removeRedundantAttributes: true,  // 移除冗余属性
      useShortDoctype: true,            // 使用短DOCTYPE
      removeOptionalTags: false,        // 保留可选标签（重要）
      caseSensitive: true               // 大小写敏感
    }))
    .pipe(gulp.dest(paths.build))
    .on('end', () => {
      tracker.complete();
      cb();
    });
});

// 压缩 CSS 文件
gulp.task('minify-css', function(cb) {
  console.log('🎨 正在压缩 CSS 文件...');
  
  // 创建进度跟踪器
  const tracker = new ProgressTracker('CSS 压缩', 'fast');
  const fileCount = countFiles(paths.css, paths.exclude);
  
  if (fileCount === 0) {
    console.log('🎨 没有找到 CSS 文件，跳过压缩');
    return cb();
  }
  
  tracker.setTotalFiles(fileCount);
  
  return gulp.src([paths.css, ...paths.exclude])
    .pipe(createProgressPlugin(tracker))
    .pipe(sourcemaps.init())
    .pipe(cleanCSS({
      compatibility: 'ie8',            // 兼容性设置
      level: 2,                        // 压缩级别
      debug: true,                     // 调试模式
      rebase: false,                   // 不重新定位URL
      keepSpecialComments: 0,          // 不保留特殊注释
      aggressiveMerging: true,         // 激进合并
      mediaMerging: true,              // 媒体查询合并
      restructuring: true,             // 重构
      semanticMerging: false           // 语义合并（保守设置）
    }))
    .pipe(gulp.dest(paths.build))
    .on('end', () => {
      tracker.complete();
      cb();
    });
});

// 验证混淆结果
gulp.task('verify', function(cb) {
  console.log('🔍 正在验证混淆结果...');
  
  const manifestPath = path.join(paths.build, 'manifest.json');
  const popupJsPath = path.join(paths.build, 'popup.main.js');
  const backgroundJsPath = path.join(paths.build, 'background.main.js');
  
  // 检查关键文件是否存在
  const criticalFiles = [manifestPath, popupJsPath, backgroundJsPath];
  const missingFiles = criticalFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    console.error('❌ 关键文件缺失:', missingFiles);
    return cb(new Error('关键文件缺失'));
  }
  
  // 检查文件大小变化
  try {
    const originalSize = fs.statSync(path.join(paths.backup, 'popup.main.js')).size;
    const obfuscatedSize = fs.statSync(popupJsPath).size;
    const sizeChange = ((obfuscatedSize - originalSize) / originalSize * 100).toFixed(2);
    
    console.log(`📊 文件大小变化: ${sizeChange}%`);
    console.log(`   原始大小: ${(originalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   混淆后大小: ${(obfuscatedSize / 1024 / 1024).toFixed(2)} MB`);
  } catch (error) {
    console.warn('⚠️ 无法比较文件大小:', error.message);
  }
  
  console.log('✅ 验证完成');
  cb();
});

// 恢复备份
gulp.task('restore', function(cb) {
  console.log('🔄 正在恢复备份...');
  
  if (!fs.existsSync(paths.backup)) {
    console.error('❌ 备份目录不存在:', paths.backup);
    return cb(new Error('备份目录不存在'));
  }
  
  // 删除当前构建目录
  if (fs.existsSync(paths.build)) {
    fs.rmSync(paths.build, { recursive: true, force: true });
  }
  
  // 恢复备份
  return gulp.src([paths.backup + '/**/*'])
    .pipe(gulp.dest(paths.build))
    .on('end', () => {
      console.log('✅ 恢复完成');
      cb();
    });
});

// 清理备份
gulp.task('clean-backup', function(cb) {
  console.log('🧹 正在清理备份文件...');
  
  if (fs.existsSync(paths.backup)) {
    fs.rmSync(paths.backup, { recursive: true, force: true });
    console.log('✅ 备份文件已清理');
  }
  
  cb();
});

// 超快速混淆（仅基础混淆）
gulp.task('obfuscate-js-fast', function(cb) {
  console.log('⚡ 正在执行超快速混淆...');
  
  // 创建进度跟踪器
  const tracker = new ProgressTracker('超快速 JavaScript 混淆', 'fast');
  const fileCount = countFiles(paths.js, paths.exclude);
  tracker.setTotalFiles(fileCount);
  
  return gulp.src([paths.js, ...paths.exclude])
    .pipe(createProgressPlugin(tracker))
    .pipe(gulpJavaScriptObfuscator({
      // 最小化混淆配置 - 追求极致速度
      compact: true,                    // 压缩代码
      selfDefending: false,             // 关闭自我保护
      
      // 最基础的混淆
      controlFlowFlattening: false,     // 关闭控制流扁平化（大幅提升速度）
      deadCodeInjection: false,         // 关闭死代码注入（大幅提升速度）
      
      // 仅标识符混淆
      identifierNamesGenerator: 'mangled', // 使用最快的标识符生成器
      renameGlobals: false,             // 不重命名全局变量
      
      // 最简字符串处理
      stringArray: false,               // 关闭字符串数组（大幅提升速度）
      
      // 关闭所有耗时功能
      numbersToExpressions: false,      // 关闭数字转表达式
      splitStrings: false,              // 关闭字符串分割
      simplify: true,                   // 启用简化
      
      // 保留重要的标识符（Chrome扩展相关）
      reservedNames: [
        'chrome', 'browser', 'manifest', 'background', 'popup',
        'content', 'options', 'tabs', 'storage', 'runtime',
        'extension', 'webRequest', 'cookies', 'permissions',
        'activeTab', 'scripting', 'host_permissions'
      ],
      
      // 保留字符串（API相关）
      reservedStrings: [
        'chrome-extension://', 'moz-extension://',
        'content_scripts', 'background', 'popup', 'options'
      ],
      
      // 性能优化
      target: 'browser',
      disableConsoleOutput: false,
      sourceMap: false
    }))
    .pipe(gulp.dest(paths.build))
    .on('end', () => {
      tracker.complete();
      cb();
    });
});

// 高强度混淆（原配置的加强版）
gulp.task('obfuscate-js-strong', function(cb) {
  console.log('🔐 正在执行高强度混淆...');
  
  // 创建进度跟踪器
  const tracker = new ProgressTracker('高强度 JavaScript 混淆', 'strong');
  const fileCount = countFiles(paths.js, paths.exclude);
  tracker.setTotalFiles(fileCount);
  
  return gulp.src([paths.js, ...paths.exclude])
    .pipe(createProgressPlugin(tracker))
    .pipe(gulpJavaScriptObfuscator({
      // 高强度混淆配置
      compact: true,
      selfDefending: true,              // 启用自我保护
      
      // 强化控制流混淆
      controlFlowFlattening: true,
      controlFlowFlatteningThreshold: 0.8, // 高阈值
      
      // 强化死代码注入
      deadCodeInjection: true,
      deadCodeInjectionThreshold: 0.6,  // 高阈值
      
      // 使用十六进制标识符
      identifierNamesGenerator: 'hexadecimal',
      renameGlobals: false,
      
      // 强化字符串混淆
      stringArray: true,
      stringArrayThreshold: 0.9,        // 高阈值
      stringArrayEncoding: ['base64', 'rc4'], // 多重编码
      stringArrayWrappersCount: 3,      // 更多包装器
      stringArrayWrappersChainedCalls: true,
      
      // 启用数字混淆
      numbersToExpressions: true,
      
      // 启用字符串分割
      splitStrings: true,
      splitStringsChunkLength: 5,       // 更小的分割长度
      
      // 保留标识符和字符串
      reservedNames: [
        'chrome', 'browser', 'manifest', 'background', 'popup',
        'content', 'options', 'tabs', 'storage', 'runtime',
        'extension', 'webRequest', 'cookies', 'permissions',
        'activeTab', 'scripting', 'host_permissions'
      ],
      reservedStrings: [
        'chrome-extension://', 'moz-extension://',
        'content_scripts', 'background', 'popup', 'options'
      ],
      
      target: 'browser',
      disableConsoleOutput: false,
      sourceMap: false
    }))
    .pipe(gulp.dest(paths.build))
    .on('end', () => {
      tracker.complete();
      cb();
    });
});

// 只混淆 background.main.js 文件
gulp.task('obfuscate-background-only', function(cb) {
  console.log('🎯 正在混淆关键文件: background.main.js');
  
  // 检查文件是否存在
  if (!fs.existsSync(paths.backgroundMain)) {
    console.log('❌ 错误: background.main.js 文件不存在');
    console.log('📁 预期路径:', paths.backgroundMain);
    return cb(new Error('background.main.js 文件不存在'));
  }
  
  // 创建进度跟踪器
  const tracker = new ProgressTracker('background.main.js 混淆', 'normal');
  tracker.setTotalFiles(1);
  
  return gulp.src(paths.backgroundMain)
    .pipe(createProgressPlugin(tracker))
    .pipe(gulpJavaScriptObfuscator({
      // 针对关键文件的混淆配置
      compact: true,
      selfDefending: false,
      
      // 控制流混淆
      controlFlowFlattening: true,
      controlFlowFlatteningThreshold: 0.6,
      
      // 死代码注入
      deadCodeInjection: true,
      deadCodeInjectionThreshold: 0.4,
      
      // 标识符混淆
      identifierNamesGenerator: 'hexadecimal',
      renameGlobals: false,
      
      // 字符串混淆
      stringArray: true,
      stringArrayThreshold: 0.8,
      stringArrayEncoding: ['base64'],
      stringArrayWrappersCount: 2,
      stringArrayWrappersChainedCalls: true,
      
      // 数字混淆
      numbersToExpressions: true,
      
      // 字符串分割
      splitStrings: true,
      splitStringsChunkLength: 8,
      
      // 保留重要的标识符（Chrome扩展相关）
      reservedNames: [
        'chrome', 'browser', 'manifest', 'background', 'popup',
        'content', 'options', 'tabs', 'storage', 'runtime',
        'extension', 'webRequest', 'cookies', 'permissions',
        'activeTab', 'scripting', 'host_permissions'
      ],
      
      // 保留字符串（API相关）
      reservedStrings: [
        'chrome-extension://', 'moz-extension://',
        'content_scripts', 'background', 'popup', 'options'
      ],
      
      target: 'browser',
      disableConsoleOutput: false,
      sourceMap: false
    }))
    .pipe(gulp.dest(path.dirname(paths.backgroundMain)))
    .on('end', () => {
      tracker.complete();
      console.log('✅ background.main.js 混淆完成！');
      console.log('📁 文件位置:', paths.backgroundMain);
      cb();
    });
});

// 完整的 background.main.js 混淆流程（包含备份）
gulp.task('obfuscate-background', gulp.series(
  function(cb) {
    global.taskStartTime = Date.now();
    createTaskStartMessage('关键文件混淆', [
      '备份 background.main.js',
      '混淆 background.main.js',
      '验证结果'
    ]);
    cb();
  },
  function(cb) {
    // 只备份 background.main.js 文件
    console.log('🔄 正在备份 background.main.js...');
    
    if (!fs.existsSync(paths.backgroundMain)) {
      console.log('❌ 错误: background.main.js 文件不存在');
      return cb(new Error('background.main.js 文件不存在'));
    }
    
    // 创建备份目录
    if (!fs.existsSync(paths.backup)) {
      fs.mkdirSync(paths.backup, { recursive: true });
    }
    
    const backupFile = path.join(paths.backup, 'background.main.js');
    
    try {
      fs.copyFileSync(paths.backgroundMain, backupFile);
      console.log('✅ background.main.js 备份完成');
      console.log('📁 备份位置:', backupFile);
      cb();
    } catch (error) {
      console.log('❌ 备份失败:', error.message);
      cb(error);
    }
  },
  'obfuscate-background-only',
  function(cb) {
    createTaskCompleteMessage('关键文件混淆', global.taskStartTime);
    cb();
  }
));

// 任务开始提示
function createTaskStartMessage(taskName, steps) {
  console.log('\n' + '='.repeat(60));
  console.log(`🚀 开始执行: ${taskName}`);
  console.log('📋 执行步骤:');
  steps.forEach((step, index) => {
    console.log(`   ${index + 1}. ${step}`);
  });
  console.log('='.repeat(60) + '\n');
}

// 任务完成提示
function createTaskCompleteMessage(taskName, startTime) {
  const totalTime = Date.now() - startTime;
  const totalSeconds = Math.floor(totalTime / 1000);
  console.log('\n' + '='.repeat(60));
  console.log(`🎉 ${taskName} 全部完成！`);
  console.log(`⏱️  总耗时: ${formatTime(totalSeconds)}`);
  console.log('='.repeat(60) + '\n');
}

// 格式化时间函数
function formatTime(seconds) {
  if (seconds < 60) {
    return `${seconds}秒`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  }
}

// 完整的混淆流程
gulp.task('obfuscate', gulp.series(
  function(cb) {
    global.taskStartTime = Date.now();
    createTaskStartMessage('完整混淆', [
      '备份原始文件',
      'JavaScript 混淆（平衡模式）',
      'HTML 压缩',
      'CSS 压缩',
      '验证结果'
    ]);
    cb();
  },
  'backup',
  'obfuscate-js',
  'minify-html', 
  'minify-css',
  'verify',
  function(cb) {
    createTaskCompleteMessage('完整混淆', global.taskStartTime);
    cb();
  }
));

// 快速混淆（仅JS - 平衡模式）
gulp.task('obfuscate-quick', gulp.series(
  function(cb) {
    global.taskStartTime = Date.now();
    createTaskStartMessage('平衡混淆', [
      '备份原始文件',
      'JavaScript 混淆（平衡模式）',
      '验证结果'
    ]);
    cb();
  },
  'backup',
  'obfuscate-js',
  'verify',
  function(cb) {
    createTaskCompleteMessage('平衡混淆', global.taskStartTime);
    cb();
  }
));

// 超快速混淆（仅基础混淆）
gulp.task('obfuscate-fast', gulp.series(
  function(cb) {
    global.taskStartTime = Date.now();
    createTaskStartMessage('超快速混淆', [
      '备份原始文件',
      'JavaScript 混淆（超快速模式）',
      '验证结果'
    ]);
    cb();
  },
  'backup',
  'obfuscate-js-fast',
  'verify',
  function(cb) {
    createTaskCompleteMessage('超快速混淆', global.taskStartTime);
    cb();
  }
));

// 高强度混淆
gulp.task('obfuscate-strong', gulp.series(
  function(cb) {
    global.taskStartTime = Date.now();
    createTaskStartMessage('高强度混淆', [
      '备份原始文件',
      'JavaScript 混淆（高强度模式）',
      'HTML 压缩',
      'CSS 压缩',
      '验证结果'
    ]);
    cb();
  },
  'backup',
  'obfuscate-js-strong',
  'minify-html',
  'minify-css',
  'verify',
  function(cb) {
    createTaskCompleteMessage('高强度混淆', global.taskStartTime);
    cb();
  }
));

// 默认任务（平衡模式）
gulp.task('default', gulp.series(['obfuscate']));

// 导出任务用于外部调用
module.exports = {
  obfuscate: gulp.series(['obfuscate']),
  obfuscateFast: gulp.series(['obfuscate-fast']),
  obfuscateStrong: gulp.series(['obfuscate-strong']),
  restore: gulp.series(['restore']),
  clean: gulp.series(['clean-backup'])
}; 