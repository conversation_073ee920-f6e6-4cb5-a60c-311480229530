<?php

namespace App\Utils;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ClearTableFieldsCache
{
    public function clear()
    {
        $this->clearTableFieldsCache();
    }

    public function clearTableFieldsCache()
    {
        $tables = DB::select('show tables');
        $database_name = 'Tables_in_' . config('database.connections.mysql.database');
        foreach ($tables as $k => $v) {
            $v = $v->$database_name;
            Cache::forget($v);
        }
        echo "已清空所有表缓存字段\r\n";
    }
}
