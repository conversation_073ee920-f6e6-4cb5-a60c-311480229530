<?php
namespace App\Http\Controllers\Api\V1\Wap\TeMu;

use App\Http\Controllers\Api\Controller;
use App\Service\TeMu\GoodsService;

class GoodsController extends Controller{

    protected GoodsService $goodsService;

    public function __construct(GoodsService $goodsService){
        $this->goodsService = $goodsService;
    }

    public function goodsAdd(\Illuminate\Http\Request $request)
    {
        $data = $request->all();

        $user = $request->attributes->get('user');
        $user_id = $user['id'] ?? 0;
        $user_id = intval($user_id);
        $data['user_id'] = $user_id;
        $result = $this->goodsService->saveGoodsData($data);
        return $this->apiSuccess($result, '商品数据添加成功');
    }
}
