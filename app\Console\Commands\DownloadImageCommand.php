<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class DownloadImageCommand extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'image:download 
                            {url? : 图片URL地址}
                            {--name= : 自定义文件名（可选）}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '下载图片到public/attachment目录';

    /**
     * 默认图片URL
     *
     * @var string
     */
    private $defaultUrl = 'https://img.kwcdn.com/product/fancy/de690f5c-e207-40fb-b5a8-33cab0a48765.jpg';

    /**
     * 执行命令
     */
    public function handle()
    {
        try {
            // 获取图片URL，如果没有提供则使用默认URL
            $imageUrl = $this->argument('url') ?: $this->defaultUrl;
            
            $this->info("开始下载图片: {$imageUrl}");
            
            // 验证URL格式
            if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                $this->error('无效的URL格式');
                return Command::FAILURE;
            }
            
            // 确保目标目录存在
            $targetDir = public_path('attachment');
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
                $this->info("创建目录: {$targetDir}");
            }
            
            // 获取文件名
            $fileName = $this->getFileName($imageUrl);
            $filePath = $targetDir . DIRECTORY_SEPARATOR . $fileName;
            
            // 检查文件是否已存在
            if (file_exists($filePath)) {
                if (!$this->confirm("文件 {$fileName} 已存在，是否覆盖？")) {
                    $this->info('操作已取消');
                    return Command::SUCCESS;
                }
            }
            
            // 下载图片
            $this->info('正在下载图片...');
            $imageData = $this->downloadImage($imageUrl);
            
            if ($imageData === false) {
                $this->error('图片下载失败');
                return Command::FAILURE;
            }
            
            // 保存图片
            $result = file_put_contents($filePath, $imageData);
            
            if ($result === false) {
                $this->error('图片保存失败');
                return Command::FAILURE;
            }
            
            $fileSize = $this->formatBytes(strlen($imageData));
            $this->info("图片下载成功！");
            $this->info("文件名: {$fileName}");
            $this->info("文件大小: {$fileSize}");
            $this->info("保存路径: {$filePath}");
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("下载过程中发生错误: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
    
    /**
     * 下载图片数据
     *
     * @param string $url
     * @return string|false
     */
    private function downloadImage(string $url)
    {
        // 设置上下文选项
        $context = stream_context_create([
            'http' => [
                'timeout' => 30, // 30秒超时
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
        ]);
        
        // 使用file_get_contents下载
        $imageData = @file_get_contents($url, false, $context);
        
        // 如果file_get_contents失败，尝试使用cURL
        if ($imageData === false && function_exists('curl_init')) {
            $imageData = $this->downloadWithCurl($url);
        }
        
        return $imageData;
    }
    
    /**
     * 使用cURL下载图片
     *
     * @param string $url
     * @return string|false
     */
    private function downloadWithCurl(string $url)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);
        
        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($data === false || $httpCode !== 200) {
            $this->error("cURL下载失败: HTTP {$httpCode}, Error: {$error}");
            return false;
        }
        
        return $data;
    }
    
    /**
     * 获取文件名
     *
     * @param string $url
     * @return string
     */
    private function getFileName(string $url): string
    {
        // 检查是否有自定义文件名
        $customName = $this->option('name');
        if ($customName) {
            // 确保有扩展名
            if (!pathinfo($customName, PATHINFO_EXTENSION)) {
                $extension = $this->getFileExtension($url);
                $customName .= $extension ? ".{$extension}" : '.jpg';
            }
            return $customName;
        }
        
        // 从URL提取文件名
        $fileName = basename(parse_url($url, PHP_URL_PATH));
        
        // 如果无法从URL获取文件名，生成一个
        if (empty($fileName) || strpos($fileName, '.') === false) {
            $extension = $this->getFileExtension($url) ?: 'jpg';
            $fileName = 'image_' . date('YmdHis') . '_' . uniqid() . ".{$extension}";
        }
        
        return $fileName;
    }
    
    /**
     * 获取文件扩展名
     *
     * @param string $url
     * @return string|null
     */
    private function getFileExtension(string $url): ?string
    {
        $path = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        
        // 常见图片扩展名验证
        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        
        return in_array(strtolower($extension), $validExtensions) ? strtolower($extension) : null;
    }
    
    /**
     * 格式化字节大小
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
} 