/**
 * N11任务查询API
 * 用于查询N11平台的任务执行结果
 */
import { sendRequestViaBackground } from './api'

// N11任务查询响应接口
export interface N11TaskQueryResponse {
  taskId: number
  skus: {
    content: Array<{
      id: number
      taskId: number
      ownerId: number
      itemCode: string
      status: string
      sku: {
        categoryId: number
        stockCode: string
        description: string
        productMainId: string
        salePrice: number
        listPrice: number
        currencyType: string
        images: Array<{
          url: string
          order: number
        }>
        reasons?: string[]
        stock: number
      }
      reasons?: string[]
    }>
    pageable: {
      sort: any
      offset: number
      pageSize: number
      pageNumber: number
      paged: boolean
      unpaged: boolean
    }
    last: boolean
    totalElements: number
    totalPages: number
    sort: any
    first: boolean
    number: number
    numberOfElements: number
    size: number
    empty: boolean
  }
  createdDate: string
  modifiedDate: string
  status: string
}

/**
 * 查询N11任务详情
 * @param appKey N11应用密钥
 * @param appSecret N11应用秘钥
 * @param taskId 任务ID
 * @param page 页码，默认为0
 * @param size 每页大小，默认为1000
 * @returns 任务查询结果
 */
export const queryN11TaskDetails = async (
  appKey: string,
  appSecret: string,
  taskId: number,
  page: number = 0,
  size: number = 1000
): Promise<N11TaskQueryResponse> => {
  const url = "https://api.n11.com/ms/product/task-details/page-query"
  
  const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "appkey": appKey,
    "appsecret": appSecret
  }
  
  const payload = {
    taskId: taskId,
    pageable: {
      page: page,
      size: size
    }
  }

  try {
    const response = await sendRequestViaBackground({
      funName: 'queryN11TaskDetails',
      url,
      method: 'POST',
      headers,
      data: JSON.stringify(payload),
      auth: false,
      encrypto: false,
      timeout: 30000
    })

    console.log('N11任务查询响应:', response)
    return response as N11TaskQueryResponse

  } catch (error: any) {
    console.error('N11任务查询失败:', error)
    throw error
  }
}

/**
 * 处理N11查询结果，转换为更新数据格式
 * @param queryResult N11查询结果
 * @param taskDetails 任务详情列表
 * @returns 更新数据数组
 */
export const processN11QueryResult = (
  queryResult: N11TaskQueryResponse,
  taskDetails: Array<{ id: number; stock_code: string; goods_name: string }>
): Array<{
  detail_id: number
  status: number
  third_status: string
  third_result: string
  memo: string
}> => {
  const updates: Array<{
    detail_id: number
    status: number
    third_status: string
    third_result: string
    memo: string
  }> = []

  const skuResults = queryResult.skus?.content || []
  
  // 创建以stock_code为键的映射
  const skuResultMap = new Map<string, any>()
  skuResults.forEach(skuResult => {
    const stockCode = skuResult.sku?.stockCode
    if (stockCode) {
      skuResultMap.set(stockCode, skuResult)
    }
  })

  // 处理每个任务详情
  taskDetails.forEach(detail => {
    const skuResult = skuResultMap.get(detail.stock_code)
    
    if (skuResult) {
      const status = skuResult.status || ''
      const reasons = skuResult.reasons || []
      
      // 处理状态
      let newStatus = 1 // 默认成功
      let memo = ''
      
      if (status === 'FAIL') {
        newStatus = 3 // 失败
        if (reasons.length > 0) {
          let reason = reasons[0]
          // 替换土耳其语错误信息
          if (reason.includes('özellik alanı boş olamaz')) {
            reason = reason.replace('özellik alanı boş olamaz', '属性字段不能为空')
          }
          memo = reason
        }
      } else if (status === 'SUCCESS') {
        newStatus = 1 // 成功
        memo = '商品上传成功'
      } else {
        // 其他状态，可能还在处理中
        newStatus = 2 // 保持待查询状态
        memo = `状态: ${status}`
      }
      
      updates.push({
        detail_id: detail.id,
        status: newStatus,
        third_status: status,
        third_result: memo,
        memo: memo
      })
    } else {
      // 没有找到对应的SKU结果
      updates.push({
        detail_id: detail.id,
        status: 3,
        third_status: 'NOT_FOUND',
        third_result: '未找到对应的SKU结果',
        memo: 'N11结果匹配失败'
      })
    }
  })

  return updates
} 