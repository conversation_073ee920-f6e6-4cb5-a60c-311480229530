/**
 * N11产品删除模块
 * 专门用于通过RPC方式操作浏览器DOM删除N11产品
 * 
 * 参考文件:
 * - pageController.ts: RPC通信模式
 * - n11RejectedApi.ts: RPC调用模式
 */

// 删除操作结果接口
export interface N11DeleteResult {
  success: boolean
  message?: string
  details?: any
}

// 删除配置接口
export interface N11DeleteConfig {
  searchTimeout?: number    // 搜索超时时间 (默认10秒)
  dropdownTimeout?: number  // 下拉菜单超时时间 (默认5秒)
  modalTimeout?: number     // 确认弹窗超时时间 (默认5秒)
  deletionTimeout?: number  // 删除完成超时时间 (默认15秒)
  pollInterval?: number     // 轮询间隔 (默认500毫秒)
}

// 默认配置
const DEFAULT_CONFIG: Required<N11DeleteConfig> = {
  searchTimeout: 10000,
  dropdownTimeout: 5000,
  modalTimeout: 5000,
  deletionTimeout: 15000,
  pollInterval: 500
}


/**
 * 执行完整的N11产品删除流程
 * 注意：这个函数会被注入到页面中执行，所以需要包含所有依赖的函数
 */
async function executeN11ProductDeletion(stockCode: string, config: Required<N11DeleteConfig>): Promise<N11DeleteResult> {
  // 在页面执行上下文中定义所有需要的辅助函数
  
  /**
   * 设置输入框值的辅助函数
   */
  function setInputValue(element: HTMLInputElement, value: string): void {
    element.focus()
    element.value = ''
    element.value = value
    element.dispatchEvent(new Event('input', { bubbles: true }))
    element.dispatchEvent(new Event('change', { bubbles: true }))
    element.blur()
  }

  /**
   * 等待函数
   */
  function wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 轮询等待条件满足
   */
  async function waitForCondition(
    condition: () => boolean,
    timeout: number,
    interval: number = 100
  ): Promise<boolean> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      if (condition()) {
        return true
      }
      await wait(interval)
    }
    
    return false
  }

  /**
   * 第零步：点击分页器的"第一页"按钮，确保在第一页进行搜索
   */
  async function goToFirstPage(): Promise<void> {
    // 查找分页器中的"第一页"按钮
    const firstPageButton = document.querySelector('button img[src*="first-page"]')?.parentElement as HTMLButtonElement
    
    if (!firstPageButton) {
      console.log('未找到第一页按钮，可能已经在第一页或分页器不存在')
      return
    }
    
    // 检查按钮是否可点击（不是禁用状态）
    if (firstPageButton.disabled) {
      console.log('第一页按钮已禁用，说明当前已在第一页')
      return
    }
    
    console.log('找到第一页按钮，正在点击...')
    firstPageButton.click()
    await wait(1000) // 等待页面跳转和加载
  }

  /**
   * 第一步：填写stock_code输入框
   */
  async function fillStockCodeInput(stockCode: string): Promise<void> {
    const stockInput = document.querySelector('input#stock_code') as HTMLInputElement
    if (!stockInput) {
      throw new Error('未找到stock_code输入框')
    }
    
    setInputValue(stockInput, stockCode)
    await wait(500) // 等待输入处理
  }

  /**
   * 第二步：点击"Filtrele"按钮
   */
  async function clickFilterButton(): Promise<void> {
    // 多种方式查找Filtrele按钮
    let filterButton: HTMLButtonElement | null = null
    const buttonTexts: string[] = []
    
    // 方法1: 通过文本内容查找所有按钮
    const allButtons = document.querySelectorAll('button')
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i] as HTMLButtonElement
      const buttonText = button.textContent?.trim() || ''
      buttonTexts.push(`按钮${i}: "${buttonText}" (类名: ${button.className})`)
      
      if (buttonText.includes('Filtrele')) {
        filterButton = button
        break
      }
    }
    
    // 方法2: 通过CSS类选择器查找
    if (!filterButton) {
      const primaryButtons = document.querySelectorAll('button.btn.primary')
      for (let i = 0; i < primaryButtons.length; i++) {
        const button = primaryButtons[i] as HTMLButtonElement
        if (button.textContent?.includes('Filtrele')) {
          filterButton = button
          break
        }
      }
    }
    
    // 方法3: 通过button-container查找
    if (!filterButton) {
      const buttonContainers = document.querySelectorAll('.button-container')
      for (let i = 0; i < buttonContainers.length; i++) {
        const container = buttonContainers[i]
        const buttons = container.querySelectorAll('button')
        for (let j = 0; j < buttons.length; j++) {
          const button = buttons[j] as HTMLButtonElement
          if (button.textContent?.includes('Filtrele')) {
            filterButton = button
            break
          }
        }
        if (filterButton) break
      }
    }
    
    if (!filterButton) {
      const debugInfo = `页面上找到的按钮: ${buttonTexts.join(', ')}`
      throw new Error(`未找到Filtrele按钮。${debugInfo}`)
    }
    
    console.log(`找到Filtrele按钮，文本内容: "${filterButton.textContent?.trim()}"`)
    filterButton.click()
  }

  /**
   * 第三步：等待表格更新
   */
  async function waitForTableUpdate(timeout: number): Promise<void> {
    const tableUpdateSuccess = await waitForCondition(() => {
      const tableRows = document.querySelectorAll('table tbody tr')
      return tableRows.length > 0
    }, timeout)
    
    if (!tableUpdateSuccess) {
      throw new Error('搜索超时，表格未更新')
    }
    
    await wait(1000) // 等待表格完全加载
  }

  /**
   * 第四步：验证搜索结果
   */
  async function validateSearchResult(stockCode: string): Promise<HTMLTableRowElement> {
    const tableRows = document.querySelectorAll('table tbody tr')
    
    if (tableRows.length === 0) {
      throw new Error('搜索结果为空，未找到匹配的产品')
    }
    
    if (tableRows.length > 1) {
      throw new Error(`搜索结果不唯一，找到${tableRows.length}个产品`)
    }
    
    const firstRow = tableRows[0] as HTMLTableRowElement
    const fourthColumnCell = firstRow.querySelector('td:nth-child(4)')
    
    if (!fourthColumnCell || !fourthColumnCell.textContent?.includes(stockCode)) {
      throw new Error(`第四列不包含期望的stock_code: ${stockCode}`)
    }
    
    return firstRow
  }

  /**
   * 第五步：点击第一列的复选框
   */
  async function clickRowCheckbox(row: HTMLTableRowElement): Promise<void> {
    const checkbox = row.querySelector('td:first-child input[type="checkbox"]') as HTMLInputElement
    
    if (!checkbox) {
      throw new Error('未找到第一列的复选框')
    }
    
    checkbox.click()
    await wait(500) // 等待复选框状态更新
  }

  /**
   * 第六步：等待并点击下拉菜单中的"Sil"按钮
   */
  async function clickDeleteButton(timeout: number): Promise<void> {
    const dropdownSuccess = await waitForCondition(() => {
      const dropdownButtons = document.querySelectorAll('button')
      for (let i = 0; i < dropdownButtons.length; i++) {
        const button = dropdownButtons[i]
        if (button.textContent && button.textContent.trim() === 'Sil') {
          return true
        }
      }
      return false
    }, timeout)
    
    if (!dropdownSuccess) {
      throw new Error('下拉菜单中的Sil按钮未出现')
    }
    
    // 找到并点击Sil按钮
    const dropdownButtons = document.querySelectorAll('button')
    let silButton: HTMLButtonElement | null = null
    
    for (let i = 0; i < dropdownButtons.length; i++) {
      const button = dropdownButtons[i] as HTMLButtonElement
      if (button.textContent && button.textContent.trim() === 'Sil') {
        silButton = button
        break
      }
    }
    
    if (!silButton) {
      throw new Error('未找到Sil按钮')
    }
    
    silButton.click()
  }

  /**
   * 第七步：等待确认弹窗并点击"Ürünleri Sil"按钮
   */
  async function clickConfirmButton(timeout: number): Promise<void> {
    const modalSuccess = await waitForCondition(() => {
      const positiveButton = document.querySelector('#positive-button')
      return !!(positiveButton && positiveButton.textContent?.includes('Ürünleri Sil'))
    }, timeout)
    
    if (!modalSuccess) {
      throw new Error('确认弹窗未出现或未找到Ürünleri Sil按钮')
    }
    
    const positiveButton = document.querySelector('#positive-button') as HTMLButtonElement
    positiveButton.click()
  }

  /**
   * 第八步：轮询等待行消失，确认删除成功
   */
  async function waitForDeletionComplete(stockCode: string, timeout: number): Promise<void> {
    const deletionSuccess = await waitForCondition(() => {
      const updatedRows = document.querySelectorAll('table tbody tr')
      // 检查是否还有包含该stock_code的行
      for (let i = 0; i < updatedRows.length; i++) {
        const row = updatedRows[i]
        const fourthCol = row.querySelector('td:nth-child(4)')
        if (fourthCol && fourthCol.textContent?.includes(stockCode)) {
          return false // 还存在，删除未完成
        }
      }
      return true // 不存在了，删除成功
    }, timeout)
    
    if (!deletionSuccess) {
      throw new Error('删除操作超时，产品可能未被成功删除')
    }
  }

  // 主执行逻辑
  try {
    // 第零步：先回到第一页，确保搜索的准确性
    await goToFirstPage()
    
    // 第一步：填写stock_code输入框
    await fillStockCodeInput(stockCode)
    
    // 第二步：点击"Filtrele"按钮
    await clickFilterButton()
    
    // 第三步：等待表格更新
    await waitForTableUpdate(config.searchTimeout)
    
    // 第四步：验证搜索结果
    const targetRow = await validateSearchResult(stockCode)
    
    // 第五步：点击第一列的复选框
    await clickRowCheckbox(targetRow)
    
    // 第六步：等待并点击下拉菜单中的"Sil"按钮
    await clickDeleteButton(config.dropdownTimeout)
    
    // 第七步：等待确认弹窗并点击"Ürünleri Sil"按钮
    await clickConfirmButton(config.modalTimeout)
    
    // 第八步：轮询等待行消失，确认删除成功
    await waitForDeletionComplete(stockCode, config.deletionTimeout)
    
    return {
      success: true,
      message: `产品 ${stockCode} 删除成功`
    }
    
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : String(error),
      details: error
    }
  }
}

/**
 * 通过RPC方式删除N11产品
 * 
 * @param stockCode 产品的stock_code
 * @param config 可选的配置参数
 * @returns Promise<N11DeleteResult> 删除操作结果
 */
export async function deleteN11ProductByStockCode(
  stockCode: string,
  config: N11DeleteConfig = {}
): Promise<N11DeleteResult> {
  try {
    console.log(`开始通过RPC方式删除N11产品: ${stockCode}`)
    
    // 检查RPC环境支持
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      throw new Error('RPC环境不支持：未检测到Chrome扩展环境')
    }

    // 合并配置
    const finalConfig = { ...DEFAULT_CONFIG, ...config }
    
    // 通过background发送RPC请求
    const result = await new Promise<N11DeleteResult>((resolve, reject) => {
      chrome.runtime.sendMessage({
        funType: 'rpcPageControl',
        action: 'deleteN11Product',
        stockCode: stockCode,
        config: finalConfig
      }, (response: any) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError)
          return
        }
        
        if (response && typeof response.success === 'boolean') {
          resolve(response)
        } else if (response && response.success === false) {
          reject(new Error(response.message || 'RPC删除产品失败'))
        } else {
          reject(new Error('RPC返回数据格式不正确'))
        }
      })
    })

    console.log(`RPC方式删除N11产品结果:`, result)
    return result

  } catch (error) {
    console.error('RPC方式删除N11产品失败:', error)
    return {
      success: false,
      message: `删除操作异常: ${error instanceof Error ? error.message : String(error)}`,
      details: error
    }
  }
}

/**
 * 批量删除N11产品
 * 
 * @param stockCodes 产品stock_code数组
 * @param config 可选的配置参数
 * @returns Promise<N11DeleteResult[]> 删除操作结果数组
 */
export async function batchDeleteN11Products(
  stockCodes: string[],
  config: N11DeleteConfig = {}
): Promise<N11DeleteResult[]> {
  const results: N11DeleteResult[] = []
  
  // 简单的等待函数
  const wait = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  for (const stockCode of stockCodes) {
    try {
      const result = await deleteN11ProductByStockCode(stockCode, config)
      results.push(result)
      
      // 如果删除失败，可以选择继续或停止
      if (!result.success) {
        console.warn(`删除产品 ${stockCode} 失败:`, result.message)
      }
      
      // 批量操作间隔，避免过于频繁
      await wait(1000)
      
    } catch (error) {
      results.push({
        success: false,
        message: `删除产品 ${stockCode} 时发生异常: ${error instanceof Error ? error.message : String(error)}`,
        details: error
      })
    }
  }
  
  return results
}

// 导出DOM操作函数供background script使用
export { executeN11ProductDeletion }