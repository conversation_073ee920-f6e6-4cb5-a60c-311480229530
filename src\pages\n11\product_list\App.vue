<template>
    <div class="chrome_container">
        <h1>N11 Product List</h1>
        <div class="debug-info" v-if="debugMode">
            <!-- 用户登录状态显示 -->
            <div style="margin-bottom: 10px; padding: 8px; background-color: #e8f5e8; border-radius: 4px; border-left: 4px solid #4caf50;">
                <p style="margin: 2px 0; font-size: 12px; font-weight: bold;">用户登录状态:</p>
                <p v-if="userInfo" style="margin: 2px 0; font-size: 12px; color: #2e7d32;">
                    ✓ 已登录 - {{ userInfo.phone }} 
                    <span v-if="userInfo.isVip" style="color: #ff9800;">(VIP)</span>
                    <span v-if="userInfo.isAdmin" style="color: #f44336;">(管理员)</span>
                </p>
                <p v-else style="margin: 2px 0; font-size: 12px; color: #d32f2f;">
                    ✗ 未登录 - 请先登录后使用功能
                </p>
            </div>
            
            <p>监听器状态: {{ isObserving ? '运行中' : '未运行' }}</p>
            <p>第六个按钮状态: {{ sixthButtonActive ? '激活' : '未激活' }}</p>
            <p>重新上传按钮状态: {{ reuploadButtonExists ? '已添加' : '未添加' }}</p>
            <p>数据获取方式: {{ usingRPCMode ? 'RPC模式' : 'API模式（自动选择）' }}</p>
            <p>调试按钮状态: {{ debugButtonExists ? '已添加' : '未添加' }}</p>
            
            <!-- 执行状态显示 -->
            <div style="margin-top: 10px; padding: 8px; background-color: #e3f2fd; border-radius: 4px;">
                <p style="margin: 2px 0; font-size: 12px;">执行状态: {{ executionState.isRunning ? '运行中' : '空闲' }}</p>
                <p v-if="executionState.lastError" style="margin: 2px 0; font-size: 12px; color: #d32f2f;">
                    最后错误: {{ executionState.lastError }}
                </p>
                <p v-if="executionState.retryCount > 0" style="margin: 2px 0; font-size: 12px; color: #f57c00;">
                    重试次数: {{ executionState.retryCount }}/{{ executionState.maxRetries }}
                </p>
                <button v-if="executionState.isRunning" @click="stopExecution" 
                        style="margin-top: 5px; padding: 3px 8px; font-size: 12px; background-color: #f44336; color: white; border: none; border-radius: 3px;">
                    强制停止
                </button>
                <button @click="clearExecutionState"
                        style="margin-top: 5px; margin-left: 5px; padding: 3px 8px; font-size: 12px;">
                    清除状态
                </button>
                <button @click="testProgressUpdate"
                        style="margin-top: 5px; margin-left: 5px; padding: 3px 8px; font-size: 12px; background-color: #4caf50; color: white; border: none; border-radius: 3px;">
                    测试进度更新
                </button>
            </div>
            
            <div style="margin-top: 10px; padding: 8px; background-color: #f9f9f9; border-radius: 4px;">
                <label style="display: flex; align-items: center; font-size: 12px; margin-bottom: 5px;">
                    <input type="checkbox" v-model="enableDebugButton" style="margin-right: 5px;">
                    启用调试按钮（在重新上传按钮右侧添加调试按钮）
                </label>
            </div>
            
            <div style="margin-top: 10px; padding: 8px; background-color: #f9f9f9; border-radius: 4px;">
                <label style="display: flex; align-items: center; font-size: 12px;">
                    <input type="checkbox" v-model="enableRPCStatusCheck" style="margin-right: 5px;">
                    启用RPC状态检测（可选，不影响系统运行）
                </label>
                
                <template v-if="enableRPCStatusCheck">
                    <p style="margin: 5px 0;">RPC支持状态: {{ rpcStatus ? (rpcStatus.supported ? '支持' : '不支持') : '检测中' }}</p>
                    <p v-if="rpcStatus && !rpcStatus.supported" style="color: #666; font-size: 11px; margin: 5px 0;">
                        备注: {{ rpcStatus.message || 'RPC不可用，将使用API模式' }}
                    </p>
                    <button @click="testRPCEnv" style="margin-top: 5px; padding: 3px 8px; font-size: 12px;">测试RPC环境</button>
                    <button @click="forceCleanupDebuggers" style="margin-top: 5px; margin-left: 5px; padding: 3px 8px; font-size: 12px; background-color: #ff9800; color: white; border: none; border-radius: 3px;">
                        清理调试器
                    </button>
                </template>
            </div>
        </div>
        
        <!-- 新的详细进度对话框 -->
        <ReuploadProgressDialog
            v-model:visible="showProgressDialog"
            :current-step="progressCurrentStep"
            :fetch-progress="fetchProgress"
            :upload-progress="uploadProgress"
            :can-cancel="progressCanCancel"
            :has-error="progressHasError"
            :error-message="progressErrorMessage"
            :is-completed="progressIsCompleted"
            @cancel="handleProgressCancel"
            @close="handleProgressClose"
        />

        <!-- 保留原有的简单进度显示作为备用 -->
        <div v-if="isProcessing && !showProgressDialog" class="progress-overlay">
            <div class="progress-dialog">
                <h3>{{ currentStep }}</h3>
                <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
                </div>
                <p class="progress-message">{{ progressMessage }}</p>
                <div class="progress-percent">{{ progressPercent }}%</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { getAllRejectedProductsWithFallback, getRPCStatus } from '@/utils/n11/n11RejectedApi'
import { batchSaveRejectedProducts } from '@/utils/n11/rejectedProductApi'
import { processRejectedProductReupload, processRejectedProductReuploadDetailed, type ProductInfo, type ProcessedProduct } from '@/utils/n11/rejectedTaskProcessor'
import ReuploadProgressDialog, { type FetchProgress, type UploadProgress } from './components/ReuploadProgressDialog.vue'
import { debugSetPageSize100, getCurrentPageSize, type DebugOperationResult } from '@/utils/rpc/debugOperations'
import { initializeRPC, cleanupAllRPCResources, DebuggerManager } from '@/utils/rpc/index'
import { cleanupAllNetworkListeners } from '@/utils/rpc/networkListener'
import { 
  userInfo as globalUserInfo,
  checkUserLoginStatus,
  initUserStore,
  cleanupUserStore,
  onUserInfoChange
} from '@/utils/userStore';

// 响应式状态
const isObserving = ref(false)
const sixthButtonActive = ref(false)
const reuploadButtonExists = ref(false)
const debugButtonExists = ref(false) // 调试按钮存在状态
const enableDebugButton = ref(false) // 调试按钮开关
const debugMode = ref(true) // 可在开发时设为true
const enableRPCStatusCheck = ref(false) // RPC状态检测默认关闭，避免影响系统运行

// 用户信息相关状态
const userInfo = ref<any>(null)
let userInfoChangeCleanup: (() => void) | null = null
let isCheckingUserLogin = ref(false) // 防止重复检查登录状态
let lastLoginCheckTime = 0 // 防抖时间戳
const LOGIN_CHECK_DEBOUNCE_TIME = 2000 // 2秒防抖

// 处理状态
const isProcessing = ref(false)
const currentStep = ref('')
const progressPercent = ref(0)
const progressMessage = ref('')

// 新的详细进度UI状态
const showProgressDialog = ref(false)
const progressCurrentStep = ref<'fetch' | 'upload'>('fetch')
const progressCanCancel = ref(true)
const progressHasError = ref(false)
const progressErrorMessage = ref('')
const progressIsCompleted = ref(false)

// Step 1 数据获取进度
const fetchProgress = ref<FetchProgress>({
  mode: 'rpc',
  currentPage: 0,
  totalPages: 0,
  currentBatch: 0,
  totalProducts: 0,
  networkStatus: 'idle',
  speed: 0,
  estimatedTime: 0
})

// Step 2 商品上传进度
const uploadProgress = ref<UploadProgress>({
  currentIndex: 0,
  totalCount: 0,
  currentProduct: null,
  currentProcessStep: 1,
  stepMessage: '',
  successCount: 0,
  failureCount: 0,
  processHistory: [],
  speed: 0,
  estimatedTime: 0
})

// 执行状态管理
const executionState = ref({
  isRunning: false,
  shouldStop: false,
  lastError: null as string | null,
  retryCount: 0,
  maxRetries: 3
})

// RPC状态
const rpcStatus = ref<any>(null)
const usingRPCMode = ref(false)

// MutationObserver实例
let observer: MutationObserver | null = null

// 重新上传按钮的标识
const REUPLOAD_BUTTON_ID = 'n11-reupload-button'
// 调试按钮的标识
const DEBUG_BUTTON_ID = 'n11-debug-button'

// 获取插件名称
const getPluginName = (): Promise<string> => {
  return new Promise((resolve) => {
    chrome.runtime.getManifest ? 
      resolve(chrome.runtime.getManifest().name) : 
      resolve('跨境蜂');
  });
};

// 显示登录提示
const showLoginNotification = async () => {
  const pluginName = await getPluginName();
  ElNotification({
    title: pluginName,
    message: '请先登录',
    type: 'warning',
    duration: 5000,
    position: 'top-right'
  });
};

/**
 * 检查用户登录状态
 */
const checkAndHandleUserLogin = async (showErrorMessage = true): Promise<boolean> => {
  try {
    // 检查登录状态
    const loginStatus = await checkUserLoginStatus();
    if (!loginStatus.isLoggedIn) {
      showLoginNotification();
      return false;
    }

    // 使用全局用户信息
    userInfo.value = {
      name: globalUserInfo.phone,
      phone: globalUserInfo.phone,
      isVip: globalUserInfo.isVip,
      isAdmin: globalUserInfo.isAdmin,
      expiryDate: globalUserInfo.expiryDate
    };

    console.log('用户登录检查通过:', {
      phone: userInfo.value.phone,
      isVip: userInfo.value.isVip,
      isAdmin: userInfo.value.isAdmin
    });

    return true;
  } catch (error) {
    console.error('检查用户登录状态失败:', error);
    
    // 只在允许显示错误消息时才显示
    if (showErrorMessage) {
      ElMessage.error('检查用户登录状态失败');
    }
    
    return false;
  }
};

/**
 * 检查第六个按钮是否具有active类
 */
const checkSixthButtonActive = (): boolean => {
    try {
        const tabManager = document.querySelector('.tabManager')
        if (!tabManager) {
            console.log('未找到 .tabManager 容器')
            return false
        }

        const buttons = tabManager.querySelectorAll('button, .tab-button, [role="tab"]')
        if (buttons.length < 6) {
            console.log(`按钮数量不足，当前数量: ${buttons.length}`)
            return false
        }

        const sixthButton = buttons[5] as HTMLElement // 索引为5的是第六个按钮
        const hasActive = sixthButton.classList.contains('active')
        
        console.log(`第六个按钮active状态: ${hasActive}`)
        return hasActive
    } catch (error) {
        console.error('检查第六个按钮状态时发生错误:', error)
        return false
    }
}

/**
 * 添加重新上传按钮
 */
const addReuploadButton = (): void => {
    try {
        // 检查按钮是否已存在
        if (document.getElementById(REUPLOAD_BUTTON_ID)) {
            console.log('重新上传按钮已存在，跳过添加')
            return
        }

        // 查找目标容器 - 优先查找Excel导入按钮附近的容器
        let container = document.querySelector('.table-top-buttons')
        
        if (!container) {
            // 查找Excel导入按钮的父容器
            const excelButton = document.querySelector('button[data-v-70104054]') ||
                               document.querySelector('button:contains("Excel ile İndir")') ||
                               document.querySelector('button:contains("Excel")') ||
                               document.querySelector('button:contains("导出")') ||
                               document.querySelector('button:contains("下载")')
            
            if (excelButton) {
                container = excelButton.parentElement
                console.log('找到Excel按钮容器:', container)
            }
        }
        
        if (!container) {
            // 备选容器查找
            container = document.querySelector('.toolbar') ||
                       document.querySelector('.action-buttons') ||
                       document.querySelector('[class*="button"]') ||
                       document.querySelector('.btn-group')
        }
        
        if (!container) {
            console.log('未找到合适的按钮容器')
            return
        }
        
        console.log('使用按钮容器:', container)

        // 创建重新上传按钮
        const reuploadButton = document.createElement('button')
        reuploadButton.id = REUPLOAD_BUTTON_ID
        reuploadButton.className = 'btn btn-primary reupload-btn'
        reuploadButton.textContent = '重新上传'
        reuploadButton.type = 'button'
        
        // 添加点击事件
        reuploadButton.addEventListener('click', handleReuploadClick)
        
        // 插入按钮到容器
        container.appendChild(reuploadButton)
        
        reuploadButtonExists.value = true
        console.log('重新上传按钮已添加')
    } catch (error) {
        console.error('添加重新上传按钮时发生错误:', error)
    }
}

/**
 * 移除重新上传按钮
 */
const removeReuploadButton = (): void => {
    try {
        const existingButton = document.getElementById(REUPLOAD_BUTTON_ID)
        if (existingButton) {
            existingButton.removeEventListener('click', handleReuploadClick)
            existingButton.remove()
            reuploadButtonExists.value = false
            console.log('重新上传按钮已移除')
        }
    } catch (error) {
        console.error('移除重新上传按钮时发生错误:', error)
    }
}

/**
 * 重新上传按钮点击事件处理器
 */
const handleReuploadClick = async (event: Event): Promise<void> => {
    event.preventDefault()
    console.log('重新上传按钮被点击')
    
    // 首先检查用户登录状态
    const isLoggedIn = await checkAndHandleUserLogin()
    if (!isLoggedIn) {
        console.log('用户未登录，终止重新上传操作')
        return
    }
    
    // 检查是否已经在执行中
    if (executionState.value.isRunning) {
        ElMessage.warning('系统正在执行中，请等待完成后再试')
        return
    }
    
    // 重置执行状态
    executionState.value = {
        isRunning: false,
        shouldStop: false,
        lastError: null,
        retryCount: 0,
        maxRetries: 3
    }
    
    await ElMessageBox.confirm(
      `确定要开始重新上传被拒绝的商品吗？`,
      '确认重新上传',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    try {
        await startReuploadProcess()
    } catch (error) {
        console.error('重新上传过程出错:', error)
        
        // 检查是否是debugger冲突错误
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        
        if (errorMessage.includes('Another debugger is already attached') || 
            errorMessage.includes('already attached')) {
            
            ElMessage.error('检测到调试器冲突，正在尝试自动恢复...')
            
            // 尝试清理后重试
            try {
                await cleanupAndRetry()
            } catch (retryError) {
                console.error('重试失败:', retryError)
                ElMessage.error(`重新上传失败: ${retryError instanceof Error ? retryError.message : '重试失败'}`)
                executionState.value.isRunning = false
            }
        } else {
            ElMessage.error(`重新上传失败: ${errorMessage}`)
            executionState.value.isRunning = false
        }
    }
}

/**
 * 添加调试按钮
 */
const addDebugButton = (): void => {
    try {
        // 检查按钮是否已存在
        if (document.getElementById(DEBUG_BUTTON_ID)) {
            console.log('调试按钮已存在，跳过添加')
            return
        }

        // 查找重新上传按钮作为参考点
        const reuploadButton = document.getElementById(REUPLOAD_BUTTON_ID)
        
        if (!reuploadButton) {
            console.log('未找到重新上传按钮，无法添加调试按钮')
            return
        }

        const container = reuploadButton.parentElement
        if (!container) {
            console.log('未找到重新上传按钮的父容器')
            return
        }
        
        console.log('使用调试按钮容器:', container)

        // 创建调试按钮
        const debugButton = document.createElement('button')
        debugButton.id = DEBUG_BUTTON_ID
        debugButton.className = 'btn btn-secondary debug-btn'
        debugButton.textContent = '调试'
        debugButton.type = 'button'
        debugButton.title = '调试功能：测试页面大小切换到100条'
        
        // 添加点击事件
        debugButton.addEventListener('click', handleDebugClick)
        
        // 插入按钮到重新上传按钮之后
        if (reuploadButton.nextSibling) {
            container.insertBefore(debugButton, reuploadButton.nextSibling)
        } else {
            container.appendChild(debugButton)
        }
        
        debugButtonExists.value = true
        console.log('调试按钮已添加')
    } catch (error) {
        console.error('添加调试按钮时发生错误:', error)
    }
}

/**
 * 移除调试按钮
 */
const removeDebugButton = (): void => {
    try {
        const existingButton = document.getElementById(DEBUG_BUTTON_ID)
        if (existingButton) {
            existingButton.removeEventListener('click', handleDebugClick)
            existingButton.remove()
            debugButtonExists.value = false
            console.log('调试按钮已移除')
        }
    } catch (error) {
        console.error('移除调试按钮时发生错误:', error)
    }
}

/**
 * 调试按钮点击事件处理器
 */
const handleDebugClick = async (event: Event): Promise<void> => {
    event.preventDefault()
    console.log('调试按钮被点击')
    
    // 首先检查用户登录状态
    const isLoggedIn = await checkAndHandleUserLogin()
    if (!isLoggedIn) {
        console.log('用户未登录，终止调试操作')
        return
    }
    
    try {
        // 获取当前页面大小
        const currentPageSize = getCurrentPageSize()
        console.log('当前页面大小:', currentPageSize)
        
        // 显示选择对话框
        const { value: method } = await ElMessageBox.prompt(
            `当前页面大小: ${currentPageSize.pageSize}条/页\n\n请选择调试方式:\n1. background - 通过Background调用executeScriptInTab\n2. direct - 直接操作DOM\n3. both - 两种方式都测试`,
            '调试页面大小切换',
            {
                confirmButtonText: '执行',
                cancelButtonText: '取消',
                inputValue: 'both',
                inputValidator: (value: string) => {
                    if (!['background', 'direct', 'both'].includes(value)) {
                        return '请输入 background、direct 或 both'
                    }
                    return true
                }
            }
        )
        
        if (!method) return
        
        console.log(`开始执行调试，方式: ${method}`)
        
        // 执行调试功能
        const results = await debugSetPageSize100(method as 'background' | 'direct' | 'both')
        
        // 显示结果
        let resultMessage = '调试执行完成:\n\n'
        results.forEach((result, index) => {
            resultMessage += `方式${index + 1} (${result.method}):\n`
            resultMessage += `状态: ${result.success ? '成功' : '失败'}\n`
            resultMessage += `消息: ${result.message}\n\n`
        })
        
        // 再次获取页面大小验证结果
        const newPageSize = getCurrentPageSize()
        resultMessage += `执行后页面大小: ${newPageSize.pageSize}条/页`
        
        alert(resultMessage)
        
        console.log('调试结果:', results)
        console.log('执行后页面大小:', newPageSize)
        
    } catch (error) {
        console.error('调试功能执行出错:', error)
        if (error !== 'cancel') { // 用户取消不显示错误
            alert(`调试功能失败: ${error instanceof Error ? error.message : '未知错误'}`)
        }
    }
}

/**
 * 处理DOM变化
 */
const handleDomChange = (): void => {
    try {
        const currentSixthButtonActive = checkSixthButtonActive()
        const currentReuploadButtonExists = !!document.getElementById(REUPLOAD_BUTTON_ID)
        const currentDebugButtonExists = !!document.getElementById(DEBUG_BUTTON_ID)
        
        console.log(`DOM变化检测 - 第六个按钮active: ${currentSixthButtonActive}, 重新上传按钮存在: ${currentReuploadButtonExists}, 调试按钮存在: ${currentDebugButtonExists}`)
        
        // 更新状态
        sixthButtonActive.value = currentSixthButtonActive
        reuploadButtonExists.value = currentReuploadButtonExists
        debugButtonExists.value = currentDebugButtonExists
        
        // 根据状态添加或移除重新上传按钮
        if (currentSixthButtonActive && !currentReuploadButtonExists) {
            console.log('需要添加重新上传按钮')
            addReuploadButton()
        } else if (!currentSixthButtonActive && currentReuploadButtonExists) {
            console.log('需要移除重新上传按钮')
            removeReuploadButton()
        }
        
        // 根据状态和开关添加或移除调试按钮
        if (enableDebugButton.value && currentReuploadButtonExists && !currentDebugButtonExists) {
            console.log('需要添加调试按钮')
            // 延迟添加，确保重新上传按钮已完全渲染
            setTimeout(addDebugButton, 100)
        } else if ((!enableDebugButton.value || !currentReuploadButtonExists) && currentDebugButtonExists) {
            console.log('需要移除调试按钮')
            removeDebugButton()
        }
    } catch (error) {
        console.error('处理DOM变化时发生错误:', error)
    }
}

/**
 * 设置MutationObserver
 */
const setupMutationObserver = (): void => {
    try {
        // 清理现有观察器
        if (observer) {
            observer.disconnect()
        }

        // 创建新的观察器
        observer = new MutationObserver((mutations) => {
            let shouldCheck = false
            
            // 检查是否有相关的DOM变化
            for (const mutation of mutations) {
                if (mutation.type === 'childList' || 
                    mutation.type === 'attributes') {
                    
                    const target = mutation.target as Element
                    
                    // 检查变化是否涉及tabManager或其子元素
                    if (target.closest('.tabManager') || 
                        target.classList.contains('tabManager') ||
                        target.querySelector('.tabManager')) {
                        shouldCheck = true
                        console.log('检测到tabManager DOM变化:', mutation.type, target)
                        break
                    }
                    
                    // 也检查是否是按钮的class变化
                    if (mutation.type === 'attributes' && 
                        mutation.attributeName === 'class' &&
                        target.tagName === 'BUTTON') {
                        const tabManager = target.closest('.tabManager')
                        if (tabManager) {
                            shouldCheck = true
                            console.log('检测到tabManager内按钮class变化:', target)
                            break
                        }
                    }
                }
            }
            
            if (shouldCheck) {
                console.log('触发DOM变化处理')
                // 使用setTimeout避免频繁触发
                setTimeout(handleDomChange, 50)
            }
        })

        // 开始观察
        const targetElement = document.body
        observer.observe(targetElement, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class'] // 只监听class属性变化
        })

        isObserving.value = true
        console.log('MutationObserver已启动')
        
        // 初始检查
        handleDomChange()
    } catch (error) {
        console.error('设置MutationObserver时发生错误:', error)
    }
}

/**
 * 清理MutationObserver
 */
const cleanupObserver = (): void => {
    try {
        if (observer) {
            observer.disconnect()
            observer = null
            isObserving.value = false
            console.log('MutationObserver已清理')
        }
        
        // 清理添加的按钮
        removeReuploadButton()
        removeDebugButton()
    } catch (error) {
        console.error('清理MutationObserver时发生错误:', error)
    }
}

/**
 * 清理并重试
 */
const cleanupAndRetry = async (): Promise<void> => {
    try {
        executionState.value.retryCount++
        
        if (executionState.value.retryCount > executionState.value.maxRetries) {
            throw new Error('已达到最大重试次数，请刷新页面后重试')
        }
        
        console.log(`开始第 ${executionState.value.retryCount} 次重试清理...`)
        
        // 清理所有网络监听器
        await cleanupAllNetworkListeners()
        
        // 等待一段时间确保清理完成
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        console.log('清理完成，开始重试...')
        
        // 重新执行上传流程
        await startReuploadProcess()
        
    } catch (error) {
        console.error('清理重试失败:', error)
        executionState.value.isRunning = false
        throw error
    }
}

/**
 * 处理进度对话框取消事件
 */
const handleProgressCancel = (): void => {
    executionState.value.shouldStop = true
    progressCanCancel.value = false
}

/**
 * 处理进度对话框关闭事件
 */
const handleProgressClose = (): void => {
    showProgressDialog.value = false
    // 重置所有进度状态
    progressCurrentStep.value = 'fetch'
    progressCanCancel.value = true
    progressHasError.value = false
    progressErrorMessage.value = ''
    progressIsCompleted.value = false

    // 重置进度数据
    fetchProgress.value = {
        mode: 'rpc',
        currentPage: 0,
        totalPages: 0,
        currentBatch: 0,
        totalProducts: 0,
        networkStatus: 'idle',
        speed: 0,
        estimatedTime: 0
    }

    uploadProgress.value = {
        currentIndex: 0,
        totalCount: 0,
        currentProduct: null,
        currentProcessStep: 1,
        stepMessage: '',
        successCount: 0,
        failureCount: 0,
        processHistory: [],
        speed: 0,
        estimatedTime: 0
    }
}

/**
 * 开始重新上传处理流程（新版本，使用详细进度UI）
 */
const startReuploadProcess = async (): Promise<void> => {
    // 检查执行状态
    if (executionState.value.shouldStop) {
        console.log('收到停止信号，终止执行')
        executionState.value.isRunning = false
        return
    }

    executionState.value.isRunning = true
    isProcessing.value = true

    // 显示新的详细进度对话框
    showProgressDialog.value = true
    progressCurrentStep.value = 'fetch'
    progressCanCancel.value = true
    progressHasError.value = false
    progressIsCompleted.value = false

    try {
        console.log('开始重新上传流程，使用详细进度UI')

        // 第一步：获取并保存被拒绝商品数据
        progressCurrentStep.value = 'fetch'
        fetchProgress.value.mode = 'rpc'
        fetchProgress.value.networkStatus = 'requesting'

        const saveResult = await fetchAndSaveRejectedProductsDetailed()

        if (saveResult.savedCount === 0) {
            progressHasError.value = true
            progressErrorMessage.value = '没有找到被拒绝的商品数据'
            ElMessage.error('没有找到被拒绝的商品数据')
            return
        }
        return;
        // 第二步：执行重新上传流程
        progressCurrentStep.value = 'upload'
        uploadProgress.value.totalCount = saveResult.savedCount

        const uploadResult = await processRejectedProductReuploadDetailed({
            onDetailedProgress: (progress) => {
                // 检查是否需要停止
                if (executionState.value.shouldStop) {
                    throw new Error('用户取消操作')
                }

                // 更新上传进度
                uploadProgress.value = {
                    currentIndex: progress.currentIndex,
                    totalCount: progress.totalCount,
                    currentProduct: progress.currentProduct,
                    currentProcessStep: progress.currentProcessStep,
                    stepMessage: progress.stepMessage,
                    successCount: progress.successCount,
                    failureCount: progress.failureCount,
                    processHistory: progress.processHistory,
                    speed: progress.speed,
                    estimatedTime: progress.estimatedTime
                }
            },
            onComplete: (successCount: number, failureCount: number) => {
                console.log(`处理完成: 成功 ${successCount} 个，失败 ${failureCount} 个`)
                progressIsCompleted.value = true
                progressCanCancel.value = false
            },
            onError: (error) => {
                progressHasError.value = true
                progressErrorMessage.value = error.message
            }
        })

        // 完成
        progressIsCompleted.value = true
        progressCanCancel.value = false

        ElMessage.success(`重新上传完成！处理商品数量: ${uploadResult.processedCount}，成功: ${uploadResult.successCount}，失败: ${uploadResult.failureCount}`)

    } catch (error) {
        console.error('重新上传流程失败:', error)

        // 记录错误
        executionState.value.lastError = error instanceof Error ? error.message : '未知错误'
        progressHasError.value = true
        progressErrorMessage.value = executionState.value.lastError
        progressCanCancel.value = false

        // 检查是否是调试器相关错误
        const errorMessage = executionState.value.lastError || ''
        if (errorMessage.includes('Another debugger is already attached') ||
            errorMessage.includes('already attached') ||
            errorMessage.includes('debugger')) {
            console.log('检测到调试器相关错误，错误已记录但不会重复执行')
        }

        throw error
    } finally {
        executionState.value.isRunning = false
        isProcessing.value = false
        currentStep.value = ''
        progressPercent.value = 0
        progressMessage.value = ''
    }
}

/**
 * 获取并保存被拒绝商品数据
 */
const fetchAndSaveRejectedProducts = async (): Promise<{ savedCount: number; totalCount: number }> => {
    try {
        progressMessage.value = '正在获取被拒绝商品列表...'
        
        // 获取所有被拒绝商品，启用强制清理模式
        const allProducts = await getAllRejectedProductsWithFallback((current: number, total: number) => {
            // 检查是否需要停止
            if (executionState.value.shouldStop) {
                throw new Error('用户取消操作')
            }
            
            // 简化进度显示，不区分RPC/API模式
            progressMessage.value = `正在获取商品数据 ${current}/${total} 页...`
            progressPercent.value = 10 + Math.round((current / total) * 30)
            
            // 检测实际使用的模式（用于调试显示）
            if (current === 1 && total > 0) {
                // 这里可以根据实际情况判断使用的模式，但不影响功能
                console.log('数据获取已开始，系统将自动选择最佳方式')
            }
        }, true) //强制使用RPC
        
        if (allProducts.length === 0) {
            return { savedCount: 0, totalCount: 0 }
        }
        
        console.log("allProducts", allProducts);
        
        progressMessage.value = '正在保存商品数据到后端...'
        
        let totalSaved = 0
        
        // 每500条保存一次
        const batchSize = 500
        for (let i = 0; i < allProducts.length; i += batchSize) {
            const batch = allProducts.slice(i, i + batchSize)
            
            try {
                const saveResult = await batchSaveRejectedProducts(batch)
                totalSaved += saveResult.inserted_count
                
                progressMessage.value = `已保存 ${Math.min(i + batchSize, allProducts.length)}/${allProducts.length} 条商品数据`
                progressPercent.value = 40 + Math.round(((i + batchSize) / allProducts.length) * 10)
            } catch (error) {
                console.error('保存批次数据失败:', error)
                // 继续处理下一批
            }
        }
        
        console.log(`成功保存 ${totalSaved} 条被拒绝商品数据`)
        return { savedCount: totalSaved, totalCount: allProducts.length }

    } catch (error) {
        console.error('获取和保存被拒绝商品数据失败:', error)
        throw error
    }
}

/**
 * 获取并保存被拒绝商品数据（详细版本，支持详细进度更新）
 */
const fetchAndSaveRejectedProductsDetailed = async (): Promise<{ savedCount: number; totalCount: number }> => {
    try {
        // 初始化进度状态
        Object.assign(fetchProgress.value, {
            networkStatus: 'requesting' as const,
            currentPage: 0,
            totalPages: 0,
            currentBatch: 0,
            totalProducts: 0,
            mode: 'rpc' as const,
            speed: 0,
            estimatedTime: 0
        })
        
        // 设置开始时间
        const startTime = Date.now()
        console.log('开始获取被拒绝商品数据，开始时间:', new Date(startTime).toLocaleTimeString())

        // 获取所有被拒绝商品，启用强制清理模式
        const allProducts = await getAllRejectedProductsWithFallback((current: number, total: number) => {
            console.log(`[Content Script] 数据获取进度回调: ${current}/${total}`)
            console.log(`[Content Script] 当前 fetchProgress 状态:`, {
                currentPage: fetchProgress.value.currentPage,
                totalPages: fetchProgress.value.totalPages,
                networkStatus: fetchProgress.value.networkStatus
            })

            // 检查是否需要停止
            if (executionState.value.shouldStop) {
                throw new Error('用户取消操作')
            }

            // 计算速度和预估时间
            const elapsedTime = Date.now() - startTime
            const elapsedMinutes = elapsedTime / (1000 * 60) // 转换为分钟
            const speed = current > 0 && elapsedMinutes > 0 ? (current / elapsedMinutes) : 0 // 页/分钟
            const remainingPages = total - current
            const estimatedTime = speed > 0 && remainingPages > 0 ? (remainingPages / speed) * 60 : 0 // 剩余时间（秒）

            // 更新详细进度 - 使用 Object.assign 确保响应式更新
            Object.assign(fetchProgress.value, {
                currentPage: current,
                totalPages: total,
                currentBatch: Math.ceil(current / 5), // 每5页为一批
                networkStatus: 'requesting' as const,
                speed: Number(speed.toFixed(1)),
                estimatedTime: Math.round(estimatedTime)
            })

            console.log(`[Content Script] 进度更新完成: 当前页=${current}, 总页数=${total}, 速度=${speed.toFixed(1)}页/分钟, 预估剩余时间=${Math.round(estimatedTime)}秒`)
            console.log(`[Content Script] 更新后 fetchProgress 状态:`, {
                currentPage: fetchProgress.value.currentPage,
                totalPages: fetchProgress.value.totalPages,
                networkStatus: fetchProgress.value.networkStatus,
                speed: fetchProgress.value.speed
            })
        }, true) // 强制使用RPC

        if (allProducts.length === 0) {
            Object.assign(fetchProgress.value, {
                networkStatus: 'success' as const,
                totalProducts: 0
            })
            console.log('没有找到被拒绝的商品')
            return { savedCount: 0, totalCount: 0 }
        }

        // 更新最终状态
        Object.assign(fetchProgress.value, {
            totalProducts: allProducts.length,
            networkStatus: 'success' as const
        })
        
        console.log(`数据获取完成: 共获取 ${allProducts.length} 个商品，总页数 ${fetchProgress.value.totalPages}`)
        console.log("allProducts", allProducts)

        let totalSaved = 0

        // 每500条保存一次
        const batchSize = 500
        for (let i = 0; i < allProducts.length; i += batchSize) {
            const batch = allProducts.slice(i, i + batchSize)

            try {
                const saveResult = await batchSaveRejectedProducts(batch)
                totalSaved += saveResult.inserted_count

                // 更新保存进度
                Object.assign(fetchProgress.value, {
                    currentBatch: Math.ceil((i + batchSize) / batchSize)
                })
            } catch (error) {
                console.error('保存批次数据失败:', error)
                // 继续处理下一批
            }
        }

        console.log(`成功保存 ${totalSaved} 条被拒绝商品数据`)
        return { savedCount: totalSaved, totalCount: allProducts.length }

    } catch (error) {
        console.error('获取和保存被拒绝商品数据失败:', error)
        Object.assign(fetchProgress.value, {
            networkStatus: 'error' as const
        })
        throw error
    }
}

// 测试RPC环境 - 安全版本
const testRPCEnv = async () => {
    // 首先检查用户登录状态
    const isLoggedIn = await checkAndHandleUserLogin()
    if (!isLoggedIn) {
        console.log('用户未登录，终止RPC测试操作')
        return
    }
    
    try {
        console.log('手动测试RPC环境...')
        const testResult = await getRPCStatus()
        console.log('详细测试结果:', testResult)
        
        let message = `RPC环境测试完成，请查看控制台详细信息\n支持状态: ${testResult.supported ? '是' : '否'}`
        
        if (testResult.message) {
            message += `\n消息: ${testResult.message}`
        }
        
        if (testResult.error) {
            message += `\n错误详情: ${testResult.error}`
            console.error('RPC测试错误堆栈:', testResult.error)
        }
        
        alert(message)
    } catch (error) {
        console.error('RPC环境测试失败:', error)
        let errorMessage = 'RPC环境测试失败，这不会影响系统正常运行'
        
        if (error instanceof Error) {
            errorMessage += `\n错误: ${error.message}`
            if (error.stack) {
                console.error('测试错误堆栈:', error.stack)
            }
        }
        
        alert(errorMessage)
    }
}

// 初始化RPC状态检查 - 仅在用户启用时执行
const initializeRPCStatus = async () => {
    if (!enableRPCStatusCheck.value) {
        console.log('RPC状态检测已禁用，跳过初始化')
        return
    }
    
    try {
        console.log('开始RPC状态检查...')
        rpcStatus.value = await getRPCStatus()
        console.log('RPC状态检查完成:', rpcStatus.value)
        
        if (debugMode.value) {
            console.log('RPC状态详细信息:', {
                supported: rpcStatus.value?.supported,
                chromeAPIs: rpcStatus.value?.chromeAPIs,
                currentPage: rpcStatus.value?.currentPage,
                permissions: rpcStatus.value?.permissions,
                message: rpcStatus.value?.message,
                error: rpcStatus.value?.error
            })
        }
    } catch (error) {
        console.warn('RPC状态检查失败，但不影响系统正常运行:', error)
        // 设置默认的安全状态
        rpcStatus.value = { 
            supported: false, 
            message: '状态检查失败，系统将使用API模式',
            chromeAPIs: { scripting: false, debugger: false, tabs: false },
            currentPage: { isN11: false },
            permissions: { debugger: false, scripting: false, activeTab: false }
        }
    }
}

// 监听RPC检测启用状态变化
import { watch } from 'vue'
watch(enableRPCStatusCheck, (newValue) => {
    if (newValue) {
        console.log('用户启用了RPC状态检测')
        initializeRPCStatus()
    } else {
        console.log('用户禁用了RPC状态检测')
        rpcStatus.value = null
    }
})

// 监听调试按钮开关变化
watch(enableDebugButton, (newValue) => {
    console.log(`调试按钮开关变化: ${newValue}`)
    // 触发DOM变化检测，重新评估按钮状态
    setTimeout(handleDomChange, 50)
})

/**
 * 停止执行
 */
const stopExecution = (): void => {
    console.log('用户请求停止执行')
    executionState.value.shouldStop = true
    ElMessage.info('正在停止执行...')
}

/**
 * 清除执行状态
 */
const clearExecutionState = (): void => {
    executionState.value = {
        isRunning: false,
        shouldStop: false,
        lastError: null,
        retryCount: 0,
        maxRetries: 3
    }
    ElMessage.success('执行状态已清除')
}

/**
 * 强制清理所有调试器
 */
const forceCleanupDebuggers = async (): Promise<void> => {
    // 首先检查用户登录状态
    const isLoggedIn = await checkAndHandleUserLogin()
    if (!isLoggedIn) {
        console.log('用户未登录，终止清理调试器操作')
        return
    }

    try {
        ElMessage.info('正在清理所有调试器...')
        await cleanupAllRPCResources()
        ElMessage.success('调试器清理完成')
    } catch (error) {
        console.error('清理调试器失败:', error)
        ElMessage.error(`清理失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
}

/**
 * 测试进度更新
 */
const testProgressUpdate = (): void => {
    console.log('[测试] 开始测试进度更新')

    // 显示进度对话框
    showProgressDialog.value = true
    progressCurrentStep.value = 'fetch'

    // 模拟进度更新
    let currentPage = 0
    const totalPages = 10

    const updateProgress = () => {
        currentPage++
        console.log(`[测试] 更新进度: ${currentPage}/${totalPages}`)

        Object.assign(fetchProgress.value, {
            currentPage: currentPage,
            totalPages: totalPages,
            currentBatch: Math.ceil(currentPage / 5),
            networkStatus: 'requesting' as const,
            speed: 2.5,
            estimatedTime: (totalPages - currentPage) * 2
        })

        if (currentPage < totalPages) {
            setTimeout(updateProgress, 1000)
        } else {
            Object.assign(fetchProgress.value, {
                networkStatus: 'success' as const,
                totalProducts: currentPage * 100
            })
            console.log('[测试] 进度更新完成')
        }
    }

    setTimeout(updateProgress, 500)
}

// 生命周期钩子
onMounted(async () => {
    console.log('N11产品列表页面组件已挂载')
    
    // 初始化用户信息管理
    await initUserStore()
    
    // 监听用户信息变化
    userInfoChangeCleanup = onUserInfoChange(() => {
        console.log('用户信息发生变化，重新检查登录状态')
        
        // 防抖处理：避免短时间内重复检查
        const now = Date.now()
        if (now - lastLoginCheckTime < LOGIN_CHECK_DEBOUNCE_TIME) {
            console.log('防抖：跳过重复的用户信息变化检查')
            return
        }
        lastLoginCheckTime = now
        
        // 防止重复检查
        if (isCheckingUserLogin.value) {
            console.log('正在检查用户登录状态中，跳过重复检查')
            return
        }
        
        // 当用户信息变化时，重新检查登录状态
        if (globalUserInfo.isLogin) {
            // 异步执行，避免阻塞
            setTimeout(async () => {
                try {
                    isCheckingUserLogin.value = true
                    await checkAndHandleUserLogin(false) // 不显示错误消息，避免死循环
                } catch (error) {
                    console.error('用户信息变化后检查登录状态失败:', error)
                    // 不显示错误消息，避免死循环
                } finally {
                    isCheckingUserLogin.value = false
                }
            }, 100)
        } else {
            // 用户退出登录，清理状态
            userInfo.value = null
            isCheckingUserLogin.value = false
            console.log('用户已退出登录，清理用户信息状态')
        }
    })
    
    // 先检查用户登录状态
    try {
        isCheckingUserLogin.value = true
        await checkAndHandleUserLogin()
    } catch (error) {
        console.error('初始化检查用户登录状态失败:', error)
        // 初始化失败时显示一次错误消息
        ElMessage.error('初始化用户登录状态失败，请刷新页面重试')
    } finally {
        isCheckingUserLogin.value = false
    }
    
    // 初始化RPC环境，启用调试器通知抑制
    initializeRPC({
        suppressDebuggerNotification: true,
        autoCleanupOnError: true
    })
    
    // 注意：RPC状态检查现在是可选的，默认禁用以避免影响系统运行
    // 如果需要检查RPC状态，请在调试界面中手动启用
    console.log('RPC状态检测已设为可选，不会自动执行以避免潜在问题')
    
    // 延迟启动观察器，确保页面完全加载
    setTimeout(setupMutationObserver, 1000)
    
    // 添加额外的点击监听器作为备用检测机制
    setTimeout(() => {
        const tabManager = document.querySelector('.tabManager')
        if (tabManager) {
            tabManager.addEventListener('click', (event) => {
                console.log('检测到tabManager点击事件:', event.target)
                // 延迟检查状态变化
                setTimeout(handleDomChange, 200)
            })
            console.log('已添加tabManager点击监听器')
        }
    }, 1500)
})

onUnmounted(async () => {
    console.log('N11产品列表页面组件即将卸载')
    
    // 设置停止标志
    executionState.value.shouldStop = true
    
    // 清理用户信息变化监听器
    if (userInfoChangeCleanup) {
        userInfoChangeCleanup()
    }
    
    // 清理用户信息管理
    cleanupUserStore()
    
    // 清理观察器
    cleanupObserver()
    
    // 清理RPC资源
    try {
        await cleanupAllRPCResources()
        console.log('组件卸载时已清理所有RPC资源')
    } catch (error) {
        console.error('组件卸载时清理RPC资源失败:', error)
    }
})
</script>

<style scoped>
.chrome_container {
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.debug-info {
    margin-top: 20px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
}

.debug-info p {
    margin: 5px 0;
}

/* 重新上传按钮样式 */
:global(.reupload-btn) {
    background-color: #fff;
    color: #5d3ebc;
    border: 1px solid #5d3ebc;
    padding: 5px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    writing-mode: horizontal-tb;
    text-orientation: mixed;
    direction: ltr;
    min-width: auto;
    height: auto;
    line-height: normal;
    vertical-align: middle;
}
/* :global(.reupload-btn) {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 7px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    writing-mode: horizontal-tb;
    text-orientation: mixed;
    direction: ltr;
    min-width: auto;
    height: auto;
    line-height: normal;
    vertical-align: middle;
} */

:global(.reupload-btn:hover) {
    color: #fff;
    background-color: #0056b3;
}

:global(.reupload-btn:active) {
    color: #fff;
    background-color: #004085;
}

:global(.reupload-btn:disabled) {
    color: #fff;
    background-color: #6c757d;
    cursor: not-allowed;
}

/* 调试按钮样式 */
:global(.debug-btn) {
    background-color: #fff;
    color: #28a745;
    border: 1px solid #28a745;
    padding: 5px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    writing-mode: horizontal-tb;
    text-orientation: mixed;
    direction: ltr;
    min-width: auto;
    height: auto;
    line-height: normal;
    vertical-align: middle;
}

:global(.debug-btn:hover) {
    color: #fff;
    background-color: #28a745;
}

:global(.debug-btn:active) {
    color: #fff;
    background-color: #1e7e34;
}

:global(.debug-btn:disabled) {
    color: #fff;
    background-color: #6c757d;
    cursor: not-allowed;
}

/* 进度显示样式 */
.progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.progress-dialog {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 400px;
    text-align: center;
}

.progress-dialog h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 15px 0;
}

.progress-fill {
    height: 100%;
    background-color: #007bff;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-message {
    margin: 15px 0;
    color: #666;
    font-size: 14px;
}

.progress-percent {
    font-size: 16px;
    font-weight: bold;
    color: #007bff;
}
</style>