<template>
  <div class="collection-settings-card">
    <div class="card-header">
      <h3>TEMU商品采集设置</h3>
      <el-button 
        type="primary" 
        size="small" 
        @click="openSettingsDialog"
        :loading="settingsLoading"
      >
        设置
      </el-button>
    </div>
    <div class="card-content">
      <div v-if="collectionSettings" class="settings-info">
        <div class="info-row">
          <span class="info-label">采集模式:</span>
          <span class="info-value" :class="getCollectionModeClass()">
            {{ collectionSettings.collection_mode_name || '未设置' }}
          </span>
        </div>
        <div class="info-row">
          <span class="info-label">存储目录:</span>
          <span class="info-value">
            {{ collectionSettings.default_directory_name || '未设置' }}
          </span>
        </div>
        <div v-if="collectionSettings.no_remind_until" class="info-row">
          <span class="info-label">下次提醒:</span>
          <span class="info-value" :class="getRemindTimeClass()">
            {{ formatRemindTime(collectionSettings.no_remind_until) }}
          </span>
        </div>
        <div v-else class="info-row">
          <span class="info-label">提醒状态:</span>
          <span class="info-value remind-always">每次均需手动选择目录</span>
        </div>
      </div>
      <div v-else-if="isLoading" class="settings-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载设置信息...</span>
      </div>
      <div v-else class="settings-empty">
        <el-icon><Setting /></el-icon>
        <span>点击设置按钮配置采集参数</span>
      </div>
    </div>

    <!-- 采集设置对话框 -->
    <CollectionSettingsDialog
      v-model="showSettingsDialog"
      :available-directories="availableDirectories"
      :loading="settingsLoading"
      :initial-data="collectionSettings"
      @confirm="handleSettingsConfirm"
      @create-directory="openDirectoryDialog"
    />

    <!-- 目录创建对话框 -->
    <DirectoryCreateDialog
      v-model="showDirectoryDialog"
      :loading="directoryLoading"
      @confirm="handleDirectoryCreate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Setting } from '@element-plus/icons-vue'
import CollectionSettingsDialog from './CollectionSettingsDialog.vue'
import DirectoryCreateDialog from './DirectoryCreateDialog.vue'
import { 
  getUserCollectionSettings, 
  saveUserCollectionSettings, 
  getUserAvailableDirectories,
  type CollectionSettings,
  type Directory,
  type SaveSettingsData
} from '../utils/collectionSettingsApi'
import { createDirectory } from '../utils/directoryApi'

// Props
interface Props {
  autoLoad?: boolean // 是否自动加载数据
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

// Emits
const emit = defineEmits<{
  'settings-updated': [settings: CollectionSettings]
}>()

// 响应式数据
const collectionSettings = ref<CollectionSettings | null>(null)
const availableDirectories = ref<Directory[]>([])
const settingsLoading = ref(false)
const directoryLoading = ref(false)
const showSettingsDialog = ref(false)
const showDirectoryDialog = ref(false)
const isLoading = ref(false) // 新增：用于跟踪加载状态

// 获取采集模式样式类
const getCollectionModeClass = () => {
  if (!collectionSettings.value) return ''
  
  switch (collectionSettings.value.collection_mode) {
    case 1:
      return 'mode-auto' // 自动采集 - 绿色
    case 2:
      return 'mode-manual' // 手动采集 - 橙色
    case 3:
      return 'mode-none' // 不采集 - 红色
    default:
      return ''
  }
}

// 获取提醒时间样式类
const getRemindTimeClass = () => {
  if (!collectionSettings.value?.no_remind_until) return ''
  
  const remindTime = new Date(collectionSettings.value.no_remind_until)
  const now = new Date()
  
  return now >= remindTime ? 'remind-expired' : 'remind-active'
}

// 格式化提醒时间
const formatRemindTime = (timeStr: string) => {
  if (!timeStr) return ''
  
  const remindTime = new Date(timeStr)
  const now = new Date()
  
  if (now >= remindTime) {
    return `${timeStr} (已到期)`
  } else {
    return timeStr
  }
}

// 加载采集设置
const loadCollectionSettings = async () => {
  try {
    isLoading.value = true
    settingsLoading.value = true
    const settings = await getUserCollectionSettings()
    collectionSettings.value = settings
    console.log('采集设置加载成功:', settings)
    emit('settings-updated', settings)
  } catch (error) {
    console.error('加载采集设置失败:', error)
    ElMessage.error('加载采集设置失败')
  } finally {
    isLoading.value = false
    settingsLoading.value = false
  }
}

// 加载可用目录
const loadAvailableDirectories = async () => {
  try {
    const response = await getUserAvailableDirectories()
    availableDirectories.value = response.list || []
    console.log('可用目录加载成功:', response.list)
  } catch (error) {
    console.error('加载可用目录失败:', error)
    ElMessage.error('加载可用目录失败')
  }
}

// 打开设置对话框
const openSettingsDialog = async () => {
  try {
    settingsLoading.value = true
    await loadAvailableDirectories()
    showSettingsDialog.value = true
  } catch (error) {
    console.error('打开设置对话框失败:', error)
  } finally {
    settingsLoading.value = false
  }
}

// 处理设置确认
const handleSettingsConfirm = async (data: SaveSettingsData) => {
  try {
    settingsLoading.value = true
    const result = await saveUserCollectionSettings(data)
    
    ElMessage.success('设置保存成功')
    showSettingsDialog.value = false
    
    // 重新加载设置
    await loadCollectionSettings()
    
    console.log('设置保存成功:', result)
  } catch (error: any) {
    console.error('保存设置失败:', error)
    ElMessage.error(error.message || '保存设置失败')
  } finally {
    settingsLoading.value = false
  }
}

// 打开目录创建对话框
const openDirectoryDialog = () => {
  showDirectoryDialog.value = true
}

// 处理目录创建
const handleDirectoryCreate = async (data: { name: string; description: string }) => {
  try {
    directoryLoading.value = true
    
    const result = await createDirectory({
      name: data.name,
      description: data.description,
      status: 1
    })
    
    ElMessage.success('目录创建成功')
    showDirectoryDialog.value = false
    
    // 重新加载可用目录
    await loadAvailableDirectories()
    
    console.log('目录创建成功:', result)
  } catch (error: any) {
    console.error('创建目录失败:', error)
    ElMessage.error(error.message || '创建目录失败')
  } finally {
    directoryLoading.value = false
  }
}

// 暴露方法供父组件调用
defineExpose({
  loadCollectionSettings,
  collectionSettings
})

// 组件挂载时加载数据
onMounted(() => {
  // 无论autoLoad是否为true，都尝试加载设置数据
  // autoLoad主要用于控制是否显示加载状态，但数据始终需要加载
  loadCollectionSettings()
})
</script>

<style scoped>
.collection-settings-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  background-color: #eef4ff;
  padding: 15px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.card-content {
  padding: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 100px;
  color: #666;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  flex: 1;
  color: #333;
}

/* 采集设置相关样式 */
.settings-info {
  min-height: 80px;
}

.settings-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #666;
  min-height: 80px;
}

.settings-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #999;
  min-height: 80px;
  font-style: italic;
}

.mode-auto {
  color: #52c41a;
  font-weight: bold;
}

.mode-manual {
  color: #fa8c16;
  font-weight: bold;
}

.mode-none {
  color: #ff4d4f;
  font-weight: bold;
}

.remind-active {
  color: #52c41a;
}

.remind-expired {
  color: #ff4d4f;
}

.remind-always {
  color: #fa8c16;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
    gap: 4px;
  }
  
  .info-label {
    width: auto;
    text-align: left;
    padding-right: 0;
    font-weight: 500;
  }
}
</style> 