<template>
  <el-dialog
    v-model="visible"
    title=""
    width="400px"
    :close-on-click-modal="false"
    center
    class="task-completed-dialog"
  >
    <div class="completed-content">
      <!-- 状态图标 -->
      <div class="status-icon">
        <div class="icon-wrapper success">
          <el-icon size="48"><CircleCheckFilled /></el-icon>
        </div>
      </div>
      
      <!-- 标题 -->
      <div class="dialog-title">
        <h3>任务已完成</h3>
        <p>当前任务已经完成，无需重复执行</p>
      </div>
      
      <!-- 任务信息 -->
      <div class="task-info" v-if="taskInfo">
        <div class="info-item">
          <span class="label">任务名称：</span>
          <span class="value">{{ taskInfo.store_name }}</span>
        </div>
        <div class="info-item" v-if="taskInfo.task_count">
          <span class="label">任务数量：</span>
          <span class="value">{{ taskInfo.task_count }}</span>
        </div>
        <div class="info-item" v-if="taskInfo.task_num">
          <span class="label">已处理：</span>
          <span class="value">{{ taskInfo.task_num }}</span>
        </div>
      </div>
      
      <!-- 完成状态提示 -->
      <div class="completed-status">
        <el-tag type="success" size="large" effect="light">
          <el-icon><SuccessFilled /></el-icon>
          <span>任务已完成</span>
        </el-tag>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm" size="large">
          <el-icon><Check /></el-icon>
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CircleCheckFilled, SuccessFilled, Check } from '@element-plus/icons-vue'

interface TaskInfo {
  store_name: string
  task_count?: number
  task_num?: number
}

interface Props {
  visible: boolean
  taskInfo?: TaskInfo
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: []
}>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleConfirm = () => {
  emit('confirm')
  visible.value = false
}
</script>

<style scoped>
.task-completed-dialog {
  --success-color: #67c23a;
  --success-light: #f0f9ff;
  --success-lighter: #e1f3d8;
}

:deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

:deep(.el-dialog__header) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 30px;
}

:deep(.el-dialog__footer) {
  padding: 20px 30px 30px;
  border-top: 1px solid #f0f0f0;
}

.completed-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-icon {
  margin-bottom: 20px;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-wrapper.success {
  background: var(--success-lighter);
  color: var(--success-color);
  border: 3px solid var(--success-light);
}

.icon-wrapper::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--success-lighter);
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.dialog-title {
  text-align: center;
  margin-bottom: 25px;
}

.dialog-title h3 {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.dialog-title p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.task-info {
  width: 100%;
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  line-height: 1.4;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
}

.completed-status {
  margin-bottom: 20px;
}

.completed-status .el-tag {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 20px;
}

.completed-status .el-icon {
  margin-right: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

.dialog-footer .el-button {
  min-width: 120px;
  border-radius: 8px;
  font-weight: 500;
}

.dialog-footer .el-icon {
  margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 15px 20px 20px;
  }
  
  .task-info {
    padding: 15px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .label {
    min-width: auto;
  }
}
</style> 