import { ElMessage, ElNotification } from 'element-plus';
import { saveUserInfo } from './storage';
import { setLoginExpired } from './loginState';
import { handleUserLogout } from './userStore';

interface ApiResponseData<T = any> {
  status: number;
  code: number;
  msg?: string;
  data: T;
}

/**
 * Type guard to check if data is ApiResponseData<T>
 */
function isApiResponseData<T>(data: any): data is ApiResponseData<T> {
  return typeof data === 'object' && data !== null && 'status' in data && 'code' in data;
}

/**
 * 清除所有登录相关的存储数据
 */
const clearLoginData = async (): Promise<void> => {
  try {
    // 清除用户信息
    await saveUserInfo({
      isLogin: false,
      phone: '',
      expiryDate: '',
      isVip: false,
      token: ''
    });
    
    console.log('登录数据已清除');
  } catch (error) {
    console.error('清除登录数据失败:', error);
  }
};

/**
 * 发送登录失效消息到所有相关页面
 */
const notifyLoginExpired = (): void => {
  try {
    // 发送消息到background script
    chrome.runtime.sendMessage({
      type: 'LOGIN_EXPIRED',
      timestamp: Date.now()
    }).catch(() => {
      // 忽略发送失败的情况
    });

    console.log('登录失效通知已发送');
  } catch (error) {
    console.error('发送登录失效通知失败:', error);
  }
};

/**
 * 处理 API 响应，检查错误状态并触发通知。
 * @param responseData 接口返回的数据，通常是 response[0].data
 * @param notificationType 通知类型，可选 'message' 或 'notification'，默认为 'notification'
 */
export const handleApiResponse = <T>(responseData: ApiResponseData<T> | string, statusCode: number, notificationType: 'message' | 'notification' = 'notification'): boolean => {
  if (statusCode === 404 || statusCode === 500 || statusCode === 405 || statusCode === 403) {
    if (statusCode === 403) {
      const errorMessage = (typeof responseData === 'string' &&
        responseData.toLowerCase().includes('authentication') &&
        responseData.toLowerCase().includes('failed'))
        ? '请检查appkey是否正确'
        : '权限不足';

      ElNotification({
        title: '错误提示',
        message: errorMessage,
        type: 'error',
        duration: 3000,
      });
      return false;
    }
    ElNotification({
      title: '错误提示',
      message: isApiResponseData(responseData) && responseData.status === 404 ? '访问地址错误' : '系统错误，请联系管理员',
      type: 'error',
      duration: 3000,
    });
    return false;
  }
  // 检查是否存在响应数据且 status 不是 200
  console.log('responseData', responseData);
  // 检查登录失效（401状态码）
  if (isApiResponseData(responseData) && responseData.status === 401) {
    console.log('检测到登录失效，状态码401');
    
    // 使用统一的退出登录处理
    handleUserLogout();
    
    // 返回 false 表示处理了错误
    return false;
  }
  
  if (isApiResponseData(responseData) && responseData.code != 1 && responseData.msg) {
    const message = `错误码: ${responseData.status}, 消息: ${responseData.msg}`;
    if (notificationType === 'notification') {
      ElNotification({
        title: '错误提示',
        message: responseData.msg,
        type: 'error',
        duration: 3000, // 常驻通知
      });
    } else {
      ElMessage({
        message: responseData.msg,
        type: 'error',
      });
    }

    // 返回 false 表示处理了错误
    return false;
  }
  // 返回 true 表示响应正常或未处理错误
  return true;
};

/**
 * 处理 N11 API 响应，检查错误状态并触发通知。
 * @param responseData 接口返回的数据
 * @param notificationType 通知类型，可选 'message' 或 'notification'，默认为 'notification'
 */
export const handleN11ApiResponse = <T>(responseData: ApiResponseData<T> | string, statusCode: number, notificationType: 'message' | 'notification' = 'notification'): boolean => {
  console.log('处理 N11 响应:', responseData, statusCode);
  
  // 处理500错误
  if(statusCode == 500) {
    // 检查是否是网络错误
    if (typeof responseData === 'object' && responseData.msg && 
        responseData.msg.toLowerCase().includes('network') && 
        responseData.msg.toLowerCase().includes('error')) {
      ElNotification({
        title: '错误提示',
        message: '当前网络无法访问N11接口,请开启VPN访问',
        type: 'error',
        duration: 3000,
      });
    } else {
      // 对于其他500错误，不显示通知，让调用方处理
      console.log('N11 API 500错误，由调用方处理:', responseData);
    }
    return false;
  }
  
  // 处理其他HTTP错误状态码
  if (statusCode === 404 || statusCode === 405 || statusCode === 403) {
    if (statusCode === 403) {
      const errorMessage = (typeof responseData === 'string' &&
        responseData.toLowerCase().includes('authentication') &&
        responseData.toLowerCase().includes('failed'))
        ? '请检查appkey是否正确'
        : '权限不足';

      ElNotification({
        title: '错误提示',
        message: errorMessage,
        type: 'error',
        duration: 3000,
      });
      return false;
    }
    ElNotification({
      title: '错误提示',
      message: isApiResponseData(responseData) && responseData.status === 404 ? '访问地址错误' : '系统错误，请联系管理员',
      type: 'error',
      duration: 3000,
    });
    return false;
  }
  
  return true;
}; 