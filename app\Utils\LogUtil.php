<?php

namespace App\Utils;

use GuzzleHttp\Client;
use Illuminate\Support\Str;
use App\Service\System\ConfigService;

class LogUtil
{

    private static $INSTANCE;

    public $debugUrlFeiShu = "https://open.feishu.cn/open-apis/bot/v2/hook/d27136c4-65f6-4c2d-81d2-c7f6e9a8a557";


    public $notifyUrlFeiShu = "https://open.feishu.cn/open-apis/bot/v2/hook/c8713cba-3f1e-4d1e-a669-163245cd9d5d";

    public static function getInstance()
    {
        if (self::$INSTANCE == null) self::$INSTANCE = new LogUtil();
        return self::$INSTANCE;
    }

    public function infoDebug(string $content='')
    {
        return '';
    }


    public function infoNotifyFeiShu(string $content='')
    {
    }


    /*
     * 信息及时提醒
     * */
    public function infoExceptionFeiShu(string $content='',$exception=null)
    {
        return '';
        try{
            if(empty($content)) return '';
            if($exception instanceof \Throwable || $exception instanceof \Exception){
                if(Str::contains($exception->getFile(), 'vendor_ext')){
                    return '';
                }
            }
            $content = strtolower($content);
            if(Str::contains($content,'unauthenticated') || Str::contains($content,'请先登录')){
                return '';
            }

            $host = request()->getHost();
            //判断host是否是IP地址格式
            /*if(filter_var($host, FILTER_VALIDATE_IP)){
                return '';
            }*/

            $currentUrl = $this->getCurrentUrl();
            /*if( Str::contains($currentUrl, 'event/auth')
                || Str::contains($currentUrl, 'thirdparty_t/login')
                || Str::contains($content, '参数错误')
                || Str::contains($content, '必须')
                || Str::contains($content, '不能')
                || Str::contains($content, 'Unauthenticated')
            ){
                return '';
            }*/

            $tip = 2;
            if(Str::startsWith($content,'success_')){
                $tip = 1;
                $content = str_ireplace('success_','',$content);
            }

            $log   = "提醒new";
            $log  .= "\r\n";

            if(php_sapi_name() == 'cli'){
                $log  .= "当前是命令行CLI模式";
                $log  .= "\r\n";
            }

            $log  .= ($tip==2) ? "系统异常" : "信息提示";
            $log  .= "\r\n";
            $log  .= "请求地址：".$currentUrl ?? '';
            $log  .= "\r\n";
            $log  .= "请求IP：".request()->getClientIp() ?? '';
            $log  .= "\r\n";
            $log  .= "请求域名：".request()->getHost() ?? '';
            $log  .= "\r\n";
            $log  .= "请求服务器：". $this->domainToServerName();
            $log  .= "\r\n";
            $log  .= "请求方式：".request()->getMethod() ?? '';
            $log  .= "\r\n";
            if(strtoupper(request()->getMethod())=='POST'){
                $log .= "POST参数：".json_encode($this->getPostData(),JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
                $log .= "\r\n";
            }
            if($tip == 2){
                $log .= "异常信息：".$content;
                if($exception instanceof \Throwable || $exception instanceof \Exception){
                    $log .= "\r\n";
                    $log.= "异常文件：".$exception->getFile();
                    $log .= "\r\n";
                    $log.= "异常行数：".$exception->getLine();
                }
            }else{
                $log .= "提示内容：".$content;
            }

            $content = $log;

            $headers = [
                'Content-Type' => 'application/json',
            ];
            // 构建请求数据
            $data = [
                'msg_type' => 'text',
                'content'    =>
                    ['text' => $content],
            ];

            $data = json_encode($data);
            // 发送请求
            $apiService = $this->notifyUrlFeiShu;
            $client = new Client(['timeout' => 5]);
            $response = $client->post($apiService, [
                'headers' => $headers,
                'body' => $data,
                'verify' => false,
            ]);
            // 处理响应
            $dingresult = json_decode($response->getBody(), true);
        }catch (\Exception $e){

        }
        return '';
    }

    public function getCurrentUrl()
    {
        $uri = request()->getRequestUri();
        $url = request()->getSchemeAndHttpHost() . $uri;
        $url .= request()->getQueryString() ? '?' . request()->getQueryString() : '';
        return $url;
    }

    public function getPostData()
    {
        return request()->post();
    }

    public function domainToServerName()
    {
        $domain = request()->getHost();
        switch($domain){
            case 'dp.dailyhotnews.com.cn':
                $server = '系统';
                break;
            case 'dp.test.com':
                $server = '本地调试';
                break;
            default:
                $server = '大屏数据展示';
        }
        return $server;
    }


}
