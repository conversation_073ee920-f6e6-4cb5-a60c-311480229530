<template>
  <div class="task-detail">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-section">
      <div class="custom-breadcrumb">
        <el-button type="text" @click="goBack" class="breadcrumb-back-button">
          <el-icon><ArrowLeft /></el-icon>
          <span>任务列表</span>
        </el-button>
        <span class="breadcrumb-separator">/</span>
        <span class="breadcrumb-current-page">任务详情</span>
      </div>
    </div>

    <!-- 任务基本信息和采集设置 - 响应式布局 -->
    <div class="task-info-section" v-if="taskInfo">
      <div class="task-info-layout">
        <!-- 任务基本信息 -->
        <div class="task-info-card">
          <el-card>
            <template #header>
              <div class="card-header">
                <span class="card-title">任务基本信息</span>
                <el-tag :type="taskInfo.task_over === 1 ? 'success' : 'warning'">
                  {{ taskInfo.task_over === 1 ? '已完成' : '未完成' }}
                </el-tag>
              </div>
            </template>
            
            <div class="task-info-content">
              <div class="info-row">
                <div class="info-item">
                  <span class="label">店铺名称：</span>
                  <span class="value">{{ taskInfo.store_name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">目录名称：</span>
                  <span class="value">{{ taskInfo.directory_name || '未知目录' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">商品范围：</span>
                  <span class="value">{{ taskInfo.is_selected === 1 ? '指定商品' : '指定时间范围' }}</span>
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-item">
                  <span class="label">时间范围：</span>
                  <span class="value">{{ formatTimeRange(taskInfo) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">排序方式：</span>
                  <span class="value">{{ getSortOrderDisplay(taskInfo) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ taskInfo.created_at }}</span>
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-item">
                  <span class="label">更新时间：</span>
                  <span class="value">{{ taskInfo.updated_at }}</span>
                </div>
                <div class="info-item">
                </div>
                <div class="info-item">
                </div>
              </div>
            </div>
          </el-card>
        </div>
        
        <!-- 采集设置卡片 -->
        <div class="collection-settings-card">
          <CollectionSettingsCard :auto-load="false" @settings-updated="handleSettingsUpdated" />
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-section" v-if="statistics">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total_count }}</div>
              <div class="stat-label">总数量</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.success_count }}</div>
              <div class="stat-label">成功</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card danger">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.failed_count }}</div>
              <div class="stat-label">失败</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pending_count }}</div>
              <div class="stat-label">等待</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card info">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.processing_count }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.success_rate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 200px;">
            <el-option label="等待上传" :value="0" />
            <el-option label="已成功上传" :value="1" />
            <el-option label="已上传待查询结果" :value="2" />
            <el-option label="上传失败" :value="3" />
            <el-option label="已存在" :value="4" />
            <el-option label="没有第三方商品分类" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.goods_name"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button 
            type="success" 
            @click="handleQueryResults"
            :loading="queryLoading"
            :disabled="!statistics || statistics.processing_count === 0"
          >
            <el-icon><Search /></el-icon>
            查询结果 ({{ statistics?.processing_count || 0 }})
          </el-button>
          <el-button 
            type="danger" 
            @click="handleBatchRetryUpload"
            :disabled="!statistics || statistics.failed_count_retry === 0"
          >
            <el-icon><Refresh /></el-icon>
            失败任务重新上传 ({{ statistics?.failed_count_retry || 0 }})
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 查询进度提示 -->
      <div v-if="queryProgress.show" class="query-progress">
        <el-alert
          :title="queryProgress.message"
          type="info"
          :closable="false"
          show-icon
        />
        <el-progress
          v-if="queryProgress.total > 0"
          :percentage="Math.round((queryProgress.processed / queryProgress.total) * 100)"
          :status="queryProgress.status"
          style="margin-top: 10px;"
        />
      </div>
    </div>

    <!-- 详情列表 -->
    <div class="table-section">
      <el-table 
        :data="detailList" 
        v-loading="loading"
        stripe
        border
      >
        <el-table-column label="序号" type="index" width="80" :index="getIndex" />
        <el-table-column label="商品信息" min-width="350">
          <template #default="{ row }">
            <div class="goods-info">
              <div class="goods-image">
                <el-image
                  :src="getImageUrl(row.thumb_url)"
                  :preview-src-list="getPreviewList(row.thumb_url)"
                  fit="cover"
                  style="width: 60px; height: 60px; border-radius: 4px; cursor: pointer;"
                  :preview-teleported="true"
                  :z-index="3000"
                  loading="lazy"
                  @error="handleImageError"
                >
                  <template #placeholder>
                    <div class="image-slot">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                  <template #error>
                    <div class="image-slot error">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
              </div>
              <div class="goods-details">
                <div class="goods-name" :title="row.goods_name">{{ row.goods_name }}</div>
                <div class="goods-spec" v-if="row.spec_key_values">
                  规格：{{ row.spec_key_values }}
                </div>
                <div class="goods-price">
                  原价：{{ row.price }} {{ row.currentcy }} → 
                  售价：{{ row.price_third }} {{ row.currentcy }}
                </div>
                
                <!-- N11分类信息 -->
                <div class="n11-category-info" v-if="row.n11_category">
                  <div class="platform-title">N11平台分类</div>
                  <div class="category-item">
                    <div class="category-names">
                      <span class="name-cn">{{ row.n11_category.name }}[{{ row.category_id }}]</span>
                      <span class="name-tl" v-if="row.n11_category.name_tl">{{ row.n11_category.name_tl }}</span>
                    </div>
                    <div class="category-paths" v-if="row.n11_category.path_name">
                      <span class="path-cn">{{ row.n11_category.path_name }}</span>
                      <span class="path-tl" v-if="row.n11_category.path_name_tl">{{ row.n11_category.path_name_tl }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品编码" width="120">
          <template #default="{ row }">
            <div>
              <div>主ID：{{ row.product_main_id }}</div>
              <div>编码：{{ row.stock_code }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="库存配置" width="120">
          <template #default="{ row }">
            <div>
              <div>数量：{{ row.quantity }}</div>
              <div>税率：{{ row.vat_rate }}%</div>
              <div>备货：{{ row.preparing_day }}天</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="200">
          <template #default="{ row }">
            <div class="status-column">
              <el-tag :type="row.status_type">
                {{ row.status_text }}
              </el-tag>
              
              <!-- 失败时显示详细错误信息 -->
              <div v-if="row.status === 3 && row.third_result" class="error-details">
                <div class="error-content">
                  {{ row.third_result }}
                </div>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="copyStatusInfo(row)"
                  class="copy-error-btn"
                >
                  <el-icon><DocumentCopy /></el-icon>
                  复制信息
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="第三方信息" width="150">
          <template #default="{ row }">
            <div v-if="row.third_task_id">
              <div>任务ID：{{ row.third_task_id }}</div>
              <div>类型：{{ row.third_type }}</div>
              <div>状态：{{ row.third_status }}</div>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column label="时间信息" width="160">
          <template #default="{ row }">
            <div class="time-info-column">
              <div class="time-row">
                <span class="time-label">创建：</span>
                <span class="time-value">{{ formatDateTime(row.created_at) }}</span>
              </div>
              <div class="time-row">
                <span class="time-label">更新：</span>
                <span class="time-value">{{ formatDateTime(row.updated_at) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="left">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleViewParams(row)"
                v-if="row.upload_params"
              >
                查看参数
              </el-button>
              <el-button 
                type="success"
                size="small" 
                @click="handleViewSource(row)"
                v-if="row.goods_info?.source_url"
              >
                查看原商品
              </el-button>
              <el-button 
                type="warning" 
                size="small" 
                @click="handleRetryUpload(row)"
                v-if="row.status === 3"
              >
                重新上传
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 参数查看对话框 -->
    <el-dialog
      v-model="paramsDialogVisible"
      title="上传参数详情"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="currentParams">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="第三方类型">{{ currentParams.third_type }}</el-descriptions-item>
          <el-descriptions-item label="任务详情ID">{{ currentParams.task_detail_id }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 上传状态信息 -->
        <div v-if="currentParams.status_info" style="margin-top: 20px;">
          <h4 style="margin: 0 0 10px 0;">上传状态：</h4>
          <el-tag :type="currentParams.status_info.status_type" size="large">
            {{ currentParams.status_info.status_text }}
          </el-tag>
          
          <!-- 失败时显示详细错误信息 -->
          <div v-if="currentParams.status_info.status === 3 && currentParams.status_info.third_result" 
               style="margin-top: 10px;">
            <div style="font-weight: 500; margin-bottom: 8px; color: #f56c6c;">接口返回错误信息：</div>
            <el-input
              type="textarea"
              :rows="4"
              :value="currentParams.status_info.third_result"
              readonly
              style="font-family: monospace; background-color: #fef0f0; border-color: #fbc4c4;"
            />
          </div>
        </div>
        
        <div style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h4 style="margin: 0;">请求参数：</h4>
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="color: #666; font-size: 14px;">如果没有上传成功，可复制该参数详细内容发送给客服</span>
              <el-button type="primary" size="small" @click="copyParams">
                复制参数
              </el-button>
            </div>
          </div>
          <el-input
            ref="paramsTextarea"
            type="textarea"
            :rows="15"
            :value="formatParams(currentParams.params)"
            readonly
            style="font-family: monospace;"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 进度对话框 -->
    <TaskProgressDialog
      v-model:visible="progressDialogVisible"
      :task-title="progressData.taskTitle"
      :current-progress="progressData.currentProgress"
      :total-progress="progressData.totalProgress"
      :current-product="progressData.currentProduct"
      :is-processing="progressData.isProcessing"
      :is-completed="progressData.isCompleted"
      :has-error="progressData.hasError"
      :error-message="progressData.errorMessage"
      :processing-message="progressData.processingMessage"
      :process-history="progressData.processHistory"
      @close="handleProgressDialogClose"
      @cancel="handleProgressDialogCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Search, Refresh, DocumentCopy, Picture } from '@element-plus/icons-vue'
import { 
  getTaskDetail, 
  getTaskDetailList, 
  type TaskDetailListParams, 
  type TaskDetail,
  type TaskDetailResponse,
  type Task,
  queryTaskResults,
  getPendingQueryTasks,
  batchUpdateTaskDetails,
  getRetryUploadParams
} from '../utils/taskApi'
import { queryN11TaskDetails, processN11QueryResult } from '../utils/n11QueryApi'
import TaskProgressDialog from './TaskProgressDialog.vue'
import CollectionSettingsCard from './CollectionSettingsCard.vue'
import type { ProductUploadResult } from '../utils/taskProcessor'
import type { CollectionSettings } from '../utils/collectionSettingsApi'

// 定义props
const props = defineProps<{
  taskId: number
}>()

// 定义emits
const emit = defineEmits<{
  goBack: []
}>()

// 响应式数据
const loading = ref(false)
const taskInfo = ref<Task | null>(null)
const statistics = ref<any>(null)
const detailList = ref<TaskDetail[]>([])
const dateRange = ref<string[]>([])

// 查询结果相关状态
const queryLoading = ref(false)
const queryProgress = reactive({
  show: false,
  message: '',
  processed: 0,
  total: 0,
  status: 'success' as 'success' | 'exception' | 'warning'
})

// 重新上传相关状态已移除，现在使用进度对话框

// 对话框状态
const paramsDialogVisible = ref(false)
const currentParams = ref<{
  third_type: string
  task_detail_id: number
  params: string
  status_info?: {
    status: number
    status_text: string
    status_type: string
    third_result?: string
  }
} | null>(null)

// 进度对话框相关状态
const progressDialogVisible = ref(false)
const progressData = reactive({
  taskTitle: '',
  currentProgress: 0,
  totalProgress: 0,
  currentProduct: null as ProductUploadResult | null,
  isProcessing: false,
  isCompleted: false,
  hasError: false,
  errorMessage: '',
  processingMessage: '正在处理商品...',
  processHistory: [] as ProductUploadResult[]
})

// 取消上传标志
let cancelUpload = false

// 搜索表单
const searchForm = reactive<{
  status: number | null
  goods_name: string
  created_at_start: string | undefined
  created_at_end: string | undefined
}>({
  status: null,
  goods_name: '',
  created_at_start: undefined,
  created_at_end: undefined
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 获取序号
const getIndex = (index: number) => {
  return (pagination.currentPage - 1) * pagination.pageSize + index + 1
}

// 格式化时间范围显示
const formatTimeRange = (task: Task) => {
  if (task.is_selected === 1) {
    return '----'
  } else {
    // 根据time_range字段显示不同内容
    switch (task.time_range) {
      case 'all':
        return '全部'
      case 'today':
        return '今天'
      case 'yesterday':
        return '昨天'
      case 'lastweek':
        return '最近一周'
      case 'custom':
        if (task.day_start && task.day_end) {
          if (task.day_start === task.day_end) {
            return task.day_start
          } else {
            return `${task.day_start} 至 ${task.day_end}`
          }
        }
        return '自定义'
      default:
        return '未知'
    }
  }
}

// 获取排序方式显示
const getSortOrderDisplay = (task: Task) => {
  if (task.is_selected === 0) {
    const sortOrderMap: Record<string, string> = {
      'desc': '降序',
      'asc': '升序'
    }
    return sortOrderMap[task.sort_order] || '未知'
  }
  return '--'
}

// 格式化参数显示
const formatParams = (params: string) => {
  if (!params) return ''
  
  try {
    const parsed = JSON.parse(params)
    return JSON.stringify(parsed, null, 2)
  } catch (e) {
    return params
  }
}

// 返回任务列表
const goBack = () => {
  emit('goBack')
}

// 处理日期范围变化
const handleDateRangeChange = (val: string[]) => {
  if (val && val.length === 2) {
    searchForm.created_at_start = val[0]
    searchForm.created_at_end = val[1]
  } else {
    searchForm.created_at_start = undefined
    searchForm.created_at_end = undefined
  }
}

// 加载任务详情
const loadTaskDetail = async () => {
  try {
    const response: TaskDetailResponse = await getTaskDetail(props.taskId)
    taskInfo.value = response.task_info
    statistics.value = response.statistics
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  }
}

// 加载任务详情列表
const loadTaskDetailList = async () => {
  loading.value = true
  try {
    const params: TaskDetailListParams = {
      task_id: props.taskId,
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      status: searchForm.status,
      goods_name: searchForm.goods_name,
      created_at_start: searchForm.created_at_start,
      created_at_end: searchForm.created_at_end
    }
    
    const response = await getTaskDetailList(params)
    detailList.value = response.list as TaskDetail[]
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.error('获取任务详情列表失败:', error)
    ElMessage.error('获取任务详情列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadTaskDetailList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    status: null,
    goods_name: '',
    created_at_start: undefined,
    created_at_end: undefined
  })
  dateRange.value = []
  pagination.currentPage = 1
  loadTaskDetailList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTaskDetailList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadTaskDetailList()
}

// 查看参数
const handleViewParams = (row: TaskDetail) => {
  if (!row.upload_params) {
    ElMessage.warning('该任务没有上传参数')
    return
  }
  
  // 解析原始参数并添加task_detail_id
  let paramsString = ''
  
  try {
    // row.upload_params.params 是字符串，需要解析并添加task_detail_id
    const parsedParams = JSON.parse(row.upload_params.params)
    parsedParams.task_detail_id = row.id
    paramsString = JSON.stringify(parsedParams, null, 2)
  } catch (error) {
    console.error('解析参数失败:', error)
    // 如果解析失败，直接使用原始参数
    paramsString = row.upload_params.params
  }
  
  currentParams.value = {
    third_type: row.upload_params.third_type || 'N11',
    task_detail_id: row.id,
    params: paramsString,
    status_info: {
      status: row.status,
      status_text: row.status_text,
      status_type: row.status_type,
      third_result: row.third_result
    }
  }
  paramsDialogVisible.value = true
}

// 查看原商品
const handleViewSource = async (row: TaskDetail) => {
  if (!row.goods_info?.source_url) return
  
  try {
    await ElMessageBox.confirm(
      '如果您设置了自动采集商品并且已经选择了目录，浏览该商品将会自动采集入库。您可以先设置为不采集商品再浏览商品详情。您确定要浏览该商品吗？',
      '浏览商品确认',
      {
        confirmButtonText: '确定浏览',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )
    
    window.open(row.goods_info.source_url, '_blank')
  } catch (error) {
    // 用户取消了操作
    console.log('用户取消了浏览商品')
  }
}

// 查询结果
const handleQueryResults = async () => {
  if (!statistics.value || statistics.value.processing_count === 0) {
    ElMessage.warning('没有需要查询结果的记录')
    return
  }

  queryLoading.value = true
  queryProgress.show = true
  queryProgress.message = '正在获取待查询任务列表...'
  queryProgress.processed = 0
  queryProgress.total = statistics.value.processing_count
  queryProgress.status = 'success'

  try {
    let offset = 0
    let totalProcessed = 0
    let totalUpdated = 0
    const batchSize = 500

    while (true) {
      // 获取待查询的任务列表
      queryProgress.message = `正在获取第 ${Math.floor(offset / batchSize) + 1} 批任务列表...`
      
      const pendingResponse = await getPendingQueryTasks(props.taskId, batchSize, offset)
      const taskGroups = pendingResponse.task_groups || []
      
      if (taskGroups.length === 0) {
        break
      }

      // 计算当前批次的总记录数
      const currentBatchTotal = taskGroups.reduce((sum, group) => sum + group.details.length, 0)
      let currentBatchProcessed = 0

      // 处理每个任务组
      for (let i = 0; i < taskGroups.length; i++) {
        const group = taskGroups[i]
        const { third_task_id, app_key, app_secret, details } = group
        
        // 构建进度显示信息
        const startIndex = currentBatchProcessed + 1
        const endIndex = currentBatchProcessed + details.length
        let progressText = ''
        
        if (details.length === 1) {
          // 单条记录
          progressText = `正在查询第 ${startIndex}/${currentBatchTotal} 条记录（当前批次）`
        } else {
          // 多条记录
          progressText = `正在查询 ${startIndex}-${endIndex}/${currentBatchTotal} 条记录（当前批次）`
        }
        
        queryProgress.message = progressText
        
        try {
          // 调用N11 API查询任务结果
          const n11Result = await queryN11TaskDetails(app_key, app_secret, third_task_id)
          
          // 处理查询结果
          const updates = processN11QueryResult(n11Result, details)
          
          // 批量更新任务状态
          if (updates.length > 0) {
            await batchUpdateTaskDetails(updates)
            totalUpdated += updates.length
          }
          
          totalProcessed += details.length
          currentBatchProcessed += details.length
          queryProgress.processed = totalProcessed
          
        } catch (error: any) {
          console.error(`查询任务组 ${third_task_id} 失败:`, error)
          
          // 标记该组任务为查询失败
          const failedUpdates = details.map(detail => ({
            detail_id: detail.id,
            status: 3,
            third_status: 'QUERY_FAILED',
            third_result: 'N11 API查询失败: ' + (error.message || '未知错误'),
            memo: 'N11 API查询异常'
          }))
          
          await batchUpdateTaskDetails(failedUpdates)
          totalProcessed += details.length
          currentBatchProcessed += details.length
          queryProgress.processed = totalProcessed
        }
      }

      // 检查是否还有更多数据
      if (!pendingResponse.has_more) {
        break
      }
      
      offset += batchSize
    }
    
    queryProgress.message = `查询完成！共处理 ${totalProcessed} 条记录，成功更新 ${totalUpdated} 条记录（全部待查询记录）`
    queryProgress.status = 'success'
    
    ElMessage.success(`任务结果查询完成！共处理 ${totalProcessed} 条记录，成功更新 ${totalUpdated} 条`)
    
    // 刷新数据
    await Promise.all([
      loadTaskDetail(),
      loadTaskDetailList()
    ])
    
  } catch (error: any) {
    console.error('查询任务结果失败:', error)
    queryProgress.status = 'exception'
    queryProgress.message = '查询失败: ' + (error.message || '未知错误')
    ElMessage.error('查询任务结果失败: ' + (error.message || '未知错误'))
  } finally {
    queryLoading.value = false
    // 5秒后隐藏进度条
    setTimeout(() => {
      queryProgress.show = false
    }, 5000)
  }
}

// 单个商品重新上传
const handleRetryUpload = async (row: TaskDetail) => {
  if (row.status !== 3) {
    ElMessage.warning('只有上传失败的商品才能重新上传')
    return
  }

  // 重置进度数据
  resetProgressData()
  progressData.taskTitle = '重新上传商品'
  progressData.totalProgress = 1
  progressData.isProcessing = true
  progressDialogVisible.value = true

  try {
    // 获取重新上传参数
    const uploadParams = await getRetryUploadParams(row.id)
    
            // 设置当前处理的商品
        progressData.currentProduct = {
          taskDetailId: row.id,
          productName: uploadParams.goods_name || row.goods_name,
          productImage: uploadParams.thumb_url || row.thumb_url,
          price: `${uploadParams.price_third} ${uploadParams.currentcy}`,
          success: false,
          message: '正在处理中...'
        }

    progressData.processingMessage = '正在重新上传商品...'

    // 动态导入任务处理器
    const { processSingleRetryUpload } = await import('../utils/taskProcessor')
    
    const result = await processSingleRetryUpload(row.id, {
      onProductStart: (product) => {
        progressData.currentProduct = {
          ...progressData.currentProduct!,
          message: '正在上传到N11平台...'
        }
      },
      onProductComplete: (product) => {
        progressData.currentProduct = {
          ...progressData.currentProduct!,
          success: product.success,
          message: product.message
        }
        
        // 添加到历史记录
        progressData.processHistory.push({
          ...progressData.currentProduct!
        })
        
        progressData.currentProgress = 1
        
        if (product.success) {
          ElMessage.success(`商品重新上传成功：${product.productName}`)
        } else {
          ElMessage.error(`商品重新上传失败：${product.productName} - ${product.message}`)
        }
      },
      onError: (error) => {
        progressData.hasError = true
        progressData.errorMessage = `重新上传失败：${error}`
        
        if (progressData.currentProduct) {
          progressData.currentProduct.success = false
          progressData.currentProduct.message = error
          progressData.processHistory.push({
            ...progressData.currentProduct
          })
        }
        
        ElMessage.error('重新上传失败: ' + error)
      }
    })

    if (result.success) {
      progressData.isCompleted = true
      progressData.isProcessing = false
      
      // 刷新数据
      await Promise.all([
        loadTaskDetail(),
        loadTaskDetailList()
      ])
    } else {
      progressData.hasError = true
      progressData.isProcessing = false
    }

  } catch (error: any) {
    console.error('重新上传失败:', error)
    progressData.hasError = true
    progressData.isProcessing = false
    progressData.errorMessage = '重新上传失败: ' + (error.message || '未知错误')
    
    if (progressData.currentProduct) {
      progressData.currentProduct.success = false
      progressData.currentProduct.message = error.message || '未知错误'
      progressData.processHistory.push({
        ...progressData.currentProduct
      })
    }
    
    ElMessage.error('重新上传失败: ' + (error.message || '未知错误'))
  }
}

// 批量重新上传失败任务
const handleBatchRetryUpload = async () => {
  if (!statistics.value || statistics.value.failed_count_retry === 0) {
    ElMessage.warning('没有失败的任务需要重新上传')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要重新上传所有失败的任务吗？共 ${statistics.value.failed_count_retry} 个任务。`,
      '批量重新上传确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
  } catch {
    return
  }

  // 重置进度数据和取消标志
  resetProgressData()
  cancelUpload = false
  progressData.taskTitle = '批量重新上传失败任务'
  progressData.totalProgress = statistics.value.failed_count_retry
  progressData.isProcessing = true
  progressDialogVisible.value = true

    try {
    // 调用后端接口获取失败任务详情
    const { batchRetryUpload } = await import('../utils/taskApi')
    const batchResult = await batchRetryUpload(props.taskId)
    
    if (batchResult.valid_count === 0) {
      ElMessage.warning(batchResult.message || '没有找到可重新上传的任务')
      progressData.hasError = true
      progressData.errorMessage = batchResult.message || '没有找到可重新上传的任务'
      progressData.isProcessing = false
      return
    }

    const validTasks = batchResult.valid_details || []
    progressData.totalProgress = validTasks.length
    progressData.processingMessage = `正在批量重新上传 ${validTasks.length} 个失败任务...`

    // 显示无效任务信息
    if (batchResult.invalid_count > 0) {
      const invalidMessages = batchResult.invalid_details.map((item: any) => 
        `${item.goods_name}: ${item.reason}`
      ).join('\n')
      ElMessage.warning(`${batchResult.invalid_count} 个任务无法重新上传:\n${invalidMessages}`)
    }

    // 动态导入任务处理器
    const { processSingleRetryUpload } = await import('../utils/taskProcessor')
    
    let successCount = 0
    let failedCount = 0

    // 逐个处理失败的任务
    for (let i = 0; i < validTasks.length; i++) {
      if (cancelUpload) {
        progressData.hasError = true
        progressData.errorMessage = '用户取消了上传任务'
        progressData.isProcessing = false
        break
      }

      const task = validTasks[i]
      
      try {
        // 设置当前处理的商品
        progressData.currentProduct = {
          taskDetailId: task.id,
          productName: task.goods_name,
          productImage: task.thumb_url,
          price: `${task.price_third} ${task.currentcy}`,
          success: false,
          message: '正在处理中...'
        }

        progressData.processingMessage = `正在处理第 ${i + 1}/${validTasks.length} 个商品...`

        const result = await processSingleRetryUpload(task.id, {
          onProductStart: (product) => {
            progressData.currentProduct = {
              ...progressData.currentProduct!,
              message: '正在上传到N11平台...'
            }
          },
          onProductComplete: (product) => {
            progressData.currentProduct = {
              ...progressData.currentProduct!,
              success: product.success,
              message: product.message
            }
            
            // 添加到历史记录
            progressData.processHistory.push({
              ...progressData.currentProduct!
            })
            
            if (product.success) {
              successCount++
            } else {
              failedCount++
            }
            
            progressData.currentProgress = i + 1
          },
          onError: (error) => {
            if (progressData.currentProduct) {
              progressData.currentProduct.success = false
              progressData.currentProduct.message = error
              progressData.processHistory.push({
                ...progressData.currentProduct
              })
            }
            failedCount++
            progressData.currentProgress = i + 1
          }
        })

      } catch (error: any) {
        console.error(`处理任务 ${task.id} 失败:`, error)
        
        if (progressData.currentProduct) {
          progressData.currentProduct.success = false
          progressData.currentProduct.message = error.message || '处理失败'
          progressData.processHistory.push({
            ...progressData.currentProduct
          })
        }
        
        failedCount++
        progressData.currentProgress = i + 1
      }
    }

    // 完成处理
    if (!cancelUpload) {
      progressData.isCompleted = true
      progressData.isProcessing = false
      progressData.processingMessage = `批量重新上传完成！成功: ${successCount}, 失败: ${failedCount}`
      
      ElMessage.success(`批量重新上传完成！成功: ${successCount}, 失败: ${failedCount}`)
      
      // 刷新数据
      await Promise.all([
        loadTaskDetail(),
        loadTaskDetailList()
      ])
    }

  } catch (error: any) {
    console.error('批量重新上传失败:', error)
    progressData.hasError = true
    progressData.isProcessing = false
    progressData.errorMessage = '批量重新上传失败: ' + (error.message || '未知错误')
    ElMessage.error('批量重新上传失败: ' + (error.message || '未知错误'))
  }
}



// 重置进度数据
const resetProgressData = () => {
  Object.assign(progressData, {
    taskTitle: '',
    currentProgress: 0,
    totalProgress: 0,
    currentProduct: null,
    isProcessing: false,
    isCompleted: false,
    hasError: false,
    errorMessage: '',
    processingMessage: '正在处理商品...',
    processHistory: []
  })
}

// 处理进度对话框关闭
const handleProgressDialogClose = () => {
  progressDialogVisible.value = false
  resetProgressData()
}

// 处理进度对话框取消
const handleProgressDialogCancel = () => {
  cancelUpload = true
  ElMessage.info('正在取消上传任务...')
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return dateTime.substring(0, 16) // 只显示到分钟
}

// 复制参数到剪贴板
const copyParams = async () => {
  if (!currentParams.value || !currentParams.value.params) {
    ElMessage.warning('没有可复制的参数')
    return
  }
  
  try {
    let copyText = '=== 上传参数详情 ===\n\n'
    
    // 添加状态信息
    if (currentParams.value.status_info) {
      copyText += `上传状态: ${currentParams.value.status_info.status_text}\n`
      copyText += `第三方类型: ${currentParams.value.third_type}\n`
      copyText += `任务详情ID: ${currentParams.value.task_detail_id}\n\n`
      
      // 如果是失败状态，添加错误信息
      if (currentParams.value.status_info.status === 3 && currentParams.value.status_info.third_result) {
        copyText += '=== 接口返回错误信息 ===\n'
        copyText += currentParams.value.status_info.third_result + '\n\n'
      }
    }
    
    copyText += '=== 请求参数 ===\n'
    copyText += formatParams(currentParams.value.params)
    
    await navigator.clipboard.writeText(copyText)
    ElMessage.success('参数详情已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择复制')
  }
}

// 复制状态信息到剪贴板
const copyStatusInfo = async (row: TaskDetail) => {
  try {
    let copyText = '=== 商品上传详情 ===\n\n'
    copyText += `商品名称: ${row.goods_name}\n`
    copyText += `上传状态: ${row.status_text}\n`
    copyText += `第三方任务ID: ${row.third_task_id || '无'}\n`
    copyText += `第三方类型: ${row.third_type || '无'}\n`
    copyText += `第三方状态: ${row.third_status || '无'}\n\n`
    
    // 添加错误信息
    if (row.third_result) {
      copyText += '=== 接口返回错误信息 ===\n'
      copyText += row.third_result + '\n\n'
    }
    
    // 添加上传参数
    if (row.upload_params) {
      copyText += '=== 上传参数 ===\n'
      try {
        const parsedParams = JSON.parse(row.upload_params.params)
        parsedParams.task_detail_id = row.id
        copyText += JSON.stringify(parsedParams, null, 2)
      } catch (error) {
        copyText += row.upload_params.params
      }
    }
    
    await navigator.clipboard.writeText(copyText)
    ElMessage.success('商品上传错误信息已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择复制')
  }
}

// 获取图片URL，提供默认图片
const getImageUrl = (url: string | null | undefined) => {
  if (!url || url.trim() === '') {
    // 返回默认占位图片
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0MFY0MEgyMFYyMFoiIGZpbGw9IiNEREREREQiLz4KPC9zdmc+'
  }
  return url
}

// 获取预览图片列表
const getPreviewList = (url: string | null | undefined) => {
  if (!url || url.trim() === '') {
    return []
  }
  return [url]
}

// 处理图片加载错误
const handleImageError = (error: Event) => {
  console.warn('图片加载失败:', error)
}

// 处理采集设置更新
const handleSettingsUpdated = (settings: CollectionSettings) => {
  console.log('采集设置已更新:', settings)
}

// 组件挂载时加载数据
onMounted(() => {
  if (!props.taskId) {
    ElMessage.error('任务ID无效')
    goBack()
    return
  }
  
  loadTaskDetail()
  loadTaskDetailList()
})
</script>

<style scoped>
.task-detail {
  padding: 20px;
}

.breadcrumb-section {
  margin-bottom: 20px;
}

.custom-breadcrumb {
  display: flex;
  align-items: center;
  font-size: 18px;
}

.breadcrumb-back-button {
  font-size: 18px;
  color: #409EFF;
  padding: 0;
  margin-right: 8px;
}

.breadcrumb-back-button .el-icon {
  margin-right: 4px;
}

.breadcrumb-back-button:hover,
.breadcrumb-back-button:focus {
  color: #66b1ff;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #C0C4CC;
}

.breadcrumb-current-page {
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
  max-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-info-section {
  margin-bottom: 20px;
}

/* 响应式布局 */
.task-info-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 1200px) {
  .task-info-layout {
    grid-template-columns: 1fr;
  }
}

.task-info-card {
  border-radius: 8px;
}

.collection-settings-card {
  border-radius: 8px;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-row {
  display: flex;
  align-items: center;
}

.time-info-column {
  font-size: 12px;
}

.time-info-column .time-row {
  margin-bottom: 2px;
}

.time-info-column .time-row:last-child {
  margin-bottom: 0;
}

.time-label {
  color: #666;
  min-width: 35px;
}

.time-value {
  color: #333;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.task-info-content {
  padding: 10px 0;
}

.info-row {
  display: flex;
  margin-bottom: 15px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: #333;
}

.statistics-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.success {
  border-color: #67c23a;
}

.stat-card.danger {
  border-color: #f56c6c;
}

.stat-card.warning {
  border-color: #e6a23c;
}

.stat-card.info {
  border-color: #909399;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.goods-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.goods-image {
  flex-shrink: 0;
}

.goods-image .image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.goods-image .image-slot.error {
  background: #fef0f0;
  color: #f56c6c;
}

.goods-image .image-slot .el-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.goods-image .image-slot span {
  font-size: 10px;
}

.goods-details {
  flex: 1;
  min-width: 0;
}

.goods-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goods-spec {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.goods-price {
  font-size: 12px;
  color: #409eff;
}

/* N11分类信息样式 */
.n11-category-info {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e4e7ed;
}

.platform-title {
  font-size: 12px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.category-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 6px 8px;
  border-left: 3px solid #28a745;
}

.category-names {
  margin-bottom: 3px;
}

.name-cn {
  font-size: 11px;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 1px;
}

.name-tl {
  font-size: 10px;
  color: #666;
  font-style: italic;
  display: block;
}

.category-paths {
  font-size: 10px;
  line-height: 1.3;
}

.path-cn {
  color: #666;
  display: block;
  margin-bottom: 1px;
}

.path-tl {
  color: #999;
  font-style: italic;
  display: block;
}

/* 备注内容样式 */
.memo-content {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.4;
  max-width: 180px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-breadcrumb {
    font-size: 16px;
  }
  
  .breadcrumb-back-button {
    font-size: 16px;
  }
  
  .info-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .memo-content {
    max-width: 150px;
  }
}

/* 查询进度样式 */
.query-progress {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.query-progress .el-alert {
  margin-bottom: 0;
}

.query-progress .el-progress {
  margin-top: 10px;
}

/* 状态列样式 */
.status-column {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.error-details {
  width: 100%;
  margin-top: 8px;
  padding: 8px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

.error-content {
  font-size: 12px;
  color: #f56c6c;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  margin-bottom: 8px;
  max-height: 120px;
  overflow-y: auto;
}

.copy-error-btn {
  padding: 4px 8px;
  font-size: 12px;
}

.copy-error-btn .el-icon {
  margin-right: 4px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* 按钮之间的间距 */
  align-items: flex-start; /* 确保换行时顶部对齐 */
  justify-content: flex-start; /* 确保内容从左侧开始排列 */
}

/* 重置Element Plus按钮的默认margin，确保使用flex gap控制间距 */
.operation-buttons .el-button + .el-button {
  margin-left: 0;
}
</style> 