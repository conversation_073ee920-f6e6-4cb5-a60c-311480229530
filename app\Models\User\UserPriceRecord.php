<?php
declare(strict_types=1);

namespace App\Models\User;
use App\Models\BaseModel;
use App\Models\Course\Course;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserPriceRecord extends BaseModel
{
    protected $table = 'jk_user_price_record';

    public function goods()
    {
        return $this->belongsTo(Course::class,'good_id','id');
    }

    public function buy_user()
    {
        return $this->belongsTo(User::class,'buy_user_id','id');
    }

    protected function ctime(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value > 0 ? date('Y-m-d H:i:s',(int)$value) : ''
        );
    }

}
