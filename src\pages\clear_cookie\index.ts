// 在页面加载前清除所有cookie
(async () => {
  try {
    // 获取当前域名
    const hostname = window.location.hostname;

    // 发送消息给background script来清除cookie
    const response = await chrome.runtime.sendMessage({
      action: 'clearCookies',
      hostname: hostname
    });

    if (response.success) {
      console.log(`已清除域名 ${hostname} 下的 ${response.count} 个cookie`);
    } else {
      console.error('清除cookie失败:', response.error);
    }
  } catch (error) {
    console.error('清除cookie失败:', error);
  }
})();
