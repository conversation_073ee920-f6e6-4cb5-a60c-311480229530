<?php

namespace App\Console\Commands;

use App\Models\N11\ProductCategoryN11Model;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

class UpdateN11CategoryNamesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'n11:update-category-names';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '从Excel文件更新N11商品分类的中文名称';

    /**
     * Excel数据
     */
    private $excelData;

    /**
     * 统计信息
     */
    private $stats = [
        'excel_total' => 0,
        'mysql_total' => 0,
        'success_count' => 0,
        'failed_count' => 0,
        'no_need_update_count' => 0,
        'failed_ids' => []
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始处理N11商品分类名称更新...');
        
        // 1. 读取Excel文件
        if (!$this->readExcelFile()) {
            return Command::FAILURE;
        }

        // 2. 统计数据
        $this->countRecords();

        // 3. 显示统计信息并确认
        if (!$this->confirmExecution()) {
            $this->info('操作已取消');
            return Command::SUCCESS;
        }

        // 4. 执行更新
        $this->executeUpdate();

        // 5. 显示结果统计
        $this->showResults();

        // 6. 保存失败记录
        $this->saveFailedRecords();

        return Command::SUCCESS;
    }

    /**
     * 读取Excel文件
     */
    private function readExcelFile(): bool
    {
        $filePath = storage_path('excel/n11.xlsx');
        
        if (!file_exists($filePath)) {
            $this->error("Excel文件不存在: {$filePath}");
            return false;
        }

        try {
            $this->info('正在读取Excel文件...');
            
            $this->excelData = Excel::toCollection(null, $filePath)->first();
            
            // 移除标题行（第一行）
            $this->excelData = $this->excelData->skip(1)->filter(function ($row) {
                // 过滤掉ID为空的行
                return !empty($row[0]);
            });

            $this->info("Excel文件读取完成，共 {$this->excelData->count()} 条有效记录");
            return true;
            
        } catch (\Exception $e) {
            $this->error("读取Excel文件失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 统计记录数量
     */
    private function countRecords(): void
    {
        $this->info('正在统计记录数量...');
        
        $this->stats['excel_total'] = $this->excelData->count();
        $this->stats['mysql_total'] = ProductCategoryN11Model::count();
        
        $this->info("Excel记录总数: {$this->stats['excel_total']}");
        $this->info("MySQL记录总数: {$this->stats['mysql_total']}");
    }

    /**
     * 确认执行
     */
    private function confirmExecution(): bool
    {
        $this->newLine();
        $this->info('=== 数据统计 ===');
        $this->info("Excel文件记录数: {$this->stats['excel_total']}");
        $this->info("MySQL表记录数: {$this->stats['mysql_total']}");
        $this->newLine();
        
        return $this->confirm('确认要执行名称替换操作吗？');
    }

    /**
     * 执行更新操作
     */
    private function executeUpdate(): void
    {
        $this->info('开始执行更新操作...');
        
        $progressBar = $this->output->createProgressBar($this->stats['excel_total']);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始处理...');
        $progressBar->start();

        $current = 0;
        
        foreach ($this->excelData as $row) {
            $current++;
            $id = (int) $row[0]; // A列：ID
            $chineseName = trim($row[2] ?? ''); // C列：中文名称
            
            if (empty($chineseName)) {
                $this->stats['failed_count']++;
                $this->stats['failed_ids'][] = $id;
                
                $remaining = $this->stats['excel_total'] - $current;
                $progressBar->setMessage("ID: {$id} | 状态: 中文名称为空，跳过 | 进度: {$current}/{$this->stats['excel_total']} | 剩余: {$remaining}");
                $progressBar->advance();
                continue;
            }

            try {
                // 先查询当前记录的name值
                $currentRecord = ProductCategoryN11Model::where('id', $id)->first();
                
                if (!$currentRecord) {
                    $this->stats['failed_count']++;
                    $this->stats['failed_ids'][] = $id;
                    
                    $remaining = $this->stats['excel_total'] - $current;
                    $progressBar->setMessage("ID: {$id} | 状态: 记录不存在 | 进度: {$current}/{$this->stats['excel_total']} | 剩余: {$remaining}");
                    $progressBar->advance();
                    continue;
                }
                
                // 检查是否需要更新
                if ($currentRecord->name === $chineseName) {
                    $this->stats['no_need_update_count']++;
                    
                    $remaining = $this->stats['excel_total'] - $current;
                    $progressBar->setMessage("ID: {$id} | 状态: 无需更新 | 当前值: {$chineseName} | 进度: {$current}/{$this->stats['excel_total']} | 剩余: {$remaining}");
                    $progressBar->advance();
                    continue;
                }
                
                // 执行更新
                $updated = ProductCategoryN11Model::where('id', $id)
                    ->update(['name' => $chineseName]);
                
                if ($updated) {
                    $this->stats['success_count']++;
                    $status = "更新成功";
                    $oldName = $currentRecord->name;
                    $remaining = $this->stats['excel_total'] - $current;
                    $progressBar->setMessage("ID: {$id} | 状态: {$status} | 原值: {$oldName} -> 新值: {$chineseName} | 进度: {$current}/{$this->stats['excel_total']} | 剩余: {$remaining}");
                } else {
                    $this->stats['failed_count']++;
                    $this->stats['failed_ids'][] = $id;
                    
                    $remaining = $this->stats['excel_total'] - $current;
                    $progressBar->setMessage("ID: {$id} | 状态: 更新失败 | 目标值: {$chineseName} | 进度: {$current}/{$this->stats['excel_total']} | 剩余: {$remaining}");
                }
                
            } catch (\Exception $e) {
                $this->stats['failed_count']++;
                $this->stats['failed_ids'][] = $id;
                
                $remaining = $this->stats['excel_total'] - $current;
                $progressBar->setMessage("ID: {$id} | 状态: 异常错误 | 错误: {$e->getMessage()} | 进度: {$current}/{$this->stats['excel_total']} | 剩余: {$remaining}");
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);
    }

    /**
     * 显示结果统计
     */
    private function showResults(): void
    {
        $this->info('=== 更新结果统计 ===');
        $this->info("Excel记录总数: {$this->stats['excel_total']}");
        $this->info("MySQL记录总数: {$this->stats['mysql_total']}");
        $this->info("更新成功: {$this->stats['success_count']}");
        $this->info("无需更新: {$this->stats['no_need_update_count']}");
        $this->info("更新失败: {$this->stats['failed_count']}");
        
        if ($this->stats['failed_count'] > 0) {
            $this->warn("失败的ID数量: " . count($this->stats['failed_ids']));
        }
        
        // 计算处理率
        $processedTotal = $this->stats['success_count'] + $this->stats['no_need_update_count'] + $this->stats['failed_count'];
        if ($processedTotal > 0) {
            $successRate = round(($this->stats['success_count'] + $this->stats['no_need_update_count']) / $processedTotal * 100, 2);
            $this->info("处理成功率: {$successRate}%");
        }
    }

    /**
     * 保存失败记录到文件
     */
    private function saveFailedRecords(): void
    {
        if (empty($this->stats['failed_ids'])) {
            $this->info('没有失败记录需要保存');
            return;
        }

        $failedContent = [
            '更新失败统计',
            '生成时间: ' . date('Y-m-d H:i:s'),
            '失败总数: ' . $this->stats['failed_count'],
            '失败的ID列表:',
            implode(',', $this->stats['failed_ids'])
        ];

        $fileName = 'n11_category_update_failed_' . date('YmdHis') . '.txt';
        $filePath = storage_path('logs/' . $fileName);
        
        // 确保目录存在
        if (!is_dir(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        file_put_contents($filePath, implode("\n", $failedContent));
        
        $this->info("失败记录已保存到: {$filePath}");
    }
} 