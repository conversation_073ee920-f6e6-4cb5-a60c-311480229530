import { ref, reactive } from 'vue'
import { getUserInfo, saveUserInfo } from './storage'
import { fetchUserInfo } from './api'
import { setLoginExpired } from './loginState'
import { ElNotification } from 'element-plus'

interface UserInfo {
  isLogin: boolean;
  phone: string;
  expiryDate: string;
  isVip: boolean;
  isAdmin: boolean;
  token: string;
}

// 用户信息状态
export const userInfo = reactive<UserInfo>({
  isLogin: false,
  phone: '',
  expiryDate: '',
  isVip: false,
  isAdmin: false,
  token: ''
})

// 用户信息加载状态
export const isUserInfoLoading = ref(false)

// 事件监听器
const eventListeners: Array<() => void> = []

/**
 * 添加用户信息变化监听器
 */
export const onUserInfoChange = (callback: () => void) => {
  eventListeners.push(callback)
  
  // 返回取消监听的函数
  return () => {
    const index = eventListeners.indexOf(callback)
    if (index > -1) {
      eventListeners.splice(index, 1)
    }
  }
}

/**
 * 触发用户信息变化事件
 */
const triggerUserInfoChange = () => {
  eventListeners.forEach(callback => {
    try {
      callback()
    } catch (error) {
      console.error('用户信息变化监听器执行失败:', error)
    }
  })
}

/**
 * 更新用户信息
 */
export const updateUserInfo = (newUserInfo: Partial<UserInfo>) => {
  Object.assign(userInfo, newUserInfo)
  triggerUserInfoChange()
}

/**
 * 清空用户信息
 */
export const clearUserInfo = () => {
  updateUserInfo({
    isLogin: false,
    phone: '',
    expiryDate: '',
    isVip: false,
    isAdmin: false,
    token: ''
  })
}

/**
 * 从缓存加载用户信息
 */
export const loadUserInfoFromCache = async () => {
  try {
    const cachedInfo = await getUserInfo()
    
    if (cachedInfo.isLogin && cachedInfo.phone) {
      Object.assign(userInfo, {
        isLogin: true,
        phone: cachedInfo.phone,
        expiryDate: cachedInfo.expiryDate || '',
        isVip: cachedInfo.isVip || false,
        isAdmin: cachedInfo.isAdmin || false,
        token: cachedInfo.token || ''
      })
      
      console.log('从缓存加载用户信息成功:', {
        phone: userInfo.phone,
        isVip: userInfo.isVip,
        isAdmin: userInfo.isAdmin,
        expiryDate: userInfo.expiryDate
      })
    }
  } catch (error) {
    console.error('从缓存加载用户信息失败:', error)
  }
}

/**
 * 从API获取并更新用户信息（只在App.vue中调用）
 */
export const fetchAndUpdateUserInfo = async () => {
  if (isUserInfoLoading.value) {
    console.log('用户信息正在加载中，跳过重复请求')
    return
  }

  try {
    isUserInfoLoading.value = true
    
    // 首先从缓存获取基本信息
    const cachedInfo = await getUserInfo()
    
    // 如果有token，则请求后端验证用户信息
    if (cachedInfo.token) {
      try {
        console.log('正在从后端验证用户信息...')
        const apiResponse = await fetchUserInfo()
        if (apiResponse && apiResponse.phone) {
          const serverUserInfo = apiResponse
          
          // 更新用户信息
          const newUserInfo = {
            isLogin: true,
            phone: serverUserInfo.phone || '',
            expiryDate: serverUserInfo.vip_end_time || '',
            isVip: serverUserInfo.is_vip === 1 || false,
            isAdmin: serverUserInfo.is_admin === 1 || false,
            token: cachedInfo.token
          }
          
          updateUserInfo(newUserInfo)
          
          // 同步更新缓存
          await saveUserInfo(newUserInfo)
          
          console.log('用户信息已从后端更新:', serverUserInfo)
        }
      } catch (apiError) {
        ElNotification({
          title: '提示',
          message: '系统错误,请联系管理员',
          type: 'error',
          duration: 3000
        });
        return false;
      }
    } else {
      // 没有token，使用缓存信息
      updateUserInfo({
        isLogin: cachedInfo.isLogin || false,
        phone: cachedInfo.phone || '',
        expiryDate: cachedInfo.expiryDate || '',
        isVip: cachedInfo.isVip || false,
        isAdmin: cachedInfo.isAdmin || false,
        token: ''
      })
      console.log('未找到token，使用缓存用户信息')
    }

    console.log('用户信息加载完成:', {
      isLogin: userInfo.isLogin,
      phone: userInfo.phone,
      isVip: userInfo.isVip,
      hasToken: !!userInfo.token
    })
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElNotification({
      title: '错误',
      message: '加载用户信息失败',
      type: 'error',
      duration: 3000
    })
  } finally {
    isUserInfoLoading.value = false
  }
}

/**
 * 处理用户退出登录
 */
export const handleUserLogout = async () => {
  console.log('收到用户退出登录通知')
  
  // 清空用户信息
  clearUserInfo()
  
  // 清除存储的登录信息
  await saveUserInfo({
    isLogin: false,
    phone: '',
    expiryDate: '',
    isVip: false,
    isAdmin: false,
    token: ''
  })
  
  // 设置登录失效状态
  setLoginExpired(true)
  
  // 跳转到登录失效页面
  if (window.location.hash !== '#/login-expired') {
    window.location.hash = '#/login-expired'
  }
  
  ElNotification({
    title: '提示',
    message: '您已退出登录，请重新登录',
    type: 'warning',
    duration: 3000
  })
} 