export const checkSecurityVerification = (html: string, matchString: string = '安全验证'): boolean => {
    // 使用非贪婪正则移除所有script标签及其内容
    html = html.replace(/<script>.*?<\/script>/gs, '');
    return html.includes(matchString);
};

/**
 * 发送安全验证提示通知到background
 */
export const showSecurityVerificationNotification = () => {
  chrome.runtime.sendMessage({
    funType: 'securityVerification',
    message: '商品不存在或者触发安全验证'
  }, (response: { success: boolean; notificationId: string } | undefined) => {
    console.log('安全验证提示已发送', response);
  });
}; 

export const handleTemuSecurityVerification = (html: string,matchString: string = '安全验证') => {
    if(checkSecurityVerification(html,matchString)){
        showSecurityVerificationNotification();
        return false;
    }
    return true;
}
