/**
 * 第三方平台分类管理API接口
 */
import { sendRequestViaBackground } from './api'

// 第三方平台分类接口类型定义
export interface ThirdPartyCategory {
  id: number
  name: string
  name_en?: string
  name_tl?: string  // 翻译名称
  parent_id: number
  is_leaf: boolean
  level: number
  path: string
  path_name: string
  path_name_tl?: string  // 翻译路径名称
  sort_order?: number
  status?: number
  status_text?: string
  created_at?: string
  updated_at?: string
  children?: ThirdPartyCategory[]
}

export interface ThirdPartyCategoryListParams {
  page: number
  pageSize: number
  parent_id?: number
  name?: string
}

export interface ThirdPartyCategoryListResponse {
  list: ThirdPartyCategory[]
  pagination: {
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
    currentPage: number
    pageSize: number
  }
}

export interface CategoryLinkParams {
  temu_category_id: number
  temu_category_level: number
  temu_category_is_leaf: boolean
  link_type: number
  platform_id?: number
  platform_code?: string
  relation_status?: number // 将 relation_status 设置为可选
  categories?: Array<{
    id: number
    name: string
    level: number
    is_leaf: boolean
    path_name?: string
  }>
}

export interface UserInfo {
  is_admin: number
  phone: string
  is_vip: number
  vip_end_time: string
}

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      console.log('获取API地址响应:', response)
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 获取用户信息
 * @returns 用户信息响应
 */
export const getUserInfo = async (): Promise<UserInfo> => {
  const url = await getApiUrl('apiUserInfoUrl');
  console.log('获取用户信息URL:', url)
  return sendRequestViaBackground({
    funName: 'getUserInfo',
    url,
    method: 'get',
    auth: true
  });
};

/**
 * 获取N11平台分类列表
 * @param params 查询参数
 * @returns 分类列表响应
 */
export const getN11CategoryList = async (params: ThirdPartyCategoryListParams): Promise<ThirdPartyCategoryListResponse> => {
  const url = await getApiUrl('apiCatN11ListUrl');
  console.log('获取N11分类列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getN11CategoryList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 获取N11分类详情
 * @param categoryId 分类ID
 * @returns 分类详情响应
 */
export const getN11CategoryDetail = async (categoryId: number): Promise<ThirdPartyCategory> => {
  const url = await getApiUrl('apiCatN11DetailUrl');
  console.log('获取N11分类详情URL:', url)
  return sendRequestViaBackground({
    funName: 'getN11CategoryDetail',
    url,
    method: 'get',
    params: { id: categoryId },
    auth: true
  });
};

/**
 * 更新N11分类
 * @param categoryId 分类ID
 * @param updateData 更新数据
 * @returns 更新结果
 */
export const updateN11Category = async (categoryId: number, updateData: { name: string }): Promise<any> => {
  const url = await getApiUrl('apiCatN11UpdateUrl');
  console.log('更新N11分类URL:', url)
  return sendRequestViaBackground({
    funName: 'updateN11Category',
    url,
    method: 'post',
    data: {
      id: categoryId,
      ...updateData
    },
    auth: true
  });
};

/**
 * 关联第三方平台分类
 * @param params 关联参数
 * @returns 关联结果
 */
export const linkThirdPartyCategory = async (params: CategoryLinkParams): Promise<any> => {
  const url = await getApiUrl('apiCatRelationSetUrl');
  console.log('关联分类URL:', url)
  return sendRequestViaBackground({
    funName: 'linkThirdPartyCategory',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};