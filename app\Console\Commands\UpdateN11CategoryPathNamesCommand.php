<?php

namespace App\Console\Commands;

use App\Models\N11\ProductCategoryN11Model;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateN11CategoryPathNamesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'n11:update-path-names';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '根据path字段中的分类ID更新path_name字段';

    /**
     * 统计信息
     */
    private $stats = [
        'total_records' => 0,
        'success_count' => 0,
        'failed_count' => 0,
        'no_need_update_count' => 0,
        'failed_ids' => []
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始处理N11商品分类路径名称更新...');
        
        // 1. 统计记录数量
        $this->countRecords();

        // 2. 显示统计信息并确认
        if (!$this->confirmExecution()) {
            $this->info('操作已取消');
            return Command::SUCCESS;
        }

        // 3. 执行更新
        $this->executeUpdate();

        // 4. 显示结果统计
        $this->showResults();

        // 5. 保存失败记录
        $this->saveFailedRecords();

        return Command::SUCCESS;
    }

    /**
     * 统计记录数量
     */
    private function countRecords(): void
    {
        $this->info('正在统计记录数量...');
        
        $this->stats['total_records'] = ProductCategoryN11Model::whereNotNull('path')
            ->where('path', '!=', '')
            ->count();
        
        $this->info("需要处理的记录总数: {$this->stats['total_records']}");
    }

    /**
     * 确认执行
     */
    private function confirmExecution(): bool
    {
        $this->newLine();
        $this->info('=== 数据统计 ===');
        $this->info("需要处理的记录数: {$this->stats['total_records']}");
        $this->newLine();
        
        return $this->confirm('确认要执行路径名称更新操作吗？');
    }

    /**
     * 执行更新操作
     */
    private function executeUpdate(): void
    {
        $this->info('开始执行更新操作...');
        
        // 分批处理，避免内存溢出
        $batchSize = 100;
        $totalBatches = ceil($this->stats['total_records'] / $batchSize);
        
        $progressBar = $this->output->createProgressBar($this->stats['total_records']);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %message%');
        $progressBar->setMessage('准备开始处理...');
        $progressBar->start();

        $processedCount = 0;
        
        ProductCategoryN11Model::whereNotNull('path')
            ->where('path', '!=', '')
            ->chunk($batchSize, function ($categories) use ($progressBar, &$processedCount) {
                foreach ($categories as $category) {
                    $processedCount++;
                    $this->processCategory($category, $progressBar, $processedCount);
                }
            });

        $progressBar->finish();
        $this->newLine(2);
    }

    /**
     * 处理单个分类记录
     */
    private function processCategory($category, $progressBar, $current): void
    {
        $id = $category->id;
        $path = trim($category->path);
        
        if (empty($path)) {
            $this->stats['failed_count']++;
            $this->stats['failed_ids'][] = $id;
            
            $remaining = $this->stats['total_records'] - $current;
            $progressBar->setMessage("ID: {$id} | 状态: path为空，跳过 | 进度: {$current}/{$this->stats['total_records']} | 剩余: {$remaining}");
            $progressBar->advance();
            return;
        }

        try {
            // 解析path中的ID
            $pathIds = $this->parsePathIds($path);
            
            if (empty($pathIds)) {
                $this->stats['failed_count']++;
                $this->stats['failed_ids'][] = $id;
                
                $remaining = $this->stats['total_records'] - $current;
                $progressBar->setMessage("ID: {$id} | 状态: path解析失败 | path: {$path} | 进度: {$current}/{$this->stats['total_records']} | 剩余: {$remaining}");
                $progressBar->advance();
                return;
            }

            // 查找对应的名称
            $pathNames = $this->getPathNames($pathIds);
            $newPathName = implode(',', $pathNames);
            
            // 检查是否需要更新
            if ($category->path_name === $newPathName) {
                $this->stats['no_need_update_count']++;
                
                $remaining = $this->stats['total_records'] - $current;
                $progressBar->setMessage("ID: {$id} | 状态: 无需更新 | 当前值: {$newPathName} | 进度: {$current}/{$this->stats['total_records']} | 剩余: {$remaining}");
                $progressBar->advance();
                return;
            }
            
            // 执行更新
            $updated = ProductCategoryN11Model::where('id', $id)
                ->update(['path_name' => $newPathName]);
            
            if ($updated) {
                $this->stats['success_count']++;
                $oldPathName = $category->path_name ?? '空';
                $remaining = $this->stats['total_records'] - $current;
                $progressBar->setMessage("ID: {$id} | 状态: 更新成功 | 原值: {$oldPathName} -> 新值: {$newPathName} | 进度: {$current}/{$this->stats['total_records']} | 剩余: {$remaining}");
            } else {
                $this->stats['failed_count']++;
                $this->stats['failed_ids'][] = $id;
                
                $remaining = $this->stats['total_records'] - $current;
                $progressBar->setMessage("ID: {$id} | 状态: 更新失败 | 目标值: {$newPathName} | 进度: {$current}/{$this->stats['total_records']} | 剩余: {$remaining}");
            }
            
        } catch (\Exception $e) {
            $this->stats['failed_count']++;
            $this->stats['failed_ids'][] = $id;
            
            $remaining = $this->stats['total_records'] - $current;
            $progressBar->setMessage("ID: {$id} | 状态: 异常错误 | 错误: {$e->getMessage()} | 进度: {$current}/{$this->stats['total_records']} | 剩余: {$remaining}");
        }
        
        $progressBar->advance();
    }

    /**
     * 解析path中的ID
     */
    private function parsePathIds(string $path): array
    {
        // 如果包含逗号，按逗号分割
        if (strpos($path, ',') !== false) {
            $ids = explode(',', $path);
        } else {
            // 没有逗号，只有一个ID
            $ids = [$path];
        }
        
        // 过滤并转换为整数
        return array_filter(array_map('intval', array_map('trim', $ids)));
    }

    /**
     * 根据ID数组获取对应的名称
     */
    private function getPathNames(array $pathIds): array
    {
        if (empty($pathIds)) {
            return [];
        }
        
        // 批量查询名称
        $categories = ProductCategoryN11Model::whereIn('id', $pathIds)
            ->pluck('name', 'id')
            ->toArray();
        
        // 按原始顺序返回名称
        $names = [];
        foreach ($pathIds as $id) {
            if (isset($categories[$id]) && !empty($categories[$id])) {
                $names[] = $categories[$id];
            } else {
                // 如果找不到对应的名称，使用ID作为占位符
                $names[] = "ID:{$id}";
            }
        }
        
        return $names;
    }

    /**
     * 显示结果统计
     */
    private function showResults(): void
    {
        $this->info('=== 更新结果统计 ===');
        $this->info("处理记录总数: {$this->stats['total_records']}");
        $this->info("更新成功: {$this->stats['success_count']}");
        $this->info("无需更新: {$this->stats['no_need_update_count']}");
        $this->info("更新失败: {$this->stats['failed_count']}");
        
        if ($this->stats['failed_count'] > 0) {
            $this->warn("失败的ID数量: " . count($this->stats['failed_ids']));
        }
        
        // 计算处理率
        $processedTotal = $this->stats['success_count'] + $this->stats['no_need_update_count'] + $this->stats['failed_count'];
        if ($processedTotal > 0) {
            $successRate = round(($this->stats['success_count'] + $this->stats['no_need_update_count']) / $processedTotal * 100, 2);
            $this->info("处理成功率: {$successRate}%");
        }
    }

    /**
     * 保存失败记录到文件
     */
    private function saveFailedRecords(): void
    {
        if (empty($this->stats['failed_ids'])) {
            $this->info('没有失败记录需要保存');
            return;
        }

        $failedContent = [
            '路径名称更新失败统计',
            '生成时间: ' . date('Y-m-d H:i:s'),
            '失败总数: ' . $this->stats['failed_count'],
            '失败的ID列表:',
            implode(',', $this->stats['failed_ids'])
        ];

        $fileName = 'n11_path_name_update_failed_' . date('YmdHis') . '.txt';
        $filePath = storage_path('logs/' . $fileName);
        
        // 确保目录存在
        if (!is_dir(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        file_put_contents($filePath, implode("\n", $failedContent));
        
        $this->info("失败记录已保存到: {$filePath}");
    }
} 