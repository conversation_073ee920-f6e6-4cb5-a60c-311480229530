async function protobufRequest(url: string, headers: any, base64Data: string): Promise<any> {
  try {
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: bytes
    });
    if (response.ok) {
      return await response.json();
    } else {
      throw new Error(`请求失败,状态码: ${response.status}`);
    }
  } catch (error) {
    console.error('请求出错:', error);
    throw error;
  }
}

export default protobufRequest;
