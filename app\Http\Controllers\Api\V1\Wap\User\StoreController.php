<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\StoreService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class StoreController extends Controller
{
    protected StoreService $storeService;

    public function __construct(StoreService $storeService)
    {
        $this->storeService = $storeService;
        parent::__construct();
    }

    /**
     * 获取店铺列表（分页）
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only(['page', 'pageSize', 'account_type', 'account_name','brand','integrator_name', 'status']);
        $result = $this->storeService->getStoreList($userId, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 创建店铺
     */
    public function create(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'account_type', 'account_id', 'account_name', 'account_logo',
            'account_cookie', 'brand', 'price_rate', 'price_add', 'price_subtract', 'app_key', 'app_secret','status',
            'shipment_template', 'integrator_name', 'quantity', 'vat_rate', 'preparing_day'
        ]);
        
        $result = $this->storeService->createStore($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新店铺
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'id', 'account_type', 'account_id', 'account_name', 'account_logo',
            'account_cookie', 'brand', 'price_rate', 'price_add', 'price_subtract', 'app_key', 'app_secret', 'cookie_status','status',
            'shipment_template', 'integrator_name', 'quantity', 'vat_rate', 'preparing_day'
        ]);
        
        $result = $this->storeService->updateStore($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 批量更新店铺
     * 
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $data = $request->only([
            'ids', 'price_rate', 'price_add', 'price_subtract', 'status',
            'shipment_template', 'integrator_name', 'quantity', 'vat_rate', 'preparing_day'
        ]);
        $result = $this->storeService->batchUpdateStore($userId, $data);
        return $this->apiSuccess($result);
    }
    /**
     * 删除店铺
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $storeId = (int)$request->input('id');
        $result = $this->storeService->deleteStore($userId, $storeId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取店铺详情
     */
    public function detail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $storeId = (int)$request->input('id');
        $result = $this->storeService->getStoreDetail($userId, $storeId);
        
        return $this->apiSuccess($result);
    }
} 