@echo off
chcp 65001 >nul
title 检查 background.main.js 文件

echo.
echo ========================================
echo    检查 background.main.js 文件
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 当前目录: %CD%
echo 🔍 检查文件: ..\..\tsa_build\background.main.js
echo.

if exist "..\..\tsa_build\background.main.js" (
    echo ✅ background.main.js 文件存在
    echo 📁 文件路径: %CD%\..\..\tsa_build\background.main.js
    
    echo.
    echo 📊 文件信息:
    for %%F in ("..\..\tsa_build\background.main.js") do (
        echo    文件大小: %%~zF 字节
        echo    修改时间: %%~tF
    )
) else (
    echo ❌ background.main.js 文件不存在
    echo.
    echo 💡 可能的原因:
    echo    1. 项目尚未构建，请先运行: npm run build
    echo    2. 构建目录路径不正确
    echo    3. 文件名可能不同
    echo.
    echo 🔍 检查构建目录内容:
    if exist "..\..\tsa_build" (
        echo    构建目录存在，包含以下文件:
        dir /b "..\..\tsa_build\*.js" 2>nul
        if %errorlevel% neq 0 (
            echo    构建目录中没有 .js 文件
        )
    ) else (
        echo    构建目录不存在: ..\..\tsa_build
    )
)

echo.
echo 按任意键退出...
pause >nul 