<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BaseRequest extends FormRequest
{
    protected $stopOnFirstFailure = true;

    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        $messages = [
            'required'  => ':attribute不能为空',
            'numeric'   => ':attribute只能是数字',
            'integer'   => ':attribute只能是数字',
            'min'       => [
                'numeric' => ':attribute的最小值:min',
                'string'  => ':attribute至少输入:min个字符'
            ],
            'max'       => [
                'numeric' => ':attribute的最大值:max',
                'string'  => ':attribute最多输入:max个字符',
                'file'    => '',
                'array'   => ''
            ],
            'in'        => ':attribute参数错误',
            'regex'     => ':attribute只能由字母数字下划线组成,必须以字母开始',
            'confirmed' => '两次输入的密码不一致'
        ];
        return $messages;
    }

}
