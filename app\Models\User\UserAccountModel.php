<?php
declare(strict_types=1);

namespace App\Models\User;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

class UserAccountModel extends BaseModel
{
    protected $table = 'user_account';

    protected $fillable = [
        'user_id',
        'account_type',
        'account_id',
        'account_name',
        'account_logo',
        'account_cookie',
        'cookie_time',
        'cookie_status',
        'brand',
        'price_rate',
        'price_add',
        'price_subtract',
        'shipment_template',
        'integrator_name',
        'quantity',
        'vat_rate',
        'preparing_day',
        'app_key',
        'app_secret',
        'status',
    ];

    // 账户类型常量
    const ACCOUNT_TYPE_TEMU = 1;
    const ACCOUNT_TYPE_N11 = 2;

    // 账户类型映射
    public static function getAccountTypes(): array
    {
        return [
            self::ACCOUNT_TYPE_TEMU => 'Temu',
            self::ACCOUNT_TYPE_N11 => 'N11',
        ];
    }

    // 获取账户类型名称
    public function getAccountTypeName(): string
    {
        $types = self::getAccountTypes();
        return $types[$this->account_type] ?? '未知';
    }

    // 检查是否属于指定用户
    public function belongsToUser(int $userId): bool
    {
        return $this->user_id === $userId;
    }

    // 作用域：按用户ID筛选
    public function scopeByUserId($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    // 作用域：按账户类型筛选
    public function scopeByAccountType($query, int $accountType)
    {
        return $query->where('account_type', $accountType);
    }

    // 作用域：活跃状态
    public function scopeActive($query)
    {
        return $query->where('cookie_status', 1);
    }
}
