<template>
  <el-dialog
    v-model="visible"
    title="新增商品目录"
    width="400px"
    :close-on-click-modal="false"
    :z-index="10003"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="目录名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入目录名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="目录描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入目录描述（可选）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';

interface DirectoryFormData {
  name: string;
  description: string;
}

interface Props {
  modelValue: boolean;
  loading?: boolean;
}

interface Emits {
  'update:modelValue': [value: boolean];
  'confirm': [data: DirectoryFormData];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const formRef = ref();

const form = reactive({
  name: '',
  description: ''
});

const formRules = {
  name: [
    { required: true, message: '请输入目录名称', trigger: 'blur' },
    { min: 1, max: 50, message: '目录名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    // 重置表单
    form.name = '';
    form.description = '';
  }
});

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

const handleConfirm = async () => {
  try {
    await formRef.value.validate();
    
    emit('confirm', {
      name: form.name.trim(),
      description: form.description.trim()
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleCancel = () => {
  visible.value = false;
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 