<template>
  <div class="task-management">
    <!-- 任务列表视图 -->
    <div v-if="currentView === 'list'">
      <div class="page-header">
        <h2>任务列表</h2>
      </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="完成状态" class="status-item">
          <el-select v-model="searchForm.task_over" placeholder="请选择完成状态" clearable>
            <el-option label="未完成" :value="0" />
            <el-option label="已完成" :value="1" />
          </el-select> 
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset" style="margin-right: 20px;">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table 
        :data="taskList" 
        v-loading="loading"
        stripe
        border
      >
        <el-table-column label="序号" type="index" width="80" :index="getIndex" />
        <el-table-column label="店铺名称" prop="store_name" min-width="150" show-overflow-tooltip />
        <el-table-column label="商品范围" min-width="120">
          <template #default="{ row }">
            <div>目录：{{ row.directory_name || '未知目录' }}</div>
            <div>{{ getProductRange(row) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="商品数量" width="120">
          <template #default="{ row }">
            <div>商品数量：{{ row.goods_count || 0 }}</div>
            <div>SKU数量：{{ row.sku_count || 0 }}</div>
          </template>
        </el-table-column>
        <el-table-column label="任务进度" width="120">
          <template #default="{ row }">
            <div>已完成：{{ row.task_num || 0 }}</div>
            <div>总数量：{{ row.is_selected === 0 && (row.task_over === 0 || row.task_count === 0) ? '--' : (row.task_count || 0) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="时间范围" min-width="180">
          <template #default="{ row }">
            <span>{{ formatTimeRangeDisplay(row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="排序方式" width="100">
          <template #default="{ row }">
            <span>{{ getSortOrderDisplay(row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最近上传" width="140">
          <template #default="{ row }">
            <span>{{ getLatestUploadTime(row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="完成状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.task_over)">
              {{ getStatusName(row.task_over) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="时间信息" width="180">
          <template #default="{ row }">
            <div>创建：{{ row.created_at }}</div>
            <div>更新：{{ row.updated_at }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.task_over === 0"
              type="primary" 
              size="small" 
              @click="handleStartTask(row)" 
              :loading="startingTaskId === row.id"
            >
              开始
            </el-button>
            <el-button 
              v-if="row.task_over === 1"
              type="success" 
              size="small" 
              @click="handleViewDetails(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 任务加载对话框 -->
    <TaskLoadingDialog
      v-model:visible="loadingDialogVisible"
      :title="loadingTitle"
      :message="loadingMessage"
    />

    <!-- 任务进度对话框 -->
    <TaskProgressDialog
      v-model:visible="progressDialogVisible"
      :task-title="`${currentTask?.store_name || ''} - 任务执行进度`"
      :current-progress="taskProgress.currentProgress"
      :total-progress="taskProgress.totalProgress"
      :current-product="taskProgress.currentProduct"
      :is-processing="taskProgress.isProcessing"
      :is-completed="taskProgress.isCompleted"
      :has-error="taskProgress.hasError"
      :error-message="taskProgress.errorMessage"
      :processing-message="taskProgress.processingMessage"
      :process-history="taskProgress.processHistory"
      @close="handleProgressDialogClose"
      @cancel="handleProgressDialogCancel"
    />
    </div>

    <!-- 任务详情视图 -->
    <TaskDetail 
      v-if="currentView === 'detail' && currentTaskId"
      :task-id="currentTaskId"
      @go-back="handleBackToList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { getTaskList, startTask, type Task, type TaskListParams, type TaskStartResponse } from '../utils/taskApi'
import { processNormalTask, updateTaskComplete, type TaskResponse, type ProductUploadResult } from '../utils/taskProcessor'
import TaskLoadingDialog from './TaskLoadingDialog.vue'
import TaskProgressDialog from './TaskProgressDialog.vue'
import TaskDetail from './TaskDetail.vue'

// 响应式数据
const loading = ref(false)
const taskList = ref<Task[]>([])
const dateRange = ref<string[]>([])
const startingTaskId = ref<number | null>(null)

// 对话框状态
const loadingDialogVisible = ref(false)
const progressDialogVisible = ref(false)
const loadingTitle = ref('任务数据加载中')
const loadingMessage = ref('正在处理任务数据，请稍候...')

// 当前任务和商品信息
const currentTask = ref<Task | null>(null)

// 任务进度相关数据
const taskProgress = reactive({
  currentProgress: 0,
  totalProgress: 0,
  isProcessing: false,
  isCompleted: false,
  hasError: false,
  errorMessage: '',
  processingMessage: '正在处理商品...',
  currentProduct: null as ProductUploadResult | null,
  processHistory: [] as ProductUploadResult[]
})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 搜索表单
const searchForm = reactive<{
  task_over: number | null | undefined
  day_start: string | undefined
  day_end: string | undefined
}>({
  task_over: undefined,
  day_start: undefined,
  day_end: undefined
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 获取序号
const getIndex = (index: number) => {
  return (pagination.currentPage - 1) * pagination.pageSize + index + 1
}

// 获取状态名称
const getStatusName = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '已完成',
    0: '未完成'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'warning'
}

// 获取商品范围显示
const getProductRange = (row: Task) => {
  if (row.is_selected === 1) {
    return '指定商品'
  } else {
    // 如果time_range是all，显示"全部"
    if (row.time_range === 'all') {
      return '全部'
    } else {
      return '指定时间范围'
    }
  }
}

// 获取排序方式显示
const getSortOrderDisplay = (row: Task) => {
  if (row.is_selected === 0) {
    const sortOrderMap: Record<string, string> = {
      'desc': '降序',
      'asc': '升序'
    }
    return sortOrderMap[row.sort_order] || '未知'
  }
  return '--'
}

// 获取最近上传时间
const getLatestUploadTime = (row: Task) => {
  return row.latest_time || '--'
}

// 格式化时间范围显示
const formatTimeRangeDisplay = (row: Task) => {
  if (row.is_selected === 1) {
    return '----'
  } else {
    // 根据time_range字段显示不同内容
    switch (row.time_range) {
      case 'all':
        return '全部'
      case 'today':
        return '今天'
      case 'yesterday':
        return '昨天'
      case 'lastweek':
        return '最近一周'
      case 'custom':
        if (row.day_start && row.day_end) {
          if (row.day_start === row.day_end) {
            return row.day_start
          } else {
            return `${row.day_start} 至 ${row.day_end}`
          }
        }
        return '自定义'
      default:
        return '未知'
    }
  }
}

// 开始任务
const handleStartTask = async (row: Task) => {
  try {
    // 设置当前任务
    currentTask.value = row
    startingTaskId.value = row.id
    
    // 显示加载对话框
    loadingTitle.value = '任务数据加载中'
    loadingMessage.value = `正在处理任务"${row.store_name}"，请稍候...`
    loadingDialogVisible.value = true
    
    // 调用开始任务API
    const response: TaskStartResponse = await startTask(row.id)
    
    // 隐藏加载对话框
    loadingDialogVisible.value = false
    
    // 统一在进度组件中处理所有情况
    await handleTaskResponse(response, row)
    
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消了操作
      ElMessage.info('已取消任务开始操作')
    } else {
      console.error('开始任务失败:', error)
      loadingDialogVisible.value = false
      ElMessage.error('开始任务失败，请重试')
    }
  } finally {
    startingTaskId.value = null
  }
}

// 统一处理任务响应
const handleTaskResponse = async (response: TaskStartResponse, row: Task) => {
  // 获取正确的任务总数，优先使用响应中的task_count，其次使用row中的task_count
  const totalTaskCount = response.task_count || row.task_count || 0
  
  // 初始化进度数据
  Object.assign(taskProgress, {
    currentProgress: 0,
    totalProgress: totalTaskCount,
    isProcessing: false,
    isCompleted: false,
    hasError: false,
    errorMessage: '',
    processingMessage: '',
    currentProduct: null,
    processHistory: []
  })

  // 显示进度对话框
  progressDialogVisible.value = true

  if (response.task_over === 1) {
    // 任务已完成
    taskProgress.isCompleted = true
    taskProgress.processingMessage = `任务"${row.store_name}"已全部完成！共处理了 ${totalTaskCount} 个商品。`
    taskProgress.currentProgress = totalTaskCount
    
    // 3秒后自动刷新任务列表
    setTimeout(() => {
      loadTaskList()
      handleProgressDialogClose()
    }, 3000)
    
  } else if (response.goods_no_cat_relation === 1) {
    // 商品分类未关联
    taskProgress.hasError = true
    taskProgress.errorMessage = '商品分类未关联，请先在分类管理中关联相应的N11分类后再试。'
    
  } else if (response.task_exist === 1) {
    // 商品已存在，立即显示商品信息
    const productResult: ProductUploadResult = {
      taskDetailId: response.id || 0,
      success: false,
      message: '商品已存在，跳过处理',
      productName: response.goods_name || '未知商品',
      productImage: response.thumb_url || '',
      price: `${response.price || 0} ${response.currentcy || ''}`
    }
    
    // 立即显示当前处理的商品
    taskProgress.currentProduct = productResult
    taskProgress.processHistory.push({ ...productResult })
    
    // 更新总进度数（如果服务器返回了更准确的数据）
    if (response.task_count && response.task_count > taskProgress.totalProgress) {
      taskProgress.totalProgress = response.task_count
    }
    
    // 使用服务器返回的task_num + 1作为当前进度
    if (response.task_num !== undefined) {
      taskProgress.currentProgress = response.task_num + 1
    } else {
      taskProgress.currentProgress = Math.min(
        taskProgress.currentProgress + 1, 
        taskProgress.totalProgress
      )
    }
    taskProgress.processingMessage = '商品已存在，正在继续处理下一个...'
    
    // 继续下一个
    setTimeout(() => {
      handleContinueNextBatch()
    }, 2000)
    
  } else if (response.task_over === 0 && response.task_exist === 0 && response.goods_no_cat_relation === 0) {
    // 正常情况，开始处理任务
    // 确保response包含必要的字段
    if (response.id && response.task_id) {
      await handleNormalTask(response as TaskResponse, row.store_name)
    } else {
      taskProgress.hasError = true
      taskProgress.errorMessage = '任务响应数据不完整，缺少必要的ID字段'
    }
  }
}

// 检查是否为最后一批任务
const checkIsLastBatch = (taskCount: number, taskNum: number): boolean => {
  return taskNum >= taskCount
}

// 处理正常任务
const handleNormalTask = async (taskResponse: TaskResponse, storeName: string) => {
  try {
    // 更新进度状态
    taskProgress.isProcessing = true
    taskProgress.processingMessage = '正在获取分类属性...'
    // 使用任务响应中的当前进度，但要确保从1开始显示
    taskProgress.currentProgress = taskResponse.task_num + 1

    // 立即显示当前处理的商品信息
    const currentProductInfo: ProductUploadResult = {
      taskDetailId: taskResponse.id,
      success: false, // 初始状态为false，处理完成后会更新
      message: '正在处理中...',
      productName: taskResponse.goods_info.goods_name,
      productImage: taskResponse.thumb_url,
      price: `${taskResponse.price_third} ${taskResponse.currentcy}`
    }
    taskProgress.currentProduct = currentProductInfo

    // 处理任务
    const result = await processNormalTask(taskResponse, (productResult: ProductUploadResult) => {
      // 更新当前商品信息
      taskProgress.currentProduct = productResult
      
      // 添加到处理历史
      taskProgress.processHistory.push({ ...productResult })
      
      // 更新处理消息
      if (productResult.success) {
        taskProgress.processingMessage = '商品上传成功，准备处理下一个...'
      } else {
        taskProgress.processingMessage = '商品上传失败，准备处理下一个...'
      }
    })

    // 处理完成后更新进度
    if (result.progress) {
      taskProgress.currentProgress = result.progress.current
    }

    // 处理完成
    taskProgress.isProcessing = false
    
    // 无论成功还是失败，都要检查是否继续下一个任务
    if (result.isLastBatch) {
      // 当前批次已完成，但需要再次请求确认是否真的完成
      taskProgress.processingMessage = '当前批次已完成，正在检查是否还有更多任务...'
      setTimeout(() => {
        handleContinueNextBatch()
      }, 1000)
    } else {
      // 继续下一批任务，无论当前任务成功还是失败
      if (result.success) {
        taskProgress.processingMessage = '当前商品处理成功，正在继续下一个...'
      } else {
        taskProgress.processingMessage = '当前商品处理失败，正在继续下一个...'
      }
      
      setTimeout(() => {
        handleContinueNextBatch()
      }, 2000)
    }

  } catch (error: any) {
    console.error('处理正常任务失败:', error)
    taskProgress.isProcessing = false
    
    // 创建失败的商品结果
    const productUploadResult: ProductUploadResult = {
      taskDetailId: taskResponse?.id || 0,
      success: false,
      message: error.message || '任务处理失败',
      productName: taskResponse?.goods_info?.goods_name || '未知商品',
      productImage: taskResponse?.thumb_url || '',
      price: taskResponse ? `${taskResponse.price_third} ${taskResponse.currentcy}` : '未知价格'
    }
    
    // 添加到处理历史
    taskProgress.currentProduct = productUploadResult
    taskProgress.processHistory.push({ ...productUploadResult })
    
    // 即使发生异常，也要检查是否继续下一个任务
    if (taskResponse) {
      const isLastBatch = checkIsLastBatch(taskResponse.task_count, taskResponse.task_num + 1)
      taskProgress.currentProgress = taskResponse.task_num + 1
      
      if (isLastBatch) {
        // 当前批次已完成，但需要再次请求确认是否真的完成
        taskProgress.processingMessage = '当前批次已完成，正在检查是否还有更多任务...'
        setTimeout(() => {
          handleContinueNextBatch()
        }, 1000)
      } else {
        // 继续下一批任务
        taskProgress.processingMessage = '当前商品处理异常，正在继续下一个...'
        setTimeout(() => {
          handleContinueNextBatch()
        }, 2000)
      }
    } else {
      // 如果没有taskResponse，说明是严重错误，停止任务
      taskProgress.hasError = true
      taskProgress.errorMessage = error.message || '任务处理失败'
    }
  }
}

// 继续下一批任务
const handleContinueNextBatch = async () => {
  if (!currentTask.value) return

  try {
    taskProgress.isProcessing = true
    taskProgress.processingMessage = '正在获取下一批任务数据...'
    
    // 重新调用开始任务API获取下一批数据
    const response: TaskStartResponse = await startTask(currentTask.value.id)
    
    if (response.task_over === 1) {
      // 所有任务已完成，调用任务完成更新
      try {
        await updateTaskComplete(currentTask.value.id)
        console.log('任务完成状态更新成功')
      } catch (error: any) {
        console.error('更新任务完成状态失败:', error)
      }
      
      taskProgress.isProcessing = false
      taskProgress.isCompleted = true
      taskProgress.processingMessage = '所有任务已完成！'
      setTimeout(() => {
        loadTaskList()
      }, 3000)
    } else if (response.task_exist === 1) {
      // 又遇到已存在的商品，立即显示并跳过
      const productResult: ProductUploadResult = {
        taskDetailId: response.id || 0,
        success: false,
        message: '商品已存在，跳过处理',
        productName: response.goods_name || '未知商品',
        productImage: response.thumb_url || '',
        price: `${response.price || 0} ${response.currentcy || ''}`
      }
      
      // 立即显示当前处理的商品
      taskProgress.currentProduct = productResult
      taskProgress.processHistory.push({ ...productResult })
      
      // 更新总进度数（如果服务器返回了更准确的数据）
      if (response.task_count && response.task_count > taskProgress.totalProgress) {
        taskProgress.totalProgress = response.task_count
      }
      
      // 使用服务器返回的task_num + 1作为当前进度
      if (response.task_num !== undefined) {
        taskProgress.currentProgress = response.task_num + 1
      } else {
        taskProgress.currentProgress = Math.min(
          taskProgress.currentProgress + 1, 
          taskProgress.totalProgress
        )
      }
      taskProgress.processingMessage = '商品已存在，正在继续处理下一个...'
      
      // 继续下一个
      setTimeout(() => {
        handleContinueNextBatch()
      }, 2000)
    } else if (response.goods_no_cat_relation === 1) {
      // 遇到分类未关联的商品
      taskProgress.isProcessing = false
      taskProgress.hasError = true
      taskProgress.errorMessage = '遇到分类未关联的商品，请先在分类管理中关联相应的N11分类。'
    } else if (response.task_over === 0 && response.task_exist === 0 && response.goods_no_cat_relation === 0) {
      // 继续处理下一批正常任务
      // 确保response包含必要的字段
      if (response.id && response.task_id) {
        await handleNormalTask(response as TaskResponse, currentTask.value.store_name)
      } else {
        taskProgress.isProcessing = false
        taskProgress.hasError = true
        taskProgress.errorMessage = '任务响应数据不完整，缺少必要的ID字段'
      }
    }
  } catch (error: any) {
    console.error('继续下一批任务失败:', error)
    taskProgress.isProcessing = false
    taskProgress.hasError = true
    taskProgress.errorMessage = '继续处理失败: ' + (error.message || '未知错误')
  }
}

// 处理进度对话框关闭
const handleProgressDialogClose = () => {
  progressDialogVisible.value = false
  // 重置进度数据
  Object.assign(taskProgress, {
    currentProgress: 0,
    totalProgress: 0,
    isProcessing: false,
    isCompleted: false,
    hasError: false,
    errorMessage: '',
    processingMessage: '正在处理商品...',
    currentProduct: null,
    processHistory: []
  })
  currentTask.value = null
}

// 处理进度对话框取消
const handleProgressDialogCancel = () => {
  // 停止当前任务处理
  taskProgress.isProcessing = false
  taskProgress.hasError = true
  taskProgress.errorMessage = '用户取消了任务'
  currentTask.value = null
}

// 当前视图状态
const currentView = ref<'list' | 'detail'>('list')
const currentTaskId = ref<number | null>(null)

// 处理查看详情按钮点击
const handleViewDetails = (row: Task) => {
  console.log('查看详情:', row)
  currentTaskId.value = row.id
  currentView.value = 'detail'
}

// 返回任务列表
const handleBackToList = () => {
  currentView.value = 'list'
  currentTaskId.value = null
}

// 格式化时间范围 (原有的，可能需要调整或移除)
const formatTimeRange = (row: Task) => {
  if (row.time_range === 'today') {
    return '今天'
  } else if (row.time_range === 'yesterday') {
    return '昨天'
  } else if (row.time_range === 'custom' && row.day_start && row.day_end) {
    return `${row.day_start} 至 ${row.day_end}`
  }
  return '未知'
}

// 处理日期范围变化
const handleDateRangeChange = (val: string[]) => {
  if (val && val.length === 2) {
    searchForm.day_start = val[0]
    searchForm.day_end = val[1]
  } else {
    searchForm.day_start = undefined
    searchForm.day_end = undefined
  }
}

// 加载任务列表
const loadTaskList = async () => {
  loading.value = true
  try {
    const params: TaskListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      task_over: searchForm.task_over,
      day_start: searchForm.day_start,
      day_end: searchForm.day_end
    }
    
    const response = await getTaskList(params)
    console.log('获取任务列表成功:', response)
    taskList.value = response.list as Task[]
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadTaskList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    task_over: undefined,
    day_start: undefined,
    day_end: undefined
  })
  dateRange.value = []
  pagination.currentPage = 1
  loadTaskList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadTaskList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadTaskList()
}

// 组件挂载时加载数据
onMounted(() => {
  loadTaskList()
})
</script>

<style scoped>
.task-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.status-item {
  width: 260px; /* 根据需要调整宽度 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>