<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\GoodsCategoryService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class GoodsCategoryController extends Controller    
{
    protected GoodsCategoryService $goodsCategoryService;

    public function __construct(GoodsCategoryService $goodsCategoryService)
    {
        $this->goodsCategoryService = $goodsCategoryService;
        parent::__construct();
    }

    public function catTemuWebpageMain(): JsonResponse
    {
        $result = $this->goodsCategoryService->catTemuWebpageMain();
        return $this->apiSuccess($result);
    }

    public function catTemuWebpageList(Request $request): JsonResponse
    {
        $params = $request->only(['page', 'pageSize','parent_id','name']);
        $result = $this->goodsCategoryService->catTemuWebpageList($params);
        return $this->apiSuccess($result);
    }

    public function catTemuDetail(Request $request): JsonResponse
    {
        $params = $request->only(['id']);
        $result = $this->goodsCategoryService->catTemuDetail($params);
        return $this->apiSuccess($result);
    }

    public function catTemuMain(): JsonResponse
    {
        $result = $this->goodsCategoryService->catTemuMain();
        return $this->apiSuccess($result);
    }

    public function catTemuList(Request $request): JsonResponse
    {
        $params = $request->only(['page', 'pageSize','parent_id','name','lazy_load']);
        $result = $this->goodsCategoryService->catTemuList($params);
        return $this->apiSuccess($result);
    }

    /**
     * 获取Temu分类列表（懒加载专用）
     */
    public function catTemuListLazy(Request $request): JsonResponse
    {
        $params = $request->only(['page', 'pageSize','parent_id','name','relation_status','id','third_platform_id']);
        $result = $this->goodsCategoryService->catTemuListLazy($params);
        return $this->apiSuccess($result);
    }

    /**
     * 获取分类的子分类数量
     */
    public function catTemuChildrenCount(Request $request): JsonResponse
    {
        $parentId = $request->input('parent_id', 0);
        $result = $this->goodsCategoryService->getTemuCategoryChildrenCount($parentId);
        return $this->apiSuccess(['count' => $result]);
    }

    /**
     * 批量获取多个分类的子分类数量
     */
    public function catTemuChildrenCounts(Request $request): JsonResponse
    {
        $parentIds = $request->input('parent_ids', []);
        if (!is_array($parentIds)) {
            return $this->apiError('parent_ids必须是数组');
        }
        $result = $this->goodsCategoryService->getTemuCategoryChildrenCounts($parentIds);
        return $this->apiSuccess(['counts' => $result]);
    }

    /**
     * 获取Temu分类列表（虚拟表格专用）- 扁平化数据
     */
    public function catTemuListFlat(Request $request): JsonResponse
    {
        $params = $request->only(['page', 'pageSize','parent_id','name','level']);
        $result = $this->goodsCategoryService->catTemuListFlat($params);
        return $this->apiSuccess($result);
    }

    public function catN11List(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $params = $request->only(['page', 'pageSize','parent_id','name']);
        $result = $this->goodsCategoryService->catN11List($userId, $params);
        return $this->apiSuccess($result);
    }

    public function catN11Detail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $is_admin = $user['is_admin'];
        $params = $request->only(['id']);
        $result = $this->goodsCategoryService->catN11Detail($userId, $is_admin, $params);
        return $this->apiSuccess($result);
    }

    public function catN11Update(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $is_admin = $user['is_admin'];
        $result = $this->goodsCategoryService->catN11Update($userId, $is_admin, request()->all());
        return $this->apiSuccess($result);
    }

    public function catRelationSet(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $is_admin = $user['is_admin'];
        $result = $this->goodsCategoryService->catRelationSet($userId,$is_admin,request()->all());
        return $this->apiSuccess($result);
    }

    public function catRelationList(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $result = $this->goodsCategoryService->catRelationList($userId,request()->all());
        return $this->apiSuccess($result);
    }

}        

