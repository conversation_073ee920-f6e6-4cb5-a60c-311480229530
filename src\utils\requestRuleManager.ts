interface ModifyHeadersParams {
  requestId: string;
  headers: Record<string, string>;
  urlPattern: string;
}

export const modifyRequestHeaders = async ({ requestId, headers, urlPattern }: ModifyHeadersParams) => {
  try {
    const ruleId = Math.floor(Number(requestId));

    // 移除已存在的规则
    await chrome.declarativeNetRequest.updateSessionRules({
      removeRuleIds: [ruleId]
    });

    // 将对象转换为请求头数组
    const requestHeaders = Object.entries(headers).map(([header, value]) => ({
      header,
      operation: chrome.declarativeNetRequest.HeaderOperation.SET,
      value
    }));

    // 添加新规则
    await chrome.declarativeNetRequest.updateSessionRules({
      addRules: [{
        id: ruleId,
        priority: 1,
        action: {
          type: chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,
          requestHeaders
        },
        condition: {
          urlFilter: urlPattern,
          resourceTypes: [chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST]
        }
      }]
    });

    return true;
  } catch (error) {
    console.error('修改请求头规则失败:', error);
    return false;
  }
};

export const removeRequestRule = async (requestId: string) => {
  try {
    const ruleId = Math.floor(Number(requestId));
    await chrome.declarativeNetRequest.updateSessionRules({
      removeRuleIds: [ruleId]
    });
    return true;
  } catch (error) {
    console.error('移除Cookie规则失败:', error);
    return false;
  }
};
