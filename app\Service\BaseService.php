<?php
declare(strict_types=1);

namespace App\Service;

use Illuminate\Http\Request;

class BaseService
{
    protected int $per_page = 20;
    protected int $page_no = 1;
    protected Request $request;

    public function __construct()
    {
        $this->request  = request();
        $this->per_page = (int)request()->get('per_page',config('sys.page.admin_per_page'));
        $this->page_no = (int)request()->get('page_no', 1);
    }

    public function dealOneArrayTimeShow(array $data):array
    {
        foreach($data as $key => $value){
            if($key=='ctime' || $key=='utime'){
                if(is_numeric($value) && (int)$value>0){
                    $data[$key] = date('Y-m-d H:i:s', $value);
                }else{
                    $data[$key] = '';
                }
            }
        }
        return $data;
    }

    public function dealArrayTimeShow(array $data): array
    {
        foreach($data as $key => $value){
            foreach($value as $k => $v){
                if($k=='ctime' || $k=='utime'){
                    if(is_numeric($v) && (int)$v>0){
                        $data[$key][$k] = date('Y-m-d H:i:s', $v);
                    }else{
                        $data[$key][$k] = '';
                    }
                }
            }
        }
        return $data;
    }

    public function getCommonAttr(): array
    {
        return [];
    }



}
