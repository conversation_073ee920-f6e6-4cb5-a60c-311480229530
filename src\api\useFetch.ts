import protobufRequest from '@/api/req_protobuf';
import { updateRequestCookieRule, clearRequestRules } from '@/utils/request-rules';

export async function useFetch(request: {
  funName: string;
  pramas: any;
  headers: any;
  method: string;
  url: string;
  options?: RequestInit;
  cookie?: string; // 添加可选的 cookie 参数
}, sender: any, callback = () => {}) {
  console.log("fetch request", request);
  const { funName, pramas, headers, method, url, options, cookie } = request;

  try {
    // 如果提供了 cookie，先设置请求规则
    /* if (cookie) {
      const domain = new URL(url).hostname;
      console.log("--------------------------------------------domain", domain);
      await updateRequestCookieRule({
        cookie,
        domain
      });
    } */

    // 构建fetch请求配置
    const fetchOptions: RequestInit = {
      method,
      headers,
      ...options,
    };

    if (method.toLowerCase() === 'post' && pramas) {
      fetchOptions.body = JSON.stringify(pramas);
    }

    // 执行fetch请求
    const response = await fetch(url, fetchOptions);
    let result;
    const contentType = response.headers.get('content-type');

    if (contentType?.includes('application/json')) {
      result = await response.json();
    } else if (contentType?.includes('application/x-protobuf')) {
      const buffer = await response.arrayBuffer();
      result = await protobufRequest(url, headers, buffer);
    } else {
      result = await response.text();
    }

    // 如果之前设置了 cookie，清除请求规则
    if (cookie) {
      //await clearRequestRules();
    }

    callback([{
      status: response.status,
      data: result,
      headers: Object.fromEntries(response.headers)
    }]);
  } catch (error) {
    // 确保在出错时也清除请求规则
    if (cookie) {
      //await clearRequestRules();
    }
    console.error('Fetch error:', error);
    callback(null);
  }
}
