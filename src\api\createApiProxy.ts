import putApi from './putApi';

interface ApiProxy {
  [key: string]: (body: any, callback: (arg0: any) => any, headers?: {}, config?: {}) => void;
}

export function createApiProxy(): ApiProxy {
  return new Proxy({} as ApiProxy, {
    get(target, property: string) {
      if (!(property in target)) {
        console.log(`Creating API method: ${property}`);
        putApi(target, property, "", {});
      }
      return target[property];
    }
  });
}
