import config from '@/config/index';
import * as pageMsg from '@/utils/page_msg';
import * as utils from '@/utils/index';
import * as utils_new from '@/utils/new/utils';

export const cdpClick = (type:number,tabId:number,tab_cache?:any,date_start?:any,date_end?:any,args?:any)=>{
  let res = {};
  let code = 0;
  let msg  = '';
  let opt  = '';
    // 建立到CDP的连接
    console.log("建立到 CDP 的连接，当前TABID为："+tabId);
  //增加判断 是否已经建立连接 如果已建立连接 不需要再次建立 没建立时 先建立连接
  let index = tab_cache[tabId + ""];
  console.log(index);
  if (index) {
    console.log("当前tabId已建立CDP连接,执行的操作类型为"+type);

    //下面的代码需要注意 type 相关的case值配置 需要配置2次
    //1是异常的时候  2 正常通知情形

    //先检测是否已经建立连接 检测的方式通过执行一个简单的命令来检测 如果发生异常 返回异常新  debugger is not attached to the tab  则表示未建立连接
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
      if (chrome.runtime.lastError) {
        let lastError = chrome.runtime.lastError.message;
        lastError = lastError.toLowerCase();
        if (lastError.includes('debugger is not attached to the tab')) {
          //获取当前tab的url
          chrome.tabs.query({ active: true, currentWindow: true }, function(tabs:any) {
              let url = tabs[0].url;
              chrome.tabs.remove(tabs[0].id);
              setTimeout(() => {
                chrome.tabs.create({url:url},()=>{});
              }, 1500);
              });
        }else{
          type = parseInt(type.toString());
          if(type<10000000){
            switch(type){
              case 3011:
              case 3012:
              case 3013:
              case 3014:
              case 3015:
              case 3016:
              case 6011:
              case 6012:
              case 6013:
              case 6014:
              case 6015:
              case 6016:
                operationDateSelectDomPrivatePop(type,tabId,date_start,date_end);
                return true;
                break;
              case 4:
              case 4011:
              case 4012:
              case 7011:
              case 7012:
                operationPrivateListDom(type,tabId,args);
                return true;
                break;
              case 7013:
                //已用作巨好房当前会话用户列表点击
                operationJhfUserSessionUserListDom(type,tabId,args);
                return true;

              case 7014:
                //巨好房当前会话用户滚动
                operationJhfUserSessionScrollDownOptDom(type,tabId,args);
                return true;

              case 5:
              case 5011:
              case 5012:
              case 5013:
              case 5014:
              case 5015:
              case 5016:
              case 5017:
              case 5018:
              operationDomVideoComment(type,tabId);
                return true;
                break;


              default:
                operationDom(type,tabId);
                return true;
            }
          }else{
            console.log("获取DOM数量");
            getDomNumber(type,tabId);
          }

        }
      }else{
        type = parseInt(type.toString());
        if(type<10000000){
            switch(type){
              case 3011:
              case 3012:
              case 3013:
              case 3014:
              case 3015:
              case 3016:
              case 6011:
              case 6012:
              case 6013:
              case 6014:
              case 6015:
              case 6016:
              operationDateSelectDomPrivatePop(type,tabId,date_start,date_end);
              return true;
            case 4:
            case 4011:
            case 4012:
            case 7011:
            case 7012:
              operationPrivateListDom(type,tabId,args);
              return true;
              break;

            case 7013:
              //已用作巨好房当前会话用户列表点击
              operationJhfUserSessionUserListDom(type,tabId,args);
              return true;
            case 7014:
              //巨好房当前会话用户滚动
              operationJhfUserSessionScrollDownOptDom(type,tabId,args);
              return true;

            case 5:
            case 5011:
            case 5012:
            case 5013:
            case 5014:
            case 5015:
            case 5016:
            case 5017:
            case 5018:
            operationDomVideoComment(type,tabId);
              return true;
              break;


            default:
              operationDom(type,tabId);
              return true;
          }
        }else{
          console.log("获取DOM数量");
          getDomNumber(type,tabId);
        }



      }
    });

  }else{
   /*  code = 1;
    msg  = "正在刷新当前页面";
    opt  = "refresh";
    console.log("当前tabId未建立CDP连接");
    res = {
      code: code,
      msg: msg,
      opt: opt
    };
    pageMsg.sendMessageToContentScript(res,()=>{}) */

    /* chrome.debugger.attach({tabId: tabId}, '1.0', function() {
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs:any) {
            let url = tabs[0].url;
            let params = url.split('?')[1].split('&');
            let room_id = '';
            for (let i = 0; i < params.length; i++) {
                let param = params[i].split('=');
                param[0] = param[0].toLowerCase();
                if (param[0] == 'room_id' || param[0] == 'roomid') {
                    room_id = param[1];
                }
            }
            tab_cache[tabId + ""] = {};
            tab_cache[tabId + ""]["room_id"] = room_id;
            tab_cache[tabId + ""]["fetch_urls"] = {};
            tab_cache[tabId + ""]["webSocket_urls"] = {};
        });
        operationDom(1,tabId);
    }); */
  }
}

export const checkPrivateLetterMode = (tabId:number,args:any)=>{
  let res = {};
  let code = 0; //2 警告通知类型 3 成功通知类型
  let msg  = '';
  let opt  = '';//showNotice
  let next = '';
  let data = '';
  console.log("检测当前模式 用于直接进入的就是私信模式再次检测");

  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getDocument', {}, function(root:any) {
      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
          nodeId: root.root.nodeId,
          selector: '[data-log-name="查看客服模式下会话"]'
      }, function(result:any) {
        console.log(result.nodeIds);
        if (result.nodeIds.length > 0) {
          //let groupNodeIds = result.nodeIds;
          //let lastGroupNodeId = groupNodeIds[groupNodeIds.length-1];
          console.log("查到了 查看客服模式下会话 ");
        }
      });
    });
  });

}



export const operationJhfUserSessionUserListDom = (type:number,tabId:number,args:any)=>{
  console.log("操作巨好房当前会话用户列表点击");
  let click_list: string[] = [];//存储已点击的text
  let click_list_count = 0;

  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getDocument', {}, function(root:any) {
      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
        nodeId: root.root.nodeId,
        selector: '[class^="conversationItem-"]'
      }, function(result:any) {
        if (result.nodeIds.length > 0) {
          let index = 0;
          console.log(result.nodeIds,'---------result.nodeIds------------');
          function clickNext() {
            console.log(index,'---------当前操作的index------------');
            if (index >= result.nodeIds.length) {
              console.log("所有元素已点击完毕,当前点击的元素数量为"+click_list_count);
              if(click_list_count == 0){
                //没有新元素需要点击 已全部获取完毕
                console.log("--------------没有新元素需要点击 已全部获取完毕----------------");
                return '';
              }
              let res = {
                code: 3,
                msg: `当前会话成员已获取完毕,您可以手动滑动当前会话区域点击获取其它成员私信消息`,
                opt: 'showFinishNotice',
                type:type,
                tabId:tabId,
                next:'',
                data:click_list
              };
              console.log(res);
              pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});

              return;
            }

            let nodeId = result.nodeIds[index];


            chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelector', {
              nodeId: nodeId,
              selector: '[class^="conversationName-"]'
            }, function(subResult:any) {
              if (subResult.nodeId) {
                chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: subResult.nodeId}, function(htmlResult:any) {
                  console.log(`第 ${index + 1} 个元素的文字内容: ${htmlResult.outerHTML}`);
                  let text = htmlResult.outerHTML.replace(/<[^>]*>/g, '');
                  console.log(text);

                  /* chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'static/img/icon_128.png',
                    title: '提示',
                    message: `正在获取${text}成员会话信息`,
                    priority: 2
                  }, (notificationId:any) => {
                    setTimeout(() => {
                      chrome.notifications.clear(notificationId);
                    }, 1500); // 2秒后关闭通知
                  }); */
                  if(click_list.includes(text)){
                    console.log("已经点击过该元素");
                    index++;
                    setTimeout(clickNext, 2000); // 间隔2秒点击下一个
                    return;
                  }
                    let res = {
                      code: 3,
                      msg: `正在获取【${text}】成员会话信息`,
                      opt: 'showNoticeSession',
                      type:type,
                      tabId:tabId,
                      next:'',
                      data:{}
                    };
                    console.log(res);
                    pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});

                    console.log(nodeId,'---------当前操作的nodeId------------');
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                      chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                        objectId: object.object.objectId,
                        functionDeclaration: 'function() { this.click(); }'
                      }, function() {
                        console.log(`已点击第 ${index + 1} 个元素`);
                        click_list.push(text);
                        click_list_count++;
                        index++;
                        console.log(index,'---------下一个index的值------------');
                        setTimeout(clickNext, 2000); // 间隔2秒点击下一个
                      });
                    });


                });
              } else {
                console.log(`第 ${index + 1} 个元素中没有找到 class 以 conversationName- 开头的DIV`);
              }
            });







          }

          clickNext();
        } else {
          console.log("没有找到 class 以 conversationItem- 开头的元素");
        }
      });
    });
  });
}












export const operationPrivateListDom = (type:number,tabId:number,args:any)=>{
  let res = {};
  let code = 0; //2 警告通知类型 3 成功通知类型
  let msg  = '';
  let opt  = '';//showNotice
  let next = '';
  let data = '';
  console.log("操作私信分页列表");

  let type_config = config.domRule[type as keyof typeof config.domRule];
  let xpath = type_config.xpath;

  let click_list = [];

  console.log(type_config);
  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getDocument', {}, function(root:any) {

      if(type_config.search_type=='class'){
        console.log("使用class查找,配置的class==" + type_config.css_path);
        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
            nodeId: root.root.nodeId,
            selector: type_config.css_path
        }, function(result:any) {

          console.log(result.nodeIds);

          if (result.nodeIds.length > 0) {
            if(type_config.attr!='input'){
              let groupNodeIds = result.nodeIds;
              let secondGroupNodeId = groupNodeIds[groupNodeIds.length-1]; // 第二组元素的ID
              switch(type){
                case 4:
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                      nodeId: secondGroupNodeId,
                      selector: '.leads-pager-item'
                  }, function(result:any) {
                      let itemNodeIds = result.nodeIds;
                      console.log(itemNodeIds);
                      let secondLastItemId = itemNodeIds[itemNodeIds.length - 2]; // 倒数第二个元素的ID
                      console.log(secondLastItemId);
                      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: secondLastItemId}, function(result:any) {
                          console.log(result);
                          let html = result.outerHTML;
                          let text = html.replace(/<[^>]*>/g, '');
                          console.log("总页数"+text);

                          code = 3;
                          res = {
                            code: code,
                            msg: '总共'+text+'页数据',
                            opt: 'showNotice',
                            type:type,
                            tabId:tabId,
                            next:next,
                            data:{total_page:parseInt(text)}
                          };
                          console.log(res);
                          pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});

                      });
                  });

                  return true;
                case 4011:
                  //列表数据
                  click_list = args.click_list;
                  //需要点击的列表数据
                  /* console.log(click_list);
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                      nodeId: secondGroupNodeId,
                      selector: `tr:nth-child(${click_list[0]}) td:nth-child(5) span:first-child a:first-child`
                  }, function(result:any) {
                    //点击获取到的元素
                    let click_nodeId = result.nodeIds[0];
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: click_nodeId}, function(result:any) {
                      let html = result.outerHTML;
                      let text = html.replace(/<[^>]*>/g, '');
                      console.log("当前点击的元素"+text);
                      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
                        chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                          objectId: object.object.objectId,
                          functionDeclaration: 'function() { this.click(); }'
                        }, function() {
                          // 在这里返回响应
                          console.log("已点击元素");
                        });
                      });
                    });
                  }); */


                  function clickElement(index:number) {
                    if (index >= click_list.length) {
                      code = 3;
                      res = {
                        code: code,
                        msg: "当前页面数据已成功获取",
                        opt: "currentPageDataOver",
                        type:type,
                        tabId:tabId,
                        next:"",
                        data:[]
                      };
                      pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                      return;
                    }

                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                      nodeId: secondGroupNodeId,
                      selector: `tr:nth-child(${click_list[index]}) td:nth-child(5) span:first-child a:first-child`
                    }, function(result:any) {
                      //点击获取到的元素
                      let click_nodeId = result.nodeIds[0];
                      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: click_nodeId}, function(result:any) {
                        let html = result.outerHTML;
                        let text = html.replace(/<[^>]*>/g, '');
                        console.log("当前点击的元素"+text);
                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                            objectId: object.object.objectId,
                            functionDeclaration: 'function() { this.click(); }'
                          }, function() {
                            // 在这里返回响应
                            console.log("已点击元素");

                            //selector: '.text-primary-5:not(.cursor-pointer)'
                            function clickElements() {
                              chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                nodeId: root.root.nodeId,
                                selector: 'div.leads-loading.leads-loading-block > div.py-4:first-child > span.text-primary-5:first-child'
                              }, function(result:any) {
                                if (result && result.nodeIds.length > 0) {

                                  /* chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                    nodeId: secondGroupNodeId,
                                    selector: '.overflow-y-scroll'
                                  }, function(result:any) {
                                    if (result.nodeIds.length > 0) {
                                      let nodeId = result.nodeIds[result.nodeIds.length-1];
                                      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getAttributes', {
                                        nodeId: nodeId
                                      }, function(attributes:any) {
                                        let updatedAttributes = attributes.attributes.filter((attr:any) => attr.name !== 'class');
                                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.setAttributesAsText', {
                                          nodeId: nodeId,
                                          text: updatedAttributes.map((attr:any) => `${attr.name}="${attr.value}"`).join(' ')
                                        }, function() {
                                          console.log('Removed overflow-y-scroll class');
                                        });
                                      });
                                    }
                                  });  */

                                  /* let click_nodeIds = result.nodeIds;

                                  let click_nodeId = click_nodeIds[click_nodeIds.length-1];
                                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
                                    chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                      objectId: object.object.objectId,
                                      functionDeclaration: 'function() { this.click(); }'
                                    }, function() {
                                      setTimeout(() => {
                                        clickElements();
                                      }, 2500);
                                    });
                                  }); */


                                  result.nodeIds.forEach((nodeId: any) => {
                                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                                      let html = result.outerHTML;
                                      console.log(html);
                                      let text = html.replace(/<[^>]*>/g, '');
                                      console.log("当前元素TEXT==="+text);
                                      if (text == '加载更多') {
                                        console.log("找到对应的元素页码==="+text);
                                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                            objectId: object.object.objectId,
                                            functionDeclaration: 'function() { this.click(); }'
                                          }, function() {
                                            setTimeout(() => {
                                              clickElements();
                                            }, 2500);
                                          });
                                        });
                                      }
                                    });
                                  });








                                } else {

                                  setTimeout(function() {
                                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                      nodeId: root.root.nodeId,
                                      selector: '.leads-icon-close-drawer'
                                    }, function(result:any) {
                                      console.log("关闭按钮");
                                      console.log(result.nodeIds);
                                      if (result.nodeIds.length > 0) {
                                        let click_nodeId = result.nodeIds[result.nodeIds.length - 1];
                                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
                                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                            objectId: object.object.objectId,
                                            functionDeclaration: 'function() { this.click(); }'
                                          }, function() {
                                            // Click on the next element
                                            console.log("执行下一个");
                                            setTimeout(() => {
                                              clickElement(index + 1);
                                            },1500);
                                          });
                                        });
                                      }
                                    });
                                  }, 5000);


                                }
                              });
                            }
                            setTimeout(() => {
                              clickElements();
                            }, 2500);



                          });
                        });
                      });
                    });
                  }

                  clickElement(0);

                  return true;
                case 4012:
                case 7012:
                  let current_page_span_selector = `span:not(.leads-icon)`;
                  if(type==4012){
                    console.log("处理抖音商家管理私信列表点击第"+args.page+"页");
                  }else{
                    console.log("处理巨好房私信模式列表点击第"+args.page+"页");
                    current_page_span_selector = `span:not(.fang-leads-icon)`;
                  }
                  if (args.page) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                      nodeId: secondGroupNodeId,
                      selector: current_page_span_selector
                    }, function(result:any) {
                      console.log(result);
                      if (result && result.nodeIds.length > 0) {
                        result.nodeIds.forEach((nodeId: any) => {
                          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                            let html = result.outerHTML;
                            let text = html.replace(/<[^>]*>/g, '');
                            console.log("当前元素TEXT==="+text);
                            if (text == args.page) {
                              console.log("找到对应的元素页码==="+text);
                              chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                                chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                  objectId: object.object.objectId,
                                  functionDeclaration: 'function() { this.click(); }'
                                }, function() {
                                  console.log("Clicked on element with innerText: " + args.page);
                                });
                              });
                            }
                          });
                        });
                      }
                    });
                  }
                  return true;

                case 7011:
                  console.log("处理点击巨好房私信模式列表数据");
                  click_list = args.click_list;

                  function clickElementJhf(index:number) {
                    //console.log('当前页共有元素个数====',click_list.length);
                    //console.log('当前点击的是第几个元素====',(index+1));
                    //此处判断不需要index+1 因为index是从0开始的 即便点击到了最后一个 也会再次执行一次 再次执行一次后本处会判断是否结束 结束后会执行下一个
                    if (index >= click_list.length) {
                      code = 3;
                      res = {
                        code: code,
                        msg: "当前页面数据已成功获取",
                        opt: "currentPageDataOver",
                        type:type,
                        tabId:tabId,
                        next:"",
                        data:[]
                      };
                      pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                      return;
                    }
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                      nodeId: secondGroupNodeId,
                      selector: `tr:nth-child(${click_list[index]}) td:nth-child(7) button`
                    }, function(result:any) {
                      //点击获取到的元素
                      let click_nodeId = result.nodeIds[0];
                      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: click_nodeId}, function(result:any) {
                        let html = result.outerHTML;
                        let text = html.replace(/<[^>]*>/g, '');
                        console.log("当前点击的元素"+text);
                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                            objectId: object.object.objectId,
                            functionDeclaration: 'function() { this.click(); }'
                          }, function() {
                            // 在这里返回响应
                            console.log("已点击元素");

                            //selector: '.text-primary-5:not(.cursor-pointer)'
                            async function clickElementsJhf() {

                               //获取最新一条私信沟通时间
                              let load_more_letter = true;
                              let node_latest = 'div.fang-leads-loading.fang-leads-loading-block > div.pb-4 > div.contents > div.mt-2.mb-4 >div:first-child >div.flex-1.flex.flex-col >p.invisible:first-child';

                              let async_jhf_history_time_result = await utils_new.getLocalStorageData([
                                'async_jhf_history_time',
                                'async_date_start',
                                'async_date_end',
                                'history_end_date',
                                'history_end_date_time',
                                'async_jhf_select_date',
                                'up_type'
                              ]);

                              chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                nodeId: root.root.nodeId,
                                selector: 'div.fang-leads-loading.fang-leads-loading-block > div.text-center.py-4.text-xs > span.text-primary-5:first-child'
                              }, function(result:any) {
                                if (result && result.nodeIds.length > 0) {
                                  result.nodeIds.forEach((nodeId: any) => {
                                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                                      let html = result.outerHTML;
                                      console.log(html);
                                      let text_more = html.replace(/<[^>]*>/g, '');
                                      console.log("当前元素TEXT==="+text);
                                      if(parseInt(async_jhf_history_time_result.up_type)==2){
                                        //增量更新操作时  需要判断私信沟通的时间
                                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                          nodeId: root.root.nodeId,
                                          selector: node_latest
                                        }, function(result_latest:any) {
                                          if (result_latest && result_latest.nodeIds.length > 0) {
                                            let nodeId_latest = result_latest.nodeIds[0];
                                            chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {
                                              nodeId: nodeId_latest
                                            }, function(res:any) {
                                              let html = res.outerHTML;
                                              let text = html.replace(/<[^>]*>/g, '');
                                              if(utils_new.isValidDate(text)){
                                                console.log("最新一条私信沟通时间==="+text);
                                                let timestamp = utils_new.convertToTimestamp(text);
                                                console.log("最新一条私信沟通时间==="+timestamp);
                                                console.log("系统设置的沟通结束时间戳==="+async_jhf_history_time_result.history_end_date_time);
                                                if(timestamp<async_jhf_history_time_result.history_end_date_time){
                                                    load_more_letter = false;
                                                    console.log("最新一条私信沟通时间小于系统设置的沟通结束时间戳==="+timestamp);
                                                }

                                                if(load_more_letter){
                                                  console.log("加载更多私信沟通数据");
                                                  if (text_more == '加载更多') {
                                                    console.log("找到加载更多元素==="+text_more);
                                                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                                                      chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                                        objectId: object.object.objectId,
                                                        functionDeclaration: 'function() { this.click(); }'
                                                      }, function() {

                                                        //巨好房官方存在BUG  点击 加载更多后 如果没有数据  加载更多 并不更换为 没有更多信息
                                                        //而且一直存在 加载更多 导致会多次点击 需要判断
                                                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                                          nodeId: root.root.nodeId,
                                                          selector: '.pb-4 .contents:first-child'
                                                        }, function(result:any) {
                                                          if (result && result.nodeIds.length > 0) {
                                                            let nodeId = result.nodeIds[0];
                                                            chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {
                                                              nodeId: nodeId
                                                            }, function(res:any) {
                                                              let html = res.outerHTML;
                                                              let text = html.replace(/<[^>]*>/g, '').trim();
                                                              console.log("第一个.contents的内容: " + text);
                                                              if (text === "你们已成为朋友") {
                                                                console.log("检测到'你们已成为朋友'，直接点击下一个");
                                                                setTimeout(() => {
                                                                  clickElementJhf(index + 1);
                                                                }, 1500);
                                                                return;
                                                              }else{
                                                                console.log("未检测到'你们已成为朋友'，继续加载更多");
                                                                setTimeout(() => {
                                                                  clickElementsJhf();
                                                                }, 2500);
                                                              }
                                                            });
                                                          }
                                                        });


                                                        /* setTimeout(() => {
                                                          clickElementsJhf();
                                                        }, 2500); */





                                                      });
                                                    });
                                                  }
                                                }else{
                                                  console.log("增量更新操作不需要加载更多私信沟通数据-----执行下一个-----");
                                                  setTimeout(() => {
                                                      //先注释掉 调试用
                                                      clickElementJhf(index + 1);
                                                    },1500);
                                                  }
                                              }else{
                                                console.log("--当前没有有效的私信沟通记录-----执行下一个-----");
                                                setTimeout(() => {
                                                  //先注释掉 调试用
                                                  clickElementJhf(index + 1);
                                                },1500);
                                              }
                                            });
                                          }
                                        });

                                      }else{
                                        //更新全部私信
                                        if (text_more == '加载更多') {
                                          console.log("找到对应的元素==="+text_more);
                                          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                                            chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                              objectId: object.object.objectId,
                                              functionDeclaration: 'function() { this.click(); }'
                                            }, function() {

                                                //巨好房官方存在BUG  点击 加载更多后 如果没有数据  加载更多 并不更换为 没有更多信息
                                                //而且一直存在 加载更多 导致会多次点击 需要判断
                                                chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                                  nodeId: root.root.nodeId,
                                                  selector: '.pb-4 .contents:first-child'
                                                }, function(result:any) {
                                                  if (result && result.nodeIds.length > 0) {
                                                    let nodeId = result.nodeIds[0];
                                                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {
                                                      nodeId: nodeId
                                                    }, function(res:any) {
                                                      let html = res.outerHTML;
                                                      let text = html.replace(/<[^>]*>/g, '').trim();
                                                      console.log("第一个.contents的内容: " + text);
                                                      if (text === "你们已成为朋友") {
                                                        console.log("检测到'你们已成为朋友'，直接点击下一个");
                                                        setTimeout(() => {
                                                          clickElementJhf(index + 1);
                                                        }, 1500);
                                                        return;
                                                      }else{
                                                        console.log("未检测到'你们已成为朋友'，继续加载更多");
                                                        setTimeout(() => {
                                                          clickElementsJhf();
                                                        }, 2500);
                                                      }
                                                    });
                                                  }
                                                });


                                              /* setTimeout(() => {
                                                clickElementsJhf();
                                              }, 2500); */

                                            });
                                          });
                                        }

                                      }


                                    });
                                  });

                                } else {
                                  setTimeout(function() {
                                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                      nodeId: root.root.nodeId,
                                      selector: '.fang-leads-icon-close-drawer'
                                    }, function(result:any) {
                                      console.log("关闭按钮");
                                      console.log(result.nodeIds);
                                      if (result.nodeIds.length > 0) {
                                        let click_nodeId = result.nodeIds[result.nodeIds.length - 1];
                                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
                                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                            objectId: object.object.objectId,
                                            functionDeclaration: 'function() { this.click(); }'
                                          }, function() {
                                            // Click on the next element
                                            console.log("执行下一个");
                                            setTimeout(() => {
                                              clickElementJhf(index + 1);
                                            },1500);
                                          });
                                        });
                                      }
                                    });
                                  }, 5000);


                                }
                              });
                            }



                            //点击切换全部聊天 span
                            console.log("巨好房：点击切换全部聊天 span");
                            chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                              nodeId: root.root.nodeId,
                              selector: '.i-icon.i-icon-switch + span'
                            }, function(result:any) {
                              console.log(result);
                              if (result.nodeIds.length > 0) {
                                let switch_all_btn = result.nodeIds[0];
                                chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: switch_all_btn}, function(object:any) {
                                  chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                    objectId: object.object.objectId,
                                    functionDeclaration: 'function() { this.click(); }'
                                  });
                                });
                                console.log("巨好房：已点击切换全部聊天 span");
                                  setTimeout(() => {
                                    clickElementsJhf();
                                  }, 2500);
                              }
                            });

                          });
                        });
                      });
                    });
                  }




                  clickElementJhf(0);


                  return true;

              }
            }

            code = 3;
            res = {
              code: code,
              msg: msg,
              opt: opt,
              type:type,
              tabId:tabId,
              next:next,
              data:data
            };
            pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});

          }else{
            code = 2;//警告通知类型
            msg  = "没有找到"+type_config.name+"元素";
            opt  = "showNotice";
            res = {
              code: code,
              msg: msg,
              opt: opt,
              type:type
            };
            //pageMsg.sendMessageToContentScript(res,()=>{});
            pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
            console.log("没有找到"+type_config.name+"元素");
          }

        });
      }

    });
  })

}

// 弹窗私信模式 操作DOM 日期选择框 还有一种不是弹窗私信模式的日期选择框 type 决定操作哪个DOM
export const operationDateSelectDomPrivatePop = (type:number,tabId:number,date_start:string,date_end:string)=>{
  let res = {};
  let code = 0; //2 警告通知类型 3 成功通知类型
  let msg  = '';
  let opt  = '';//showNotice
  let next = '';

  let is_click = false;

  console.log("操作日期选择框");
  let date_start_arr = date_start.split('-');
  let date_end_arr = date_end.split('-');
  let date_start_year = date_start_arr[0];
  let date_start_month = parseInt(date_start_arr[1]);
  let date_start_day = parseInt(date_start_arr[2]);
  let date_end_year = date_end_arr[0];
  let date_end_month = parseInt(date_end_arr[1]);
  let date_end_day = parseInt(date_end_arr[2]);
  if(date_start_year!=date_end_year || date_start_month!=date_end_month){
    code = 2;//警告通知类型
    msg  = "只支持选择同年同月的日期";
    opt  = "showNotice";
    res = {
      code: code,
      msg: msg,
      opt: opt,
      type:type,
      next:'none',
    };
    //pageMsg.sendMessageToContentScript(res,()=>{});
    pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
    return false;
  }
  let type_config = config.domRule[type as keyof typeof config.domRule];
  let xpath = type_config.xpath;
  console.log(type_config);
  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getDocument', {}, function(root:any) {
      if(type_config.search_type=='xpath'){
        // 使用XPath查询元素
        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.performSearch', {
            query: xpath
        }, function(result:any) {
            // 如果找到了元素，模拟点击事件
            if (result.searchId && result.resultCount > 0) {
                chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getSearchResults', {
                    searchId: result.searchId,
                    fromIndex: 0,
                    toIndex: result.resultCount
                }, function(nodes:any) {
                    if (nodes.nodeIds.length > 0) {
                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {
                            nodeId: nodes.nodeIds[0]
                          }, function(result:any) {
                            //当前只考虑此种方式
                            if(type_config.attr!='input'){
                              // 使用正则表达式提取文本内容
                              var text = result.outerHTML.replace(/<[^>]*>/g, '');
                              console.log('Element text:', text);
                              //if(text.indexOf(type_config.name)!=-1){
                              //type_config.disable_css;
                              if(type_config.disable_css){
                                //判断该元素的class属性 是否包含 leads-btn-disabled
                                if(result.outerHTML.indexOf(type_config.disable_css)!=-1){
                                  code = 2;//警告通知类型
                                  msg  = "不支持超过13天的直播间评论\r\n请选择另一场直播间数据";
                                  opt  = "showNotice";
                                  res = {
                                    code: code,
                                    msg: msg,
                                    opt: opt,
                                    type:type,
                                    next:'getLivingRoomList',
                                  };
                                  //pageMsg.sendMessageToContentScript(res,()=>{});
                                  pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                                  return true;
                                }
                              }


                                chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {
                                  nodeId: nodes.nodeIds[0]
                                }, function(result:any) {
                                    if(type != 3013){
                                      chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                        objectId: result.object.objectId,
                                        functionDeclaration: 'function() { this.click(); }'
                                      });
                                    }
                                    if(type == 3013){
                                      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                        nodeId: nodes.nodeIds[0],
                                        selector: 'span'
                                      }, function(result:any) {
                                        if (result.nodeIds.length > 0) {
                                          for (let nodeId of result.nodeIds) {
                                            chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {
                                              nodeId: nodeId
                                            }, function(result:any) {
                                              let html = result.outerHTML;
                                              console.log(html);
                                              let text = html.replace(/<[^>]*>/g, '');
                                              console.log(text);
                                              if (text === date_start_year) {
                                                chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {
                                                  nodeId: nodeId
                                                }, function(result:any) {
                                                  chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                                    objectId: result.object.objectId,
                                                    functionDeclaration: 'function() { this.click(); }'
                                                  });
                                                });
                                              }
                                            });
                                          }
                                        }
                                      })
                                    }

                                    code = 3;
                                    msg  = "";
                                    opt  = "";
                                    if(type==3011){
                                      console.log("已点击日期选择框");
                                      next = "pageDateSelectDomPrivatePopYearOpt";
                                    }
                                    if(type==3012){
                                      console.log("已点击Year");
                                      next = "pageDateSelectDomPrivatePopSearchYearOpt";
                                    }

                                    if(type==3013){
                                      console.log("已找到对应的年份元素"+date_start_year);
                                      return '';
                                    }

                                    res = {
                                      code: code,
                                      msg: msg,
                                      opt: opt,
                                      type:type,
                                      tabId:tabId,
                                      next:next
                                    };
                                    pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                                    return true;

                                    code = 3;//成功通知类型
                                    msg  = "";
                                    opt  = "showNotice";
                                    if(type==1){
                                      //点击后请求接口相关的 不返回弹窗通知 返回none 接口获取数据成功后由接口发送通知
                                      opt  = "none";
                                    }
                                    if(type==102){
                                      next = 'openCommentList';//打开直播间评论
                                    }

                                    res = {
                                      code: code,
                                      msg: msg,
                                      opt: opt,
                                      type:type,
                                      next:next
                                    };
                                    //pageMsg.sendMessageToContentScript(res,()=>{});
                                    pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                                });

                            }
                        });

                    }else{
                      code = 2;//警告通知类型
                      msg  = "没有找到"+type_config.name+"元素";
                      opt  = "showNotice";
                      res = {
                        code: code,
                        msg: msg,
                        opt: opt,
                        type:type
                      };
                      //pageMsg.sendMessageToContentScript(res,()=>{});
                      pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                      console.log("没有找到"+type_config.name+"元素");
                    }
                });
            }else{
              code = 2;//警告通知类型
              msg  = "没有找到"+type_config.name+"元素";
              opt  = "showNotice";
              res = {
                code: code,
                msg: msg,
                opt: opt,
                type:type
              };
              //pageMsg.sendMessageToContentScript(res,()=>{});
              pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
              console.log("没有找到"+type_config.name+"元素");
            }

        });
      }

      if(type_config.search_type=='class'){
        console.log("使用class查找,配置的class==" + type_config.css_path);
        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
            nodeId: root.root.nodeId,
            selector: type_config.css_path
        }, function(result:any) {
          result.nodeIds.forEach(function(nodeId:any) {
            // 获取节点的外部 HTML
            chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
              //console.log("获取节点外部的HTML");
              //console.log(result.outerHTML);
            });
          });
          if (result.nodeIds.length > 0) {

            type_config.class_index = result.nodeIds.length-1;
            console.log("共"+result.nodeIds.length+"个节点");
            console.log("强制更改class_index为"+(result.nodeIds.length-1));

            if(type_config.attr!='input'){
              switch(type){
                case 3011:
                case 6011:
                   // 获取第一个节点的对象引用 注意 需要node索引是 1
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: result.nodeIds[type_config.class_index]}, function(object:any) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                      objectId: object.object.objectId,
                      functionDeclaration: 'function() { this.click(); }'
                    });
                  });
                  next = "pageDateSelectDomPrivatePopYearOpt";
                  break;
                case 3012:
                case 6012:
                  type_config.class_index = 0;//需要固定是0
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: result.nodeIds[type_config.class_index]}, function(object:any) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                      objectId: object.object.objectId,
                      functionDeclaration: 'function() { this.click(); }'
                    });
                  });
                  next = "pageDateSelectDomPrivatePopSearchYearOpt";
                  break;
                case 3013:
                case 6013:
                  result.nodeIds.forEach(function(nodeId:any) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                      let text = result.outerHTML.replace(/<[^>]*>/g, '');
                      if(text == date_start_year){
                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                            objectId: object.object.objectId,
                            functionDeclaration: 'function() { this.click(); }'
                          });
                        });
                      }
                    });
                  });
                  next = "pageDateSelectDomPrivatePopSearchMonthOpt";
                  break;
                case 3014:
                case 6014:
                  result.nodeIds.forEach(function(nodeId:any) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                      let text = result.outerHTML.replace(/<[^>]*>/g, '');
                      text = utils.getMonthByChinese(text);
                      if(text == date_start_month){
                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                            objectId: object.object.objectId,
                            functionDeclaration: 'function() { this.click(); }'
                          });
                        });
                      }
                    });
                  });
                  next = "pageDateSelectDomPrivatePopSearchDayStartOpt";
                  break;
                case 3015:
                case 6015:
                  is_click = false;
                  result.nodeIds.forEach(function(nodeId:any) {
                    //检索的是本月 只点击第一个出现的相等数字
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                      let html = result.outerHTML;
                      if(html.indexOf('leads-date-grid-prev')!=-1){
                        return '';
                      }
                      if(html.indexOf('leads-date-disabled')!=-1){
                        return '';
                      }
                      if(html.indexOf('leads-date-grid-next')!=-1){
                        return '';
                      }
                      if(is_click){
                        //已经点击 停止
                        console.log("已经点击 停止");
                        return '';
                      }

                      let text = result.outerHTML.replace(/<[^>]*>/g, '');
                      if(text == date_start_day){
                        is_click = true;
                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                            objectId: object.object.objectId,
                            functionDeclaration: 'function() { this.click(); }'
                          });
                        });
                      }
                    });
                  });
                  next = "pageDateSelectDomPrivatePopSearchDayEndOpt";
                  break;
                case 3016:
                case 6016:
                  is_click = false;
                  result.nodeIds.forEach(function(nodeId:any) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                      let html = result.outerHTML;
                      if(html.indexOf('leads-date-grid-prev')!=-1){
                        return '';
                      }
                      if(html.indexOf('leads-date-disabled')!=-1){
                        return '';
                      }
                      if(html.indexOf('leads-date-grid-next')!=-1){
                        return '';
                      }
                      if(is_click){
                        //已经点击 停止
                        console.log("已经点击 停止");
                        return '';
                      }
                      let text = result.outerHTML.replace(/<[^>]*>/g, '');
                      if(text == date_end_day){
                        is_click = true;
                        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                            objectId: object.object.objectId,
                            functionDeclaration: 'function() { this.click(); }'
                          }, function() {
                             setTimeout(() => {
                              console.log("隐藏日期操作框");
                              let selector_class = ".leads-content-inner-wrapper.leads-drawer-content-inner-wrapper,.leads-loading.leads-loading-block";
                              if(type==6016){
                                selector_class = ".fang-leads-date-time-range-picker-input";
                                console.log("巨好房检索完毕 隐藏日期选择框，选择器=="+selector_class);
                              }
                              chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                                nodeId: root.root.nodeId,
                                selector: selector_class
                              }, function(result:any) {
                                chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: result.nodeIds[0]}, function(object:any) {
                                  chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                    objectId: object.object.objectId,
                                    functionDeclaration: 'function() { this.click(); }'
                                  }, function() {
                                    // 在这里返回响应
                                  });
                                });
                              });
                            }, 2000);
                          });
                        });


                      }
                    });
                  });
                  break;
                case 3017:
                case 6017:
                  result.nodeIds.forEach(function(nodeId:any) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
                      let text = result.outerHTML.replace(/<[^>]*>/g, '');
                      console.log(text);
                      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: nodeId}, function(object:any) {
                        chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                          objectId: object.object.objectId,
                          functionDeclaration: 'function() { this.click(); }'
                        });
                      });
                    });
                  });
                  next = "none";
                  break;
              }

              code = 3;
              msg  = "";
              opt  = "";
              res = {
                code: code,
                msg: msg,
                opt: opt,
                type:type,
                tabId:tabId,
                next:next
              };
              pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
           }



          }else{
            code = 2;//警告通知类型
            msg  = "没有找到"+type_config.name+"元素";
            opt  = "showNotice";
            res = {
              code: code,
              msg: msg,
              opt: opt,
              type:type
            };
            //pageMsg.sendMessageToContentScript(res,()=>{});
            pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
            console.log("没有找到"+type_config.name+"元素");
          }

        });
      }

    });
  });

}


export const operationDom = (type:number,tabId:number)=>{
  let res = {};
  let code = 0; //2 警告通知类型 3 成功通知类型
  let msg  = '';
  let opt  = '';//showNotice
  let next = '';

  let type_config = config.domRule[type as keyof typeof config.domRule];
  let xpath = type_config.xpath;
  console.log(type_config);
  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getDocument', {}, function(root:any) {
        if(type_config.search_type=='xpath'){
          // 使用XPath查询元素
          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.performSearch', {
              query: xpath
          }, function(result:any) {
              // 如果找到了元素，模拟点击事件
              if (result.searchId && result.resultCount > 0) {
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getSearchResults', {
                      searchId: result.searchId,
                      fromIndex: 0,
                      toIndex: result.resultCount
                  }, function(nodes:any) {
                        if (nodes.nodeIds.length > 0) {
                          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {
                              nodeId: nodes.nodeIds[0]
                            }, function(result:any) {
                              //当前只考虑此种方式
                              if(type_config.attr!='input'){
                                // 使用正则表达式提取文本内容
                                var text = result.outerHTML.replace(/<[^>]*>/g, '');
                                console.log('Element text:', text);
                                //if(text.indexOf(type_config.name)!=-1){
                                //type_config.disable_css;
                                if(type_config.disable_css){
                                  //判断该元素的class属性 是否包含 leads-btn-disabled
                                  if(result.outerHTML.indexOf(type_config.disable_css)!=-1){
                                    code = 2;//警告通知类型
                                    msg  = "不支持超过13天的直播间评论\r\n请选择另一场直播间数据";
                                    opt  = "showNotice";
                                    res = {
                                      code: code,
                                      msg: msg,
                                      opt: opt,
                                      type:type,
                                      next:'getLivingRoomList',
                                    };
                                    //pageMsg.sendMessageToContentScript(res,()=>{});
                                    pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                                    return true;
                                  }
                                }


                                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {
                                    nodeId: nodes.nodeIds[0]
                                  }, function(result:any) {
                                    if(type!=102){
                                      //直播间评论不点击 主动跳转到直播间评论页面 不打开新的标签页 方便管理
                                      chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                                          objectId: result.object.objectId,
                                          functionDeclaration: 'function() { this.click(); }'
                                      });
                                    }

                                      code = 3;//成功通知类型
                                      msg  = "";
                                      opt  = "showNotice";
                                      if(type==1){
                                        //点击后请求接口相关的 不返回弹窗通知 返回none 接口获取数据成功后由接口发送通知
                                        opt  = "none";
                                      }
                                      if(type==102){
                                        next = 'openCommentList';//打开直播间评论
                                      }

                                      res = {
                                        code: code,
                                        msg: msg,
                                        opt: opt,
                                        type:type,
                                        next:next
                                      };
                                      //pageMsg.sendMessageToContentScript(res,()=>{});
                                      pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                                  });

                                  /* chrome.debugger.sendCommand({tabId: tabId}, 'DOM.focus', {
                                      nodeId: nodes.nodeIds[0]
                                  }, function() {
                                      chrome.debugger.sendCommand({tabId: tabId}, 'Input.dispatchMouseEvent', {
                                          type: 'mousePressed',
                                          button: 'left',
                                          clickCount: 1,
                                          x: 0,
                                          y: 0
                                      }, function() {
                                          chrome.debugger.sendCommand({tabId: tabId}, 'Input.dispatchMouseEvent', {
                                              type: 'mouseReleased',
                                              button: 'left',
                                              clickCount: 1,
                                              x: 0,
                                              y: 0
                                          });
                                      });
                                  }); */
                                /* }else{
                                  code = 2;//警告通知类型
                                  msg  = "请联系客服反馈该信息：当前获取的点击元素内容为: " + text + "，不是"+type_config.name
                                  opt  = "showNotice";
                                  res = {
                                    code: code,
                                    msg: msg,
                                    opt: opt
                                  };
                                  pageMsg.sendMessageToContentScript(res,()=>{});
                                  console.log("当前获取的点击元素内容为: " + text + "，不是"+type_config.name);
                                }   */
                              }
                          });

                      }else{
                        code = 2;//警告通知类型
                        msg  = "没有找到"+type_config.name+"元素";
                        opt  = "showNotice";
                        res = {
                          code: code,
                          msg: msg,
                          opt: opt,
                          type:type
                        };
                        //pageMsg.sendMessageToContentScript(res,()=>{});
                        pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                        console.log("没有找到"+type_config.name+"元素");
                      }
                  });
              }else{
                code = 2;//警告通知类型
                msg  = "没有找到"+type_config.name+"元素";
                opt  = "showNotice";
                res = {
                  code: code,
                  msg: msg,
                  opt: opt,
                  type:type
                };
                //pageMsg.sendMessageToContentScript(res,()=>{});
                pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                console.log("没有找到"+type_config.name+"元素");
              }

          });
        }else if(type_config.search_type=='css'){
          //使用CSS查询元素
          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
              nodeId: root.root.nodeId,
              selector: type_config.css_path
          }, function(result:any) {
              // 如果找到了元素，模拟点击事件
              if (result.nodeIds.length > 0) {
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.focus', {
                      nodeId: result.nodeIds[0]
                  }, function() {
                      chrome.debugger.sendCommand({tabId: tabId}, 'Input.dispatchMouseEvent', {
                          type: 'mousePressed',
                          button: 'left',
                          clickCount: 1,
                          x: 0,
                          y: 0
                      }, function() {
                          chrome.debugger.sendCommand({tabId: tabId}, 'Input.dispatchMouseEvent', {
                              type: 'mouseReleased',
                              button: 'left',
                              clickCount: 1,
                              x: 0,
                              y: 0
                          });
                      });
                  });
              }
          });
        }else if(type_config.search_type=='class'){
          //使用CSS查询元素
          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
            nodeId: root.root.nodeId,
            selector: type_config.css_path
          }, function(result:any) {
              // 如果找到了元素，模拟点击事件
              console.log("===========使用CLASS查询元素1============");
              console.log(result.nodeIds.length);
              console.log(type_config.class_index-1)
              let class_index = type_config.class_index-1;
              if(class_index<0){
                class_index = 0;
              }
              chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: result.nodeIds[class_index]}, function(result:any) {
                console.log("元素的信息: " + result.outerHTML);
              });
              if (result.nodeIds.length > 0) {
                  let class_index = type_config.class_index-1;
                  if(class_index<0){
                    class_index = 0;
                  }
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {
                      nodeId: result.nodeIds[class_index]
                  }, function(object:any) {
                      chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                          objectId: object.object.objectId,
                          functionDeclaration: 'function() { this.click(); }'
                      });
                  });
              }
          });
        }
    });
  });
}

//获取给定元素的数量
export const getDomNumber = async (type:number,tabId:number)=>{
  let res = {};
  let code = 0; //2 警告通知类型 3 成功通知类型
  let msg  = '';
  let opt  = '';//showNotice
  let next = '';

  let type_config = config.domRule[type as keyof typeof config.domRule];
  let xpath = type_config.xpath;

  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getDocument', {}, function(root:any) {
        if(type_config.search_type=='class'){
          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
              nodeId: root.root.nodeId,
              selector: type_config.css_path
          }, function(result:any) {
              code = 3;//警告通知类型
              msg  = "成功获取数量";
              opt  = "updateNumber";
              res = {
                code: code,
                msg: msg,
                opt: opt,
                tabId:tabId,
                type:type,
                data:{
                  num:result.nodeIds.length
                },
                next:""
              };
              pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
            });
        }
      });
  });
}



export const operationDomVideoComment = async (type:number,tabId:number)=>{
  let res = {};
  let code = 0; //2 警告通知类型 3 成功通知类型
  let msg  = '';
  let opt  = '';//showNotice
  let next = '';

  let type_config = config.domRule[type as keyof typeof config.domRule];
  let xpath = type_config.xpath;
  console.log(type_config);

  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.enable', {}, function() {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getDocument', {}, function(root:any) {
        if(type_config.search_type=='class'){
          //使用CSS查询元素

          let parentNodeId = root.root.nodeId

          chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
              nodeId: root.root.nodeId,
              selector: type_config.css_path
          }, function(result:any) {
              console.log("使用CLASS查询元素");
              console.log(result.nodeIds);
              // 如果找到了元素，模拟点击事件

              if(type==5012){
                console.log("mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm");
                console.log("当前共"+result.nodeIds.length+"个未点击的短视频数量");
                code = 3;//警告通知类型
                msg  = "当前共"+result.nodeIds.length+"个未点击的短视频数量";
                opt  = "updateNumber";
                res = {
                  code: code,
                  msg: msg,
                  opt: opt,
                  tabId:tabId,
                  type:type,
                  data:{
                    num:result.nodeIds.length
                  },
                  next:""
                };
                pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
              }



              if (result.nodeIds.length > 0) {

                getOuterHTML(tabId, result.nodeIds[0]).then((text_content) => {
                  if(text_content.indexOf('没有')!=-1){
                    code = 3;//警告通知类型
                    msg  = "短视频列表已全部获取";
                    opt  = "showNotice";
                    res = {
                      code: code,
                      msg: msg,
                      opt: opt,
                      tabId:tabId,
                      type:type,
                      next:"dataSuccess"
                    };
                    //pageMsg.sendMessageToContentScript(res,()=>{});
                    pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                    console.log("短视频列表已全部获取");
                    return false;
                  }

                  if(text_content.indexOf('更多')!=-1){
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.scrollIntoViewIfNeeded', {
                      nodeId: result.nodeIds[0]
                    });
                  }

                  let nodeId = result.nodeIds[0];

                  setTimeout(() => {


                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {
                        nodeId: nodeId
                      }, function(result:any) {

                        chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                            objectId: result.object.objectId,
                            functionDeclaration: 'function() { this.click(); }'
                        });
                    });
                    console.log("点击完成.............");




                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getAttributes', {
                      nodeId: nodeId
                    }, function(result:any) {
                      if (chrome.runtime.lastError) {
                        console.error('Error:', chrome.runtime.lastError.message);
                      } else {
                        let classIndex = result.attributes.indexOf('class');
                        let currentClassValue = '';
                        if (classIndex >= 0) {
                          currentClassValue = result.attributes[classIndex + 1];
                          console.log('当前的 class 值:', currentClassValue);

                          if(currentClassValue.indexOf('disabled')!==-1){
                            //下一页按钮已经禁用 没有更多数据
                            code = 3;//警告通知类型
                            msg  = "当前短视频评论已全部获取，继续下一条短视频";
                            opt  = "showNotice";
                            res = {
                              code: code,
                              msg: msg,
                              opt: opt,
                              tabId:tabId,
                              type:type,
                              next:"clickViewComment"
                            };
                            //pageMsg.sendMessageToContentScript(res,()=>{});
                            pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                            console.log("当前短视频评论已全部获取，继续下一条短视频");
                            return false;
                          }

                        }

                        if(type==5012){
                          if(currentClassValue.indexOf('operation_over')===-1){
                            let newClassValue = currentClassValue ? currentClassValue + ' operation_over' : 'operation_over';
                            console.log('newClassValue======', newClassValue);
                            chrome.debugger.sendCommand({tabId: tabId}, 'DOM.setAttributeValue', {
                              nodeId: nodeId,
                              name: 'class',
                              value: newClassValue
                            });
                          }
                        }




                      }
                    });

                  }, 1000);

                  if(text_content.indexOf('更多')!=-1){
                    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.scrollIntoViewIfNeeded', {
                      nodeId: result.nodeIds[0]
                    });
                  }


              });

            }else{
              //遮罩层不提示
              if(type_config.css_path.indexOf('mask')==-1 && type!=5012 && type!=5014 && type!=5015){
                code = 2;//警告通知类型
                msg  = "没有找到"+type_config.name+"元素";
                opt  = "showNotice";
                res = {
                  code: code,
                  msg: msg,
                  opt: opt,
                  tabId:tabId,
                  type:type
                };
                //pageMsg.sendMessageToContentScript(res,()=>{});
                pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                console.log("没有找到"+type_config.name+"元素");
              }

              if(type==5012){
                //当前评论列表已全部获取 点击继续加载列表
                code = 3;//警告通知类型
                msg  = "当前短视频和评论已全部获取，加载更多";
                opt  = "showNotice";
                res = {
                  code: code,
                  msg: msg,
                  opt: opt,
                  tabId:tabId,
                  type:type,
                  next:"pageNext"
                };
                //pageMsg.sendMessageToContentScript(res,()=>{});
                pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                console.log("当前短视频和评论已全部获取，加载更多");
                return true;
              }

              if(type==5014){
                //只有一页评论时
                code = 3;//警告通知类型
                msg  = "当前短视频评论已全部获取，继续下一条短视频";
                opt  = "showNotice";
                res = {
                  code: code,
                  msg: msg,
                  opt: opt,
                  tabId:tabId,
                  type:type,
                  next:"clickViewComment"
                };
                //pageMsg.sendMessageToContentScript(res,()=>{});
                pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                console.log("当前短视频评论已全部获取，继续下一条短视频");
                return true;

              }

              if(type==5015){
                code = 3;//通知类型
                msg  = "当前短视频评论回复已全部获取，继续下一页评论";
                opt  = "showNotice";
                res = {
                  code: code,
                  msg: msg,
                  opt: opt,
                  tabId:tabId,
                  type:type,
                  next:"clickCommentNextPage"
                };
                //pageMsg.sendMessageToContentScript(res,()=>{});
                pageMsg.sendMessageToContentScripByTabId(tabId,res,()=>{});
                console.log("当前短视频评论没有回复，继续下一页评论");
                return true;

              }


            }
          });


        }
    });
  });
}

export const getOuterHTML = (tabId:number,nodeId:number)=>{
  return new Promise((resolve, reject) => {
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: nodeId}, function(result:any) {
      if(chrome.runtime.lastError){
        reject(chrome.runtime.lastError);
      }else{
        let text = result.outerHTML.replace(/<[^>]*>/g, '');
        resolve(text);
      }
    });
  });
}
