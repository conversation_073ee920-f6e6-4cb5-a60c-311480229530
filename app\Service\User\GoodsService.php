<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\N11\ProductCategoryN11Model;
use App\Exceptions\MyException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class GoodsService extends BaseService
{
    /**
     * 获取商品列表（分页）
     */
    public function getGoodsList(int $userId, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $type = $params['type'] ?? null;
        $status = $params['status'] ?? null;
        $goodsName = $params['goods_name'] ?? null;
        $goodsId = $params['goods_id'] ?? null;
        $directoryId = $params['directory_id'] ?? null;

        if(empty($directoryId)){
            throw new MyException('必须选择一个目录');
        }

        // 构建查询
        $query = GoodsModel::byUserId($userId);

        // 按商品类型筛选
        if ($type && in_array($type, [1])) {
            $query->byType($type);
        }

        if ($goodsName) {
            $query->where('goods_name', 'like', "%{$goodsName}%");
        }

        if ($goodsId) {
            $query->where('goods_id', $goodsId);
        }

        if (is_numeric($status) && in_array($status, [0, 1])) {
            $query->byStatus($status);
        }

        // 按目录ID筛选
        if (is_numeric($directoryId)) {
            $query->byDirectoryId($directoryId);
        }

        // 排序
        $query->orderBy('id', 'desc');

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        // 关联前端分类2  frontCategory2
        // 20250605 强制改成 cat_id
        $goods = $query->offset($offset)
                      ->limit($pageSize)
                      ->with(['skus', 'frontCategory2', 'catRelations'])
                      ->get()
                      ->map(function($item) {
                          return $this->formatGoodsData($item);
                      });

        return [
            'list' => $goods,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 创建商品
     */
    public function createGoods(int $userId, array $data): array
    {
        // 验证数据
        $this->validateGoodsData($data, true);

        // 检查同一用户下是否已存在相同的商品ID和类型
        $exists = GoodsModel::byUserId($userId)
            ->where('type', $data['type'])
            ->where('goods_id', $data['goods_id'])
            ->exists();

        if ($exists) {
            throw new MyException('该商品已存在');
        }

        DB::beginTransaction();
        try {
            // 创建商品
            $goodsData = array_merge($data, [
                'user_id' => $userId,
                'status' => $data['status'] ?? 1,
            ]);

            // 处理JSON字段
            if (isset($goodsData['goods_pic']) && is_string($goodsData['goods_pic'])) {
                $goodsData['goods_pic'] = json_decode($goodsData['goods_pic'], true);
            }
            if (isset($goodsData['goods_sku']) && is_string($goodsData['goods_sku'])) {
                $goodsData['goods_sku'] = json_decode($goodsData['goods_sku'], true);
            }
            if (isset($goodsData['goods_property']) && is_string($goodsData['goods_property'])) {
                $goodsData['goods_property'] = json_decode($goodsData['goods_property'], true);
            }

            $goods = GoodsModel::create($goodsData);

            // 创建SKU数据
            if (!empty($data['skus']) && is_array($data['skus'])) {
                foreach ($data['skus'] as $skuData) {
                    $skuData['user_goods_id'] = $goods->id;
                    $skuData['goods_id'] = $goods->goods_id;
                    
                    // 处理JSON字段
                    if (isset($skuData['skc_gallery']) && is_string($skuData['skc_gallery'])) {
                        $skuData['skc_gallery'] = json_decode($skuData['skc_gallery'], true);
                    }
                    
                    GoodsSkuModel::create($skuData);
                }
            }

            DB::commit();

            return [
                'id' => $goods->id,
                'message' => '商品创建成功'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('商品创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新商品
     */
    public function updateGoods(int $userId, array $data): array
    {
        // 验证数据
        $this->validateGoodsData($data, false);

        if (!isset($data['id'])) {
            throw new MyException('商品ID不能为空');
        }

        // 查找商品并验证所有权
        $goods = GoodsModel::find($data['id']);
        
        if (!$goods) {
            throw new MyException('商品不存在');
        }

        if (!$goods->belongsToUser($userId)) {
            throw new MyException('无权限操作此商品');
        }

        // 如果更新了type或goods_id，检查是否重复
        if (isset($data['type']) || isset($data['goods_id'])) {
            $type = $data['type'] ?? $goods->type;
            $goodsId = $data['goods_id'] ?? $goods->goods_id;
            
            $exists = GoodsModel::byUserId($userId)
                ->where('type', $type)
                ->where('goods_id', $goodsId)
                ->where('id', '!=', $goods->id)
                ->exists();

            if ($exists) {
                throw new MyException('该商品已存在');
            }
        }

        DB::beginTransaction();
        try {
            // 更新商品
            $updateData = $data;
            unset($updateData['id'], $updateData['skus']);

            // 处理JSON字段
            if (isset($updateData['goods_pic']) && is_string($updateData['goods_pic'])) {
                $updateData['goods_pic'] = json_decode($updateData['goods_pic'], true);
            }
            if (isset($updateData['goods_sku']) && is_string($updateData['goods_sku'])) {
                $updateData['goods_sku'] = json_decode($updateData['goods_sku'], true);
            }
            if (isset($updateData['goods_property']) && is_string($updateData['goods_property'])) {
                $updateData['goods_property'] = json_decode($updateData['goods_property'], true);
            }

            $goods->update($updateData);

            // 更新SKU数据
            if (isset($data['skus']) && is_array($data['skus'])) {
                // 删除原有SKU
                GoodsSkuModel::where('user_goods_id', $goods->id)->delete();
                
                // 创建新SKU
                foreach ($data['skus'] as $skuData) {
                    $skuData['user_goods_id'] = $goods->id;
                    $skuData['goods_id'] = $goods->goods_id;
                    
                    // 处理JSON字段
                    if (isset($skuData['skc_gallery']) && is_string($skuData['skc_gallery'])) {
                        $skuData['skc_gallery'] = json_decode($skuData['skc_gallery'], true);
                    }
                    
                    GoodsSkuModel::create($skuData);
                }
            }

            DB::commit();

            return [
                'message' => '商品更新成功'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('商品更新失败：' . $e->getMessage());
        }
    }

    /**
     * 批量更新商品
     */
    public function batchUpdateGoods(int $userId, array $data): array
    {
        $count = GoodsModel::where('user_id', $userId)->whereIn('id', $data['ids'])->count();
        if ($count != count($data['ids'])) {
            throw new MyException('参数错误');
        }
        
        $ids = $data['ids'];
        unset($data['ids']);
        
        if (count($data) == 0) {
            throw new MyException('请至少选择一项');
        }
        
        GoodsModel::where('user_id', $userId)->whereIn('id', $ids)->update($data);
        
        return [
            'message' => '商品批量更新成功'
        ];
    }

    /**
     * 删除商品
     */
    public function deleteGoods(int $userId, int $goodsId): array
    {
        // 验证商品ID
        if (!$goodsId) {
            throw new MyException('商品ID不能为空');
        }

        // 查找商品并验证所有权
        $goods = GoodsModel::find($goodsId);
        
        if (!$goods) {
            throw new MyException('商品不存在');
        }

        if (!$goods->belongsToUser($userId)) {
            throw new MyException('无权限操作此商品');
        }

        DB::beginTransaction();
        try {
            // 删除关联的SKU
            GoodsSkuModel::where('user_goods_id', $goods->id)->delete();
            
            // 删除商品
            $goods->delete();

            DB::commit();

            return [
                'message' => '商品删除成功'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('商品删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取商品详情
     */
    public function getGoodsDetail(int $userId, int $goodsId): array
    {
        // 验证商品ID
        if (!$goodsId) {
            throw new MyException('商品ID不能为空');
        }

        // 查找商品并验证所有权
        $goods = GoodsModel::with(['skus', 'frontCategory1', 'frontCategory2'])->find($goodsId);
        
        if (!$goods) {
            throw new MyException('商品不存在');
        }

        if (!$goods->belongsToUser($userId)) {
            throw new MyException('无权限查看此商品');
        }

        return $this->formatGoodsDetailData($goods);
    }

    /**
     * 验证商品数据
     */
    private function validateGoodsData(array $data, bool $isCreate = false): void
    {
        $rules = [];
        
        if ($isCreate) {
            $rules = [
                'type' => ['required', 'integer', Rule::in([1])],
                'goods_id' => 'required|integer',
                'goods_name' => 'required|string|max:500',
            ];
        } else {
            $rules = [
                'id' => 'required|integer',
                'type' => ['sometimes', 'integer', Rule::in([1])],
                'goods_id' => 'sometimes|integer',
                'goods_name' => 'sometimes|string|max:500',
            ];
        }

        // 通用验证规则
        $commonRules = [
            'source_url' => 'nullable|string',
            'cat_id' => 'nullable|integer',
            'front_cat_id_1' => 'nullable|integer',
            'front_cat_id_2' => 'nullable|integer',
            'front_cat_desc' => 'nullable|string|max:500',
            'mall_id' => 'nullable|integer',
            'goods_detail' => 'nullable|string',
            'goods_video' => 'nullable|string|max:500',
            'goods_pic' => 'nullable',
            'goods_sku' => 'nullable',
            'goods_sku_num' => 'nullable|integer|min:1',
            'goods_property' => 'nullable',
            'status' => 'sometimes|integer|in:0,1',
            'directory_id' => 'nullable|integer|min:0',
            'skus' => 'nullable|array',
            'skus.*.sku_id' => 'required_with:skus|integer',
            'skus.*.thumb_url' => 'nullable|string|max:500',
            'skus.*.currentcy' => 'nullable|string|max:20',
            'skus.*.price' => 'nullable|numeric|min:0',
            'skus.*.spec_key_values' => 'nullable|string|max:50',
            'skus.*.spec_values' => 'nullable|string|max:255',
            'skus.*.skc_gallery' => 'nullable',
            'skus.*.is_skc_gallery' => 'nullable|boolean',
            'skus.*.url' => 'nullable|string|max:500',
        ];

        $rules = array_merge($rules, $commonRules);

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new MyException($validator->errors()->first());
        }
    }

    /**
     * 格式化商品数据（列表用）
     */
    private function formatGoodsData($goods): array
    {
        //systemLog(json_encode($goods,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),'goods');
        $front_cat_2_path_name = $goods->frontCategory2?->path_name ?? '';
        $front_cat_2_path_name = str_ireplace(',', '->', $front_cat_2_path_name);
        $goods_pic = $goods->goods_pic;
        if(!empty($goods_pic)){
            $goods_pic = json_decode($goods_pic,true);
        }
        $goods_thumb = '';
        if(is_array($goods_pic)){
            $goods_pic = array_map(function($pic) {
                return uploadFilePath($pic);
            }, $goods_pic);

            $goods_thumb = $goods_pic[0] ?? '';
            if(!empty($goods_thumb)){
                $goods_thumb = uploadFilePath($goods_thumb);
            }
        }
        $goods_skus = $goods->skus;
        $formattedSkus = [];
        $first_sku = '';
        $first_sku_price = 0;
        $first_sku_currentcy = '';
        $first_sku_thumb_url = '';//小图 
        $first_sku_thumb_url_h500 = '';//大图
        if($goods->goods_sku_num > 0){
            foreach ($goods_skus as $key=>$sku) {
                if($key == 0){
                    $first_sku = $sku->spec_key_values;
                    $first_sku_price = $sku->price;
                    $first_sku_currentcy = $sku->currentcy;
                    $first_sku_thumb_url = $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '';
                    $first_sku_thumb_url_h500 = $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '';
                }
                $formattedSkus[] = [
                    'sku' => $sku->spec_key_values,
                    'price' => $sku->price,
                    'currentcy' => $sku->currentcy,
                    'thumb_url' => $sku->thumb_url ? uploadFilePath($sku->thumb_url) : '',
                    'thumb_url_h500' => $sku->thumb_url ? uploadFilePath($sku->thumb_url) : ''
                ];
            }
        }

        // 处理关联分类信息
        $platformRelations = $this->formatPlatformRelations($goods->catRelations);

        return [
            'id' => $goods->id,
            'type' => $goods->type,
            'type_name' => $goods->getTypeName(),
            'source_url' => $goods->source_url,
            'mall_id' => $goods->mall_id,
            'goods_id' => $goods->goods_id,
            'goods_name' => $goods->goods_name,
            'goods_thumb' => $goods_thumb,
            'goods_pic' => $goods_pic,
            'goods_video' => $goods->goods_video,
            'goods_sku_num' => $goods->goods_sku_num,
            'first_sku' => $first_sku,
            'first_sku_price' => $first_sku_price,
            'first_sku_currentcy' => $first_sku_currentcy,
            'first_sku_thumb_url' => $first_sku_thumb_url,
            'first_sku_thumb_url_h500' => $first_sku_thumb_url_h500,
            'formatted_skus' => $formattedSkus,
            'goods_property' => $goods->goods_property,
            'goods_score' => $goods->goods_score,
            'goods_sold_quantity' => $goods->goods_sold_quantity,
            'status' => $goods->status,
            'cat_id' => $goods->cat_id,
            'cat_name' => $goods->frontCategory2?->name ?? '',
            'front_cat_id_2' => $goods->front_cat_id_2,
            'front_cat_desc' => $goods->front_cat_desc,
            'front_cat_2_path_name' => $front_cat_2_path_name,
            'platform_relations' => $platformRelations,
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
        ];
    }

    /**
     * 格式化商品详情数据
     */
    private function formatGoodsDetailData($goods): array
    {
        return [
            'id' => $goods->id,
            'user_id' => $goods->user_id,
            'type' => $goods->type,
            'type_name' => $goods->getTypeName(),
            'source_url' => $goods->source_url,
            'cat_id' => $goods->cat_id,
            'front_cat_id_1' => $goods->front_cat_id_1,
            'front_cat_id_2' => $goods->front_cat_id_2,
            'front_cat_desc' => $goods->front_cat_desc,
            'front_cat_1_path_name' => $goods->frontCategory1?->path_name ?? '',
            'front_cat_2_path_name' => $goods->frontCategory2?->path_name ?? '',
            'mall_id' => $goods->mall_id,
            'goods_id' => $goods->goods_id,
            'goods_name' => $goods->goods_name,
            'goods_detail' => $goods->goods_detail,
            'goods_video' => $goods->goods_video,
            'goods_pic' => $goods->goods_pic,
            'goods_sku' => $goods->goods_sku,
            'goods_sku_num' => $goods->goods_sku_num,
            'goods_property' => $goods->goods_property,
            'status' => $goods->status,
            'skus' => $goods->skus->map(function($sku) {
                return [
                    'id' => $sku->id,
                    'sku_id' => $sku->sku_id,
                    'thumb_url' => $sku->thumb_url,
                    'currentcy' => $sku->currentcy,
                    'price' => $sku->price,
                    'spec_key_values' => $sku->spec_key_values,
                    'spec_values' => $sku->spec_values,
                    'skc_gallery' => $sku->skc_gallery,
                    'is_skc_gallery' => $sku->is_skc_gallery,
                    'url' => $sku->url,
                    'created_at' => $sku->created_at,
                    'updated_at' => $sku->updated_at,
                ];
            }),
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
        ];
    }

    /**
     * 获取需要处理图片的商品ID列表
     */
    public function getGoodsNeedImageProcess(int $userId, int $directoryId): array
    {
        $goodsIds = GoodsModel::byUserId($userId)
            ->byDirectoryId($directoryId)
            ->byStatus(1) // 只获取正常状态的商品
            ->where('img_local_status', 0) // 图片未处理完成
            ->pluck('id')
            ->toArray();

        return [
            'goods_ids' => $goodsIds,
            'total_count' => count($goodsIds)
        ];
    }

    /**
     * 格式化平台关联信息
     */
    private function formatPlatformRelations($catRelations): ?array
    {
        if ($catRelations->isEmpty()) {
            return null;
        }

        $relations = [];
        foreach ($catRelations as $relation) {
            $relationData = [
                'id' => $relation->id,
                'platform_id' => $relation->platform_id,
                'third_platform_id' => $relation->third_platform_id,
                'cat_third_ids' => $relation->cat_third_ids,
                'third_platform_categories' => null,
            ];

            // 如果是N11平台(third_platform_id = 2)，获取分类详情
            if ($relation->third_platform_id == 2) {
                $relationData['third_platform_categories'] = $relation->n11_categories->map(function($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'name_tl' => $category->name_tl,
                        'path_name' => str_ireplace(',', '->', $category->path_name ?? ''),
                        'path_name_tl' => str_ireplace(',', '->', $category->path_name_tl ?? ''),
                    ];
                })->toArray();
            }

            $relations[] = $relationData;
        }

        return $relations;
    }

    /**
     * 获取商品统计信息
     */
    public function getGoodsStatistics(int $userId, array $params): array
    {
        $directoryId = $params['directory_id'] ?? 0;
        $timeRange = $params['time_range'] ?? 'today';
        $dayStart = $params['day_start'] ?? null;
        $dayEnd = $params['day_end'] ?? null;

        // 构建查询
        $query = GoodsModel::byUserId($userId)->byStatus(1);

        // 按目录ID筛选
        if (is_numeric($directoryId)) {
            $query->byDirectoryId($directoryId);
        }

        // 按时间范围筛选
        $this->applyTimeRangeFilter($query, $timeRange, $dayStart, $dayEnd);

        // 获取商品数量
        $goodsCount = $query->count();

        // 获取SKU数量
        $skuCount = $query->sum('goods_sku_num');

        return [
            'goods_count' => $goodsCount,
            'sku_count' => (int)$skuCount,
            'directory_id' => $directoryId,
            'time_range' => $timeRange,
            'day_start' => $dayStart,
            'day_end' => $dayEnd
        ];
    }

    /**
     * 应用时间范围筛选
     */
    private function applyTimeRangeFilter($query, string $timeRange, ?string $dayStart = null, ?string $dayEnd = null): void
    {
        switch ($timeRange) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'yesterday':
                $query->whereDate('created_at', today()->subDay());
                break;
            case 'lastweek':
                $query->whereBetween('created_at', [
                    today()->subWeek()->startOfDay(),
                    today()->endOfDay()
                ]);
                break;
            case 'custom':
                if ($dayStart && $dayEnd) {
                    $query->whereBetween('created_at', [
                        $dayStart . ' 00:00:00',
                        $dayEnd . ' 23:59:59'
                    ]);
                }
                break;
            case 'all':
            default:
                // 不添加时间筛选
                break;
        }
    }
} 