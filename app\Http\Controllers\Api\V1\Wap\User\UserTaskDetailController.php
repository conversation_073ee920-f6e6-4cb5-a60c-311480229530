<?php

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserTaskDetailService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserTaskDetailController extends Controller
{
    protected UserTaskDetailService $userTaskDetailService;

    public function __construct(UserTaskDetailService $userTaskDetailService)
    {
        $this->userTaskDetailService = $userTaskDetailService;
        parent::__construct();
    }

    /**
     * 获取任务详情
     */
    public function getTaskDetail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $taskId = $request->input('task_id');
        $result = $this->userTaskDetailService->getTaskDetail($userId, (int)$taskId);
        return $this->apiSuccess($result);
    }

    /**
     * 获取任务详情列表（分页）
     */
    public function getTaskDetailList(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = $request->input('task_id');
        
        $params = $request->only(['page', 'pageSize', 'status', 'goods_name', 'created_at_start', 'created_at_end']);
        $params['task_id'] = (int)$taskId;
        
        $result = $this->userTaskDetailService->getTaskDetailList($userId, $params);
        return $this->apiSuccess($result);

    }

    /**
     * 获取任务详情统计信息
     */
    public function getTaskDetailStatistics(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];

        $taskId = $request->input('task_id');
        $result = $this->userTaskDetailService->getTaskDetailStatistics($userId, (int)$taskId);
        return $this->apiSuccess($result);

    }

    /**
     * 批量重试失败的任务详情
     */
    public function batchRetryFailedTasks(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        // 验证任务ID参数
        $taskId = $request->input('task_id');
        $detailIds = $request->input('detail_ids', []);
        
        $result = $this->userTaskDetailService->batchRetryFailedTasks($userId, (int)$taskId, $detailIds);
        return $this->apiSuccess($result, '批量重试操作已提交');

    }

    /**
     * 查询任务结果
     */
    public function queryTaskResults(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = $request->input('task_id');
        
        if (empty($taskId)) {
            return $this->apiError('任务ID不能为空');
        }
        
        $result = $this->userTaskDetailService->queryTaskResults($userId, (int)$taskId);
        return $this->apiSuccess($result, '任务结果查询完成');
    }

    /**
     * 获取待查询结果的任务列表
     */
    public function getPendingQueryTasks(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = $request->input('task_id');
        $limit = $request->input('limit', 500);
        $offset = $request->input('offset', 0);
        
        if (empty($taskId)) {
            return $this->apiError('任务ID不能为空');
        }
        
        $result = $this->userTaskDetailService->getPendingQueryTasks($userId, (int)$taskId, (int)$limit, (int)$offset);
        return $this->apiSuccess($result);
    }

    /**
     * 批量更新任务详情状态
     */
    public function batchUpdateTaskDetails(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $updates = $request->input('updates', []);
        
        if (empty($updates)) {
            return $this->apiError('更新数据不能为空');
        }
        
        $result = $this->userTaskDetailService->batchUpdateTaskDetails($userId, $updates);
        return $this->apiSuccess($result, '批量更新完成');
    }

    /**
     * 保存上传参数
     */
    public function saveUploadParams(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskDetailId = $request->input('task_detail_id');
        $thirdType = $request->input('third_type', 'n11');
        $params = $request->input('params');
        
        if (empty($taskDetailId) || empty($params)) {
            return $this->apiError('参数不完整');
        }
        
        $result = $this->userTaskDetailService->saveUploadParams($userId, $taskDetailId, $thirdType, $params);
        return $this->apiSuccess($result, '上传参数保存成功');
    }

    /**
     * 获取重新上传参数
     */
    public function getRetryUploadParams(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskDetailId = $request->input('task_detail_id');
        
        if (empty($taskDetailId)) {
            return $this->apiError('任务详情ID不能为空');
        }
        
        $result = $this->userTaskDetailService->getRetryUploadParams($userId, (int)$taskDetailId);
        return $this->apiSuccess($result);
    }

    /**
     * 批量重新上传失败任务
     */
    public function batchRetryUpload(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $taskId = $request->input('task_id');
        $detailIds = $request->input('detail_ids', []);
        
        if (empty($taskId)) {
            return $this->apiError('任务ID不能为空');
        }
        
        $result = $this->userTaskDetailService->batchRetryUpload($userId, (int)$taskId, $detailIds);
        return $this->apiSuccess($result, '批量重新上传任务已启动');
    }

    /**
     * 根据stockCode查找任务详情
     */
    public function findByStockCode(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $stockCode = $request->input('stock_code');
        
        if (empty($stockCode)) {
            return $this->apiError('stockCode不能为空');
        }
        
        $result = $this->userTaskDetailService->findByStockCode($userId, $stockCode);
        return $this->apiSuccess($result);
    }
} 