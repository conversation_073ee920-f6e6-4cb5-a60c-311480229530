<?php

namespace App\Service\User;
use App\Service\BaseService;
use App\Models\TeMu\ProductCategoryWebPageTeMuModel;
use App\Models\TeMu\ProductCategoryTeMuModel;
use App\Models\N11\ProductCategoryN11Model;
use App\Exceptions\MyException;
use App\Models\System\CatRelationSystemModel;
use Illuminate\Support\Facades\DB;

class GoodsCategoryService extends BaseService
{
    
    public function catRelationSet(int $userId,int $is_admin,array $params): array
    {
       if($is_admin != 1){
            throw new MyException('您没有权限进行该操作');
       }

       $link_type = $params['link_type'] ?? 1;
       if($link_type != 1){
            throw new MyException('只能选择系统关联');
       }

       $platform_id = 1;

       $platform_code = $params['platform_code'] ?? 'n11';
       if(empty($platform_code) || !in_array($platform_code,['n11'])){
            throw new MyException('第三方平台目前仅支持n11');
       }
      
       if($platform_code == 'n11'){
            $third_platform_id = 2;
       }


       $cat_platform_id = $params['temu_category_id'] ?? 0;
       if($cat_platform_id == 0){
            throw new MyException('请选择temu分类');
       }

       if($link_type == 1){
            //如果是系统关联 则允许单独设置2种状态 1强制设置为未关联 2强制设置为无需关联
            if(isset($params['relation_status'])){
                $relation_status = $params['relation_status'] ?? 0;
                if($relation_status == 1){
                    $relation_status = 0;
                }elseif($relation_status == 2){
                    $relation_status = 2;
                }
                ProductCategoryTeMuModel::query()
                    ->where('id',$cat_platform_id)
                    ->update(['relation_status' => $relation_status]);

                $cat_relation_system = CatRelationSystemModel::query()
                    ->where('platform_id',$platform_id)
                    ->where('third_platform_id',$third_platform_id)
                    ->where('cat_platform_id',$cat_platform_id)
                    ->first();
                if($cat_relation_system){
                    //清空已有的关联分类
                    $cat_relation_system->cat_third_ids = '';
                    $cat_relation_system->save();
                }

                return [];
            }    
       }

       $categories = $params['categories'] ?? [];
       if(empty($categories)){
            throw new MyException('请选择分类');
       }

       $cat_third_ids = array_column($categories,'id');
       if(empty($cat_third_ids)){
            throw new MyException('请选择分类');
       }
       $cat_third_ids = json_encode($cat_third_ids,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);


       $cat_relation_system = CatRelationSystemModel::query()
            ->where('platform_id',$platform_id)
            ->where('third_platform_id',$third_platform_id)
            ->where('cat_platform_id',$cat_platform_id)
            ->first();
       if($cat_relation_system){
            $cat_relation_system->cat_third_ids = $cat_third_ids;
            $cat_relation_system->save();
       }else{
            $cat_relation_system = new CatRelationSystemModel();
            $cat_relation_system->platform_id = $platform_id;
            $cat_relation_system->third_platform_id = $third_platform_id;
            $cat_relation_system->cat_platform_id = $cat_platform_id;
            $cat_relation_system->cat_third_ids = $cat_third_ids;
            $cat_relation_system->save();
       }
       ProductCategoryTeMuModel::query()
            ->where('id',$cat_platform_id)
            ->update(['relation_status' => 1]);
       return [];
    }   

    public function catRelationList(int $userId,array $params): array
    {
        $cat_platform_id = $params['cat_platform_id'] ?? 0;
        if($cat_platform_id == 0){
            throw new MyException('请选择分类');
        }
        $cat_relation_system = CatRelationSystemModel::query()
            ->where('platform_id',1)
            ->where('third_platform_id',2)
            ->where('cat_platform_id',$cat_platform_id)
            ->select('id','cat_platform_id','cat_third_ids')
            ->first();
        if(!$cat_relation_system){
            return ['list' => []];
        }
        if(empty($cat_relation_system->cat_third_ids)){
            return ['list' => []];
        }
        $cat_third_list = [];

        if(!empty($cat_relation_system->cat_third_ids)){
            $cat_third_ids = json_decode($cat_relation_system->cat_third_ids,true);
            $cat_third_ids = array_map('intval', $cat_third_ids);
            $cat_third_ids = ProductCategoryN11Model::query()
                ->whereIn('id',$cat_third_ids)
                ->get()
                ->toArray();
            
            foreach($cat_third_ids as $cat_third_id){
                $cat_third_list[] = [
                    'id' => $cat_third_id['id'],
                    'name' => $cat_third_id['name'],
                    'name_tl' => $cat_third_id['name_tl'],
                    'path_name' => $cat_third_id['path_name'] ? str_ireplace(',', '->', $cat_third_id['path_name']) : '',
                    'path_name_tl' => $cat_third_id['path_name_tl'] ? str_ireplace(',', '->', $cat_third_id['path_name_tl']) : ''
                ];
            }
        }
        return ['list' => $cat_third_list];
    }


    public function catTemuDetail(array $params): array
    {
        $cat_temu_id = $params['id'] ?? 0;
        if($cat_temu_id == 0){
            throw new MyException('请选择分类');
        }
        $cat_temu = ProductCategoryTeMuModel::query()
            ->where('id',$cat_temu_id)
            ->first();
        if(!$cat_temu){
            throw new MyException('分类不存在');
        }
        $cat_temu = $cat_temu->toArray();
        $cat_temu['path_name'] = $cat_temu['path_name'] ? str_ireplace(',', '->', $cat_temu['path_name']) : '';
        $cat_temu['is_linked'] = $cat_temu['relation_status'];
        return $cat_temu;
    }

    /**
     * 获取Temu主分类列表
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function catTemuMain(): array
    {
        // 获取Temu主分类列表
        $categories = ProductCategoryTeMuModel::query()
            ->where('parent_id', 0)
            ->where('status', 1)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();
        return ['list' => $categories];
    }

    /**
     * 获取Temu分类列表（树形结构）- 优化版本，支持懒加载
     * 
     * @param array $params 查询参数
     * @return array
     */
    public function catTemuList(array $params): array
    {
        // 获取查询参数
        $parentId = $params['parent_id'] ?? 0;
        $level = $params['level'] ?? null;
        $status = $params['status'] ?? 1;
        $keyword = $params['name'] ?? '';
        $pageSize = $params['pageSize'] ?? 50; // 减少默认页面大小
        $page = $params['page'] ?? 1;
        $lazyLoad = $params['lazy_load'] ?? false; // 新增：是否懒加载模式

        // 构建查询
        $query = ProductCategoryTeMuModel::query()
            ->select([
                'id',
                'name',
                'name_en',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'sort_order',
                'status',
                'relation_status',
                'created_at',
                'updated_at'
            ])
            ->where('status', $status)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc');

        // 如果指定了层级，则按层级筛选
        if ($level !== null) {
            $query->where('level', $level);
        }

        // 懒加载模式：只加载指定父级的直接子分类
        if ($lazyLoad) {
            $query->where('parent_id', $parentId);
        } else {
            // 传统模式：如果指定了父级ID，则按父级ID筛选
            if ($parentId > 0) {
                $query->where('parent_id', $parentId);
            } else {
                // 如果没有指定父级ID，默认获取顶级分类（parent_id = 0）
                if(empty($keyword)){
                    $query->where('parent_id', 0);
                }
            }
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%");
            });
        }

        // 获取数据
        $categories = $query->paginate($pageSize, ['*'], 'page', $page);

        // 根据模式选择不同的构建方式
        if ($lazyLoad) {
            // 懒加载模式：构建扁平结构，前端负责树形展示
            $treeData = $this->buildTemuFlatStructure($categories->items());
        } else {
            // 传统模式：构建完整树形结构（仅用于兼容）
            $treeData = $this->buildTemuTreeStructure($categories->items());
        }

        return [
            'list' => $treeData,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $categories->total(),
                'totalPages' => $categories->lastPage(),
                'hasNext' => $page < $categories->lastPage(),
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 获取Temu分类列表（懒加载专用）- 新增方法
     * 
     * @param array $params 查询参数
     * @return array
     */
    public function catTemuListLazy(array $params): array
    {
        $parentId = $params['parent_id'] ?? 0;
        $status = $params['status'] ?? 1;
        $keyword = $params['name'] ?? '';
        $id = $params['id'] ?? 0;
        $relationStatus = $params['relation_status'] ?? null; //正常传值是 0 1 2
        $pageSize = $params['pageSize'] ?? 100; // 懒加载可以适当增加单次加载量
        $page = $params['page'] ?? 1;

        // 判断是否需要只查询叶子分类
        $needOnlyLeaf = !empty($keyword) || $relationStatus !== null;

        // 构建查询
        $query = ProductCategoryTeMuModel::query()
            ->select([
                'id',
                'name',
                'name_en',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'sort_order',
                'status',
                'relation_status',
                'created_at',
                'updated_at'
            ])
            ->where('status', $status)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc');

        // 如果传了id 则只查询一个分类
        if ($id > 0) {
            $query->where('id', $id);
        }

        if ($needOnlyLeaf) {
            // 如果有关键词搜索或关联状态筛选，只查询叶子分类
            $query->where('is_leaf', 1);
            
            // 如果指定了父级ID，增加路径条件
            if ($parentId > 0) {
                $query->where('path', 'like', $parentId . ',%');
            }
        } else {
            // 正常懒加载模式 - 只查询指定父级的直接子分类
            $query->where('parent_id', $parentId);
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $query->where('name', 'like', "%{$keyword}%");
        }

        // 关联状态筛选
        if ($relationStatus !== null) {
            $query->where('relation_status', $relationStatus);
        }



        // 获取数据
        $categories = $query->paginate($pageSize, ['*'], 'page', $page);

        // 构建扁平结构数据
        $flatData = $this->buildTemuFlatStructure($categories->items());

        return [
            'list' => $flatData,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $categories->total(),
                'totalPages' => $categories->lastPage(),
                'hasNext' => $page < $categories->lastPage(),
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 构建Temu分类扁平结构数据（用于懒加载）- 优化版本
     * 
     * @param array $categories 分类数据
     * @return array
     */
    private function buildTemuFlatStructure(array $categories): array
    {
        $result = [];
        $categoryIds = array_map(function($category) {
            return $category->id;
        }, $categories);

        // 批量查询子分类数量，提高性能
        $childrenCounts = [];
        if (!empty($categoryIds)) {
            $childrenCounts = ProductCategoryTeMuModel::query()
                ->whereIn('parent_id', $categoryIds)
                ->where('status', 1)
                ->selectRaw('parent_id, COUNT(*) as children_count')
                ->groupBy('parent_id')
                ->pluck('children_count', 'parent_id')
                ->toArray();
        }

        // 批量查询已关联的N11分类信息
        $linkedN11Categories = $this->getLinkedN11CategoriesBatch($categoryIds);

        foreach ($categories as $category) {
            // 使用批量查询的结果
            $hasChildren = isset($childrenCounts[$category->id]) && $childrenCounts[$category->id] > 0;

            // 获取关联的N11分类信息（只取第一个）
            $linkedN11Info = $linkedN11Categories[$category->id] ?? null;

            $categoryData = [
                'id' => $category->id,
                'name' => $category->name,
                'name_en' => $category->name_en,
                'parent_id' => $category->parent_id,
                'is_leaf' => $category->is_leaf,
                'level' => $category->level,
                'path' => $category->path,
                'path_name' => $category->path_name ? str_ireplace(',', '->', $category->path_name) : '',
                'sort_order' => $category->sort_order,
                'status' => $category->status,
                'status_text' => $category->status == 1 ? '启用' : '禁用',
                'relation_status' => $category->relation_status,
                'created_at' => $category->created_at,
                'updated_at' => $category->updated_at,
                'hasChildren' => $hasChildren, // 前端用于判断是否显示展开按钮
                'children' => [], // 初始为空，懒加载时填充
                'childrenCount' => $childrenCounts[$category->id] ?? 0, // 子分类数量
                'is_linked' => $category->relation_status, // 直接使用relation_status字段
                'linked_n11_category' => $linkedN11Info // 关联的N11分类信息
            ];

            $result[] = $categoryData;
        }

        return $result;
    }

    /**
     * 批量获取已关联的N11分类信息
     * 
     * @param array $categoryIds Temu分类ID数组
     * @return array 关联的N11分类信息数组，键为Temu分类ID
     */
    private function getLinkedN11CategoriesBatch(array $categoryIds): array
    {
        if (empty($categoryIds)) {
            return [];
        }

        // 批量查询关联关系
        $relations = CatRelationSystemModel::query()
            ->where('platform_id', 1)
            ->where('third_platform_id', 2)
            ->whereIn('cat_platform_id', $categoryIds)
            ->whereNotNull('cat_third_ids')
            ->where('cat_third_ids', '!=', '')
            ->select('cat_platform_id', 'cat_third_ids')
            ->get();

        $result = [];
        $allN11Ids = [];

        // 收集所有需要查询的N11分类ID
        foreach ($relations as $relation) {
            if (!empty($relation->cat_third_ids)) {
                $n11Ids = json_decode($relation->cat_third_ids, true);
                if (is_array($n11Ids) && !empty($n11Ids)) {
                    // 只取第一个关联的分类ID
                    $firstN11Id = intval($n11Ids[0]);
                    $allN11Ids[] = $firstN11Id;
                    $result[$relation->cat_platform_id] = $firstN11Id;
                }
            }
        }

        // 批量查询N11分类详细信息
        if (!empty($allN11Ids)) {
            $n11Categories = ProductCategoryN11Model::query()
                ->whereIn('id', $allN11Ids)
                ->select('id', 'name', 'name_tl', 'path_name', 'path_name_tl')
                ->get()
                ->keyBy('id');

            // 构建最终结果
            foreach ($result as $temuCatId => $n11CatId) {
                if (isset($n11Categories[$n11CatId])) {
                    $n11Cat = $n11Categories[$n11CatId];
                    $result[$temuCatId] = [
                        'id' => $n11Cat->id,
                        'name' => $n11Cat->name,
                        'name_tl' => $n11Cat->name_tl,
                        'path_name' => $n11Cat->path_name ? str_ireplace(',', '->', $n11Cat->path_name) : '',
                        'path_name_tl' => $n11Cat->path_name_tl ? str_ireplace(',', '->', $n11Cat->path_name_tl) : ''
                    ];
                } else {
                    unset($result[$temuCatId]);
                }
            }
        }

        return $result;
    }

    /**
     * 获取分类的直接子分类数量
     * 
     * @param int $parentId 父分类ID
     * @return int
     */
    public function getTemuCategoryChildrenCount(int $parentId): int
    {
        return ProductCategoryTeMuModel::query()
            ->where('parent_id', $parentId)
            ->where('status', 1)
            ->count();
    }

    /**
     * 批量获取多个分类的子分类数量
     * 
     * @param array $parentIds 父分类ID数组
     * @return array
     */
    public function getTemuCategoryChildrenCounts(array $parentIds): array
    {
        $counts = ProductCategoryTeMuModel::query()
            ->whereIn('parent_id', $parentIds)
            ->where('status', 1)
            ->selectRaw('parent_id, COUNT(*) as children_count')
            ->groupBy('parent_id')
            ->pluck('children_count', 'parent_id')
            ->toArray();

        // 确保所有请求的ID都有返回值
        $result = [];
        foreach ($parentIds as $parentId) {
            $result[$parentId] = $counts[$parentId] ?? 0;
        }

        return $result;
    }

    /**
     * 获取Temu网页主分类列表
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function catTemuWebpageMain(): array
    {
        // 获取Temu网页主分类列表
        $categories = ProductCategoryWebPageTeMuModel::query()
            ->where('parent_id', 0)
            ->where('status', 1)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();
        return ['list' => $categories];
    }
    
    /**
     * 获取N11网页主分类列表
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function catN11WebpageMain(int $userId): array
    {
        // 获取N11网页主分类列表
        $categories = ProductCategoryN11Model::query()
            ->where('parent_id', 0)
            ->where('status', 1)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();
        return ['list' => $categories];
    }
    
    
    
    
    /**
     * 获取Temu网页分类列表（树形结构）
     * 
     * @param array $params 查询参数
     * @return array
     */
    public function catTemuWebpageList(array $params): array
    {
        // 获取查询参数
        $parentId = $params['parent_id'] ?? 0;
        $level = $params['level'] ?? null;
        $status = $params['status'] ?? 1;
        $keyword = $params['name'] ?? '';
        $pageSize = $params['pageSize'] ?? 1000; // 默认返回大量数据用于树形展示
        $page = $params['page'] ?? 1;

        // 构建查询
        $query = ProductCategoryWebPageTeMuModel::query()
            ->select([
                'id',
                'name',
                'name_en',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'sort_order',
                'status',
                'created_at',
                'updated_at'
            ])
            ->where('status', $status)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc');

        // 如果指定了层级，则按层级筛选
        if ($level !== null) {
            $query->where('level', $level);
        }

        // 如果指定了父级ID，则按父级ID筛选
        if ($parentId > 0) {
            $query->where('parent_id', $parentId);
        } else {
            // 如果没有指定父级ID，默认获取顶级分类（parent_id = 0）
            if(empty($keyword)){
                $query->where('parent_id', 0);
            }
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%");
            });
        }

        // 获取数据
        $categories = $query->paginate($pageSize, ['*'], 'page', $page);

        // 转换为树形结构
        $treeData = $this->buildTreeStructure($categories->items());

        return [
            'list' => $treeData,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $categories->total(),
                'totalPages' => $categories->lastPage(),
                'hasNext' => $page < $categories->lastPage(),
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    public function catN11List(int $userId, array $params): array
    {
        // 获取查询参数
        $parentId = $params['parent_id'] ?? 0;
        $level = $params['level'] ?? null;
        $status = $params['status'] ?? 1;
        $keyword = !empty($params['name']) ? trim($params['name']) : '';
        $pageSize = $params['pageSize'] ?? 1000; // 默认返回大量数据用于树形展示
        $page = $params['page'] ?? 1;

        // 构建查询
        $query = ProductCategoryN11Model::query()
            ->select([
                'id',
                'name',
                'name_en',
                'name_tl',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'path_name_tl',
                'sort_order',
                'status',
                'relation_status',
                'created_at',
                'updated_at'
            ])
            ->where('status', $status)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc');

        // 如果指定了层级，则按层级筛选
        if ($level !== null) {
            $query->where('level', $level);
        }

        // 如果指定了父级ID，则按父级ID筛选
        if ($parentId > 0) {
            $query->where('parent_id', $parentId);
        } else {
            // 如果没有指定父级ID，默认获取顶级分类（parent_id = 0）
            if(empty($keyword)){
                $query->where('parent_id', 0);
            }
        }

        // 关键词搜索 - 支持name和name_tl的模糊查询
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('name_tl', 'like', "%{$keyword}%")
                  ->orWhere('name_en', 'like', "%{$keyword}%");
            });
        }

        // 获取数据
        $categories = $query->paginate($pageSize, ['*'], 'page', $page);

        // 处理数据，格式化路径名称显示
        $processedCategories = collect($categories->items())->map(function($category) {
            $categoryArray = $category->toArray();
            
            // 格式化路径名称显示
            $categoryArray['path_name_display'] = $categoryArray['path_name'] ? str_ireplace(',', '->', $categoryArray['path_name']) : '';
            $categoryArray['path_name_tl_display'] = $categoryArray['path_name_tl'] ? str_ireplace(',', '->', $categoryArray['path_name_tl']) : '';
            
            return $categoryArray;
        })->toArray();

        // 转换为树形结构
        $treeData = $this->buildN11TreeStructure($processedCategories);

        return [
            'list' => $treeData,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $categories->total(),
                'totalPages' => $categories->lastPage(),
                'hasNext' => $page < $categories->lastPage(),
                'hasPrevious' => $page > 1,
            ]
        ];
    }



    /**
     * 获取N11分类详情
     * 
     * @param int $userId 用户ID
     * @param int $is_admin 是否管理员
     * @param array $params 参数
     * @return array
     */
    public function catN11Detail(int $userId, int $is_admin, array $params): array
    {
        $categoryId = $params['id'] ?? 0;
        if ($categoryId == 0) {
            throw new MyException('请选择分类');
        }

        $category = ProductCategoryN11Model::query()
            ->where('id', $categoryId)
            ->first();

        if (!$category) {
            throw new MyException('分类不存在');
        }

        $categoryData = $category->toArray();
        // 保持原始格式，前端负责显示转换
        // $categoryData['path_name'] = $categoryData['path_name'] ? str_ireplace(',', '->', $categoryData['path_name']) : '';
        // $categoryData['path_name_tl'] = $categoryData['path_name_tl'] ? str_ireplace(',', '->', $categoryData['path_name_tl']) : '';

        return $categoryData;
    }

    /**
     * 更新N11分类
     * 
     * @param int $userId 用户ID
     * @param int $is_admin 是否管理员
     * @param array $params 参数
     * @return array
     */
    public function catN11Update(int $userId, int $is_admin, array $params): array
    {
        // 验证管理员权限
        if ($is_admin != 1) {
            throw new MyException('您没有权限进行该操作');
        }

        $categoryId = $params['id'] ?? 0;
        if ($categoryId == 0) {
            throw new MyException('请选择分类');
        }

        $newName = trim($params['name'] ?? '');
        if (empty($newName)) {
            throw new MyException('分类名称不能为空');
        }

        if (mb_strlen($newName) > 200) {
            throw new MyException('分类名称长度不能超过200个字符');
        }

        // 获取当前分类信息
        $category = ProductCategoryN11Model::query()
            ->where('id', $categoryId)
            ->first();

        if (!$category) {
            throw new MyException('分类不存在');
        }

        // 如果名称没有变化，直接返回
        if ($category->name === $newName) {
            return ['message' => '分类名称未发生变化'];
        }

        // 开始事务
        DB::beginTransaction();
        
        try {
            // 高效更新算法
            $this->updateCategoryNameWithPathName($category, $newName);
            
            DB::commit();
            
            return ['message' => '分类更新成功'];
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw new MyException('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 高效更新分类名称和相关path_name
     * 
     * @param object $category 当前分类对象
     * @param string $newName 新名称
     * @return void
     */
    private function updateCategoryNameWithPathName($category, string $newName): void
    {
        $categoryId = $category->id;
        $oldName = $category->name;
        $isLeaf = $category->is_leaf;
        $path = $category->path;
        $pathName = $category->path_name;

        // 1. 更新当前分类的name和path_name
        $pathParts = explode(',', $pathName);
        $pathIds = explode(',', $path);
        
        // 找到当前分类在path中的位置
        $currentIndex = array_search($categoryId, array_map('intval', $pathIds));
        if ($currentIndex !== false) {
            $pathParts[$currentIndex] = $newName;
            $newPathName = implode(',', $pathParts);
            
            // 更新当前分类
            ProductCategoryN11Model::query()
                ->where('id', $categoryId)
                ->update([
                    'name' => $newName,
                    'path_name' => $newPathName
                ]);
        }

        // 2. 如果不是叶子节点，需要更新所有子孙分类的path_name
        if (!$isLeaf) {
            // 批量获取所有需要更新的子孙分类
            $childCategories = ProductCategoryN11Model::query()
                ->where('path', 'like', $path . ',%')
                ->select(['id', 'path', 'path_name'])
                ->get();

            if ($childCategories->isNotEmpty()) {
                // 批量更新数据
                $updateData = [];
                
                foreach ($childCategories as $child) {
                    $childPathParts = explode(',', $child->path_name);
                    $childPathIds = explode(',', $child->path);
                    
                    // 找到当前分类在子分类path中的位置
                    $targetIndex = array_search($categoryId, array_map('intval', $childPathIds));
                    if ($targetIndex !== false && isset($childPathParts[$targetIndex])) {
                        $childPathParts[$targetIndex] = $newName;
                        $newChildPathName = implode(',', $childPathParts);
                        
                        $updateData[] = [
                            'id' => $child->id,
                            'path_name' => $newChildPathName
                        ];
                    }
                }

                // 使用批量更新提高效率
                if (!empty($updateData)) {
                    foreach ($updateData as $data) {
                        ProductCategoryN11Model::query()
                            ->where('id', $data['id'])
                            ->update(['path_name' => $data['path_name']]);
                    }
                }
            }
        }
    }

    /**
     * 构建树形结构数据
     * 
     * @param array $categories 分类数据
     * @return array
     */
    private function buildTreeStructure(array $categories): array
    {
        $result = [];

        foreach ($categories as $category) {
            // 转换为数组格式
            $categoryData = [
                'id' => $category->id,
                'name' => $category->name,
                'name_en' => $category->name_en,
                'parent_id' => $category->parent_id,
                'is_leaf' => $category->is_leaf,
                'level' => $category->level,
                'path' => $category->path,
                'path_name' => $category->path_name ? str_ireplace(',', '->', $category->path_name) : '',
                'sort_order' => $category->sort_order,
                'status' => $category->status,
                'status_text' => $category->status == 1 ? '启用' : '禁用',
                'created_at' => $category->created_at,
                'updated_at' => $category->updated_at,
                'children' => [], // Element Plus 树形表格需要的 children 字段
                'is_linked' => 0 // 网页分类默认为false
            ];

            // 如果不是叶子节点，获取其子分类
            if (!$category->is_leaf) {
                $children = ProductCategoryWebPageTeMuModel::query()
                    ->select([
                        'id',
                        'name',
                        'name_en',
                        'parent_id',
                        'is_leaf',
                        'level',
                        'path',
                        'path_name',
                        'sort_order',
                        'status',
                        'relation_status',
                        'created_at',
                        'updated_at'
                    ])
                    ->where('parent_id', $category->id)
                    ->where('status', 1)
                    ->orderBy('sort_order', 'desc')
                    ->orderBy('id', 'asc')
                    ->get();

                // 递归构建子分类
                $categoryData['children'] = $this->buildChildrenStructure($children->toArray());
            }

            $result[] = $categoryData;
        }

        return $result;
    }

    /**
     * 递归构建子分类结构
     * 
     * @param array $children 子分类数据
     * @return array
     */
    private function buildChildrenStructure(array $children): array
    {
        $result = [];

        foreach ($children as $child) {
            $childData = [
                'id' => $child['id'],
                'name' => $child['name'],
                'name_en' => $child['name_en'],
                'parent_id' => $child['parent_id'],
                'is_leaf' => $child['is_leaf'],
                'level' => $child['level'],
                'path' => $child['path'],
                'path_name' => $child['path_name'] ? str_ireplace(',', '->', $child['path_name']) : '',
                'sort_order' => $child['sort_order'],
                'status' => $child['status'],
                'status_text' => $child['status'] == 1 ? '启用' : '禁用',
                'created_at' => is_string($child['created_at']) ? $child['created_at'] : $child['created_at'],
                'updated_at' => is_string($child['updated_at']) ? $child['updated_at'] : $child['updated_at'],
                'children' => [],
                'is_linked' => 0 // 网页分类默认为false
            ];

            // 如果不是叶子节点，继续获取其子分类
            if (!$child['is_leaf']) {
                $grandChildren = ProductCategoryWebPageTeMuModel::query()
                    ->select([
                        'id',
                        'name',
                        'name_en',
                        'parent_id',
                        'is_leaf',
                        'level',
                        'path',
                        'path_name',
                        'sort_order',
                        'status',
                        'relation_status',
                        'created_at',
                        'updated_at'
                    ])
                    ->where('parent_id', $child['id'])
                    ->where('status', 1)
                    ->orderBy('sort_order', 'desc')
                    ->orderBy('id', 'asc')
                    ->get();

                // 递归构建孙分类
                $childData['children'] = $this->buildChildrenStructure($grandChildren->toArray());
            }

            $result[] = $childData;
        }

        return $result;
    }

    /**
     * 获取指定分类的所有子分类（扁平结构）
     * 
     * @param int $parentId 父分类ID
     * @return array
     */
    public function getCategoryChildren(int $parentId): array
    {
        return ProductCategoryWebPageTeMuModel::query()
            ->select([
                'id',
                'name',
                'name_en',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'sort_order',
                'status'
            ])
            ->where('parent_id', $parentId)
            ->where('status', 1)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 根据路径获取分类层级结构
     * 
     * @param string $path 分类路径，如 "1,2,3"
     * @return array
     */
    public function getCategoryByPath(string $path): array
    {
        $pathIds = explode(',', $path);
        $categories = [];

        foreach ($pathIds as $id) {
            $category = ProductCategoryWebPageTeMuModel::query()
                ->select([
                    'id',
                    'name',
                    'name_en',
                    'parent_id',
                    'is_leaf',
                    'level',
                    'path',
                    'path_name',
                    'sort_order',
                    'status'
                ])
                ->where('id', $id)
                ->where('status', 1)
                ->first();

            if ($category) {
                $categories[] = $category->toArray();
            }
        }

        return $categories;
    }

    /**
     * 构建N11分类树形结构数据
     * 
     * @param array $categories 分类数据
     * @return array
     */
    private function buildN11TreeStructure(array $categories): array
    {
        $result = [];

        foreach ($categories as $category) {
            // 判断是数组还是对象
            $isArray = is_array($category);
            
            // 转换为数组格式
            $categoryData = [
                'id' => $isArray ? $category['id'] : $category->id,
                'name' => $isArray ? $category['name'] : $category->name,
                'name_en' => $isArray ? $category['name_en'] : $category->name_en,
                'name_tl' => $isArray ? $category['name_tl'] : $category->name_tl,
                'parent_id' => $isArray ? $category['parent_id'] : $category->parent_id,
                'is_leaf' => $isArray ? $category['is_leaf'] : $category->is_leaf,
                'level' => $isArray ? $category['level'] : $category->level,
                'path' => $isArray ? $category['path'] : $category->path,
                'path_name' => $isArray ? $category['path_name'] : $category->path_name,
                'path_name_tl' => $isArray ? $category['path_name_tl'] : $category->path_name_tl,
                'path_name_display' => $isArray ? $category['path_name_display'] : '',
                'path_name_tl_display' => $isArray ? $category['path_name_tl_display'] : '',
                'linked_temu_categories' => $isArray ? ($category['linked_temu_categories'] ?? []) : [],
                'has_temu_link' => $isArray ? ($category['has_temu_link'] ?? false) : false,
                'sort_order' => $isArray ? $category['sort_order'] : $category->sort_order,
                'status' => $isArray ? $category['status'] : $category->status,
                'relation_status' => $isArray ? $category['relation_status'] : $category->relation_status,
                'status_text' => ($isArray ? $category['status'] : $category->status) == 1 ? '启用' : '禁用',
                'created_at' => $isArray ? $category['created_at'] : $category->created_at,
                'updated_at' => $isArray ? $category['updated_at'] : $category->updated_at,
                'children' => [] // Element Plus 树形表格需要的 children 字段
            ];

            // 如果不是叶子节点，获取其子分类
            $categoryId = $isArray ? $category['id'] : $category->id;
            $isLeaf = $isArray ? $category['is_leaf'] : $category->is_leaf;
            
            if (!$isLeaf) {
                $children = ProductCategoryN11Model::query()
                    ->select([
                        'id',
                        'name',
                        'name_en',
                        'name_tl',
                        'parent_id',
                        'is_leaf',
                        'level',
                        'path',
                        'path_name',
                        'path_name_tl',
                        'sort_order',
                        'status',
                        'relation_status',
                        'created_at',
                        'updated_at'
                    ])
                    ->where('parent_id', $categoryId)
                    ->where('status', 1)
                    ->orderBy('sort_order', 'desc')
                    ->orderBy('id', 'asc')
                    ->get();

                // 递归构建子分类
                $categoryData['children'] = $this->buildN11ChildrenStructure($children->toArray());
            }

            $result[] = $categoryData;
        }

        return $result;
    }

    /**
     * 递归构建N11子分类结构
     * 
     * @param array $children 子分类数据
     * @return array
     */
    private function buildN11ChildrenStructure(array $children): array
    {
        $result = [];

        foreach ($children as $child) {
            $childData = [
                'id' => $child['id'],
                'name' => $child['name'],
                'name_en' => $child['name_en'],
                'name_tl' => $child['name_tl'],
                'parent_id' => $child['parent_id'],
                'is_leaf' => $child['is_leaf'],
                'level' => $child['level'],
                'path' => $child['path'],
                'path_name' => $child['path_name'] ? str_ireplace(',', '->', $child['path_name']) : '',
                'path_name_tl' => $child['path_name_tl'] ? str_ireplace(',', '->', $child['path_name_tl']) : '',
                'sort_order' => $child['sort_order'],
                'status' => $child['status'],
                'status_text' => $child['status'] == 1 ? '启用' : '禁用',
                'created_at' => is_string($child['created_at']) ? $child['created_at'] : $child['created_at'],
                'updated_at' => is_string($child['updated_at']) ? $child['updated_at'] : $child['updated_at'],
                'children' => [],
                'is_linked' => $child['relation_status'] // 直接使用relation_status字段
            ];

            // 如果不是叶子节点，继续获取其子分类
            if (!$child['is_leaf']) {
                $grandChildren = ProductCategoryN11Model::query()
                    ->select([
                        'id',
                        'name',
                        'name_en',
                        'name_tl',
                        'parent_id',
                        'is_leaf',
                        'level',
                        'path',
                        'path_name',
                        'path_name_tl',
                        'sort_order',
                        'status',
                        'relation_status',
                        'created_at',
                        'updated_at'
                    ])
                    ->where('parent_id', $child['id'])
                    ->where('status', 1)
                    ->orderBy('sort_order', 'desc')
                    ->orderBy('id', 'asc')
                    ->get();

                // 递归构建孙分类
                $childData['children'] = $this->buildN11ChildrenStructure($grandChildren->toArray());
            }

            $result[] = $childData;
        }

        return $result;
    }

    /**
     * 获取N11指定分类的所有子分类（扁平结构）
     * 
     * @param int $parentId 父分类ID
     * @return array
     */
    public function getN11CategoryChildren(int $parentId): array
    {
        return ProductCategoryN11Model::query()
            ->select([
                'id',
                'name',
                'name_en',
                'name_tl',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'path_name_tl',
                'sort_order',
                'status'
            ])
            ->where('parent_id', $parentId)
            ->where('status', 1)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 根据路径获取N11分类层级结构
     * 
     * @param string $path 分类路径，如 "1,2,3"
     * @return array
     */
    public function getN11CategoryByPath(string $path): array
    {
        $pathIds = explode(',', $path);
        $categories = [];

        foreach ($pathIds as $id) {
            $category = ProductCategoryN11Model::query()
                ->select([
                    'id',
                    'name',
                    'name_en',
                    'name_tl',
                    'parent_id',
                    'is_leaf',
                    'level',
                    'path',
                    'path_name',
                    'path_name_tl',
                    'sort_order',
                    'status'
                ])
                ->where('id', $id)
                ->where('status', 1)
                ->first();

            if ($category) {
                $categories[] = $category->toArray();
            }
        }

        return $categories;
    }

    /**
     * 构建Temu分类树形结构数据
     * 
     * @param array $categories 分类数据
     * @return array
     */
    private function buildTemuTreeStructure(array $categories): array
    {
        $result = [];

        foreach ($categories as $category) {
            // 转换为数组格式
            $categoryData = [
                'id' => $category->id,
                'name' => $category->name,
                'name_en' => $category->name_en,
                'parent_id' => $category->parent_id,
                'is_leaf' => $category->is_leaf,
                'level' => $category->level,
                'path' => $category->path,
                'path_name' => $category->path_name ? str_ireplace(',', '->', $category->path_name) : '',
                'sort_order' => $category->sort_order,
                'status' => $category->status,
                'status_text' => $category->status == 1 ? '启用' : '禁用',
                'relation_status' => $category->relation_status,
                'created_at' => $category->created_at,
                'updated_at' => $category->updated_at,
                'children' => [], // Element Plus 树形表格需要的 children 字段
                'is_linked' => $category->relation_status // 直接使用relation_status字段
            ];

            // 如果不是叶子节点，获取其子分类
            if (!$category->is_leaf) {
                $children = ProductCategoryTeMuModel::query()
                    ->select([
                        'id',
                        'name',
                        'name_en',
                        'parent_id',
                        'is_leaf',
                        'level',
                        'path',
                        'path_name',
                        'sort_order',
                        'status',
                        'relation_status',
                        'created_at',
                        'updated_at'
                    ])
                    ->where('parent_id', $category->id)
                    ->where('status', 1)
                    ->orderBy('sort_order', 'desc')
                    ->orderBy('id', 'asc')
                    ->get();

                // 递归构建子分类
                $categoryData['children'] = $this->buildTemuChildrenStructure($children->toArray());
            }

            $result[] = $categoryData;
        }

        return $result;
    }

    /**
     * 递归构建Temu子分类结构
     * 
     * @param array $children 子分类数据
     * @return array
     */
    private function buildTemuChildrenStructure(array $children): array
    {
        $result = [];

        foreach ($children as $child) {
            $childData = [
                'id' => $child['id'],
                'name' => $child['name'],
                'name_en' => $child['name_en'],
                'parent_id' => $child['parent_id'],
                'is_leaf' => $child['is_leaf'],
                'level' => $child['level'],
                'path' => $child['path'],
                'path_name' => $child['path_name'] ? str_ireplace(',', '->', $child['path_name']) : '',
                'sort_order' => $child['sort_order'],
                'status' => $child['status'],
                'status_text' => $child['status'] == 1 ? '启用' : '禁用',
                'relation_status' => $child['relation_status'],
                'created_at' => is_string($child['created_at']) ? $child['created_at'] : $child['created_at'],
                'updated_at' => is_string($child['updated_at']) ? $child['updated_at'] : $child['updated_at'],
                'children' => [],
                'is_linked' => $child['relation_status'] // 直接使用relation_status字段
            ];

            // 如果不是叶子节点，继续获取其子分类
            if (!$child['is_leaf']) {
                $grandChildren = ProductCategoryTeMuModel::query()
                    ->select([
                        'id',
                        'name',
                        'name_en',
                        'parent_id',
                        'is_leaf',
                        'level',
                        'path',
                        'path_name',
                        'sort_order',
                        'status',
                        'relation_status',
                        'created_at',
                        'updated_at'
                    ])
                    ->where('parent_id', $child['id'])
                    ->where('status', 1)
                    ->orderBy('sort_order', 'desc')
                    ->orderBy('id', 'asc')
                    ->get();

                // 递归构建孙分类
                $childData['children'] = $this->buildTemuChildrenStructure($grandChildren->toArray());
            }

            $result[] = $childData;
        }

        return $result;
    }

    /**
     * 获取Temu指定分类的所有子分类（扁平结构）
     * 
     * @param int $parentId 父分类ID
     * @return array
     */
    public function getTemuCategoryChildren(int $parentId): array
    {
        return ProductCategoryTeMuModel::query()
            ->select([
                'id',
                'name',
                'name_en',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'sort_order',
                'status'
            ])
            ->where('parent_id', $parentId)
            ->where('status', 1)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 根据路径获取Temu分类层级结构
     * 
     * @param string $path 分类路径，如 "1,2,3"
     * @return array
     */
    public function getTemuCategoryByPath(string $path): array
    {
        $pathIds = explode(',', $path);
        $categories = [];

        foreach ($pathIds as $id) {
            $category = ProductCategoryTeMuModel::query()
                ->select([
                    'id',
                    'name',
                    'name_en',
                    'parent_id',
                    'is_leaf',
                    'level',
                    'path',
                    'path_name',
                    'sort_order',
                    'status'
                ])
                ->where('id', $id)
                ->where('status', 1)
                ->first();

            if ($category) {
                $categories[] = $category->toArray();
            }
        }

        return $categories;
    }

    /**
     * 获取Temu分类列表（虚拟表格专用）- 扁平化数据
     * 
     * @param array $params 查询参数
     * @return array
     */
    public function catTemuListFlat(array $params): array
    {
        $level = $params['level'] ?? null;
        $status = $params['status'] ?? 1;
        $keyword = $params['name'] ?? '';
        $pageSize = $params['pageSize'] ?? 1000;
        $page = $params['page'] ?? 1;
        $parentId = $params['parent_id'] ?? null;

        // 构建查询
        $query = ProductCategoryTeMuModel::query()
            ->select([
                'id',
                'name',
                'name_en',
                'parent_id',
                'is_leaf',
                'level',
                'path',
                'path_name',
                'sort_order',
                'status',
                'relation_status',
                'created_at',
                'updated_at'
            ])
            ->where('status', $status);

        // 层级筛选
        if ($level !== null) {
            $query->where('level', $level);
        }

        // 父级筛选
        if ($parentId !== null) {
            $query->where('parent_id', $parentId);
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('name_en', 'like', "%{$keyword}%")
                  ->orWhere('path_name', 'like', "%{$keyword}%");
            });
        }

        $query->orderBy('level', 'asc')
              ->orderBy('sort_order', 'desc')
              ->orderBy('id', 'asc');

        // 获取数据
        $categories = $query->paginate($pageSize, ['*'], 'page', $page);

        // 批量获取关联的N11分类信息
        $categoryIds = array_map(function($category) {
            return $category->id;
        }, $categories->items());
        $linkedN11Categories = $this->getLinkedN11CategoriesBatch($categoryIds);

        // 构建扁平结构数据（适用于虚拟表格）
        $flatData = [];
        foreach ($categories->items() as $category) {
            // 获取关联的N11分类信息（只取第一个）
            $linkedN11Info = $linkedN11Categories[$category->id] ?? null;

            $categoryData = [
                'id' => $category->id,
                'name' => $category->name,
                'name_en' => $category->name_en,
                'parent_id' => $category->parent_id,
                'is_leaf' => $category->is_leaf,
                'level' => $category->level,
                'path' => $category->path,
                'path_name' => $category->path_name ? str_ireplace(',', '->', $category->path_name) : '',
                'sort_order' => $category->sort_order,
                'status' => $category->status,
                'status_text' => $category->status == 1 ? '启用' : '禁用',
                'relation_status' => $category->relation_status,
                'created_at' => $category->created_at,
                'updated_at' => $category->updated_at,
                'indent_level' => $category->level, // 用于虚拟表格的缩进显示
                'is_linked' => $category->relation_status, // 直接使用relation_status字段
                'linked_n11_category' => $linkedN11Info // 关联的N11分类信息
            ];

            $flatData[] = $categoryData;
        }

        return [
            'list' => $flatData,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $categories->total(),
                'totalPages' => $categories->lastPage(),
                'hasNext' => $page < $categories->lastPage(),
                'hasPrevious' => $page > 1,
            ]
        ];
    }
}
