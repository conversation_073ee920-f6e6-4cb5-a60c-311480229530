/**
 * 店铺管理API接口
 * 参考api.ts的结构，通过background页面发送请求
 */
import { sendRequestViaBackground } from './api'

// 店铺接口类型定义
export interface Store {
  id?: number
  account_type: number
  account_name: string
  brand: string
  price_rate: number
  price_add: number
  price_subtract: number
  shipment_template: string
  quantity: number
  vat_rate: number
  preparing_day: number
  app_key: string
  app_secret: string
  status: number  // 新增状态字段：1正常 0禁用
  integrator_name: string  // 新增集成商名称字段
  create_time?: string
  update_time?: string
}

export interface StoreListParams {
  page: number
  pageSize: number
  account_type?: number
  account_name?: string
  brand?: string
  status?: number  // 新增状态搜索参数：1正常 0禁用
  integrator_name?: string  // 新增集成商名称搜索参数
}

export interface StoreListResponse {
  list: Store[]
  pagination: {
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean,
    currentPage: number,
    pageSize: number
  }
}

export interface BatchUpdateParams {
  ids: number[]
  price_rate?: number
  price_add?: number
  price_subtract?: number
  status?: number  // 新增批量状态设置
  shipment_template?: string
  quantity?: number
  vat_rate?: number
  preparing_day?: number
  integrator_name?: string  // 新增批量集成商名称设置
}

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 获取店铺列表
 * @param params 查询参数
 * @returns 店铺列表响应
 */
export const getStoreList = async (params: StoreListParams): Promise<StoreListResponse> => {
  const url = await getApiUrl('apiStoreListUrl');
  
  return sendRequestViaBackground({
    funName: 'getStoreList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 创建店铺
 * @param store 店铺信息
 * @returns 创建结果
 */
export const createStore = async (store: Omit<Store, 'id' | 'createTime' | 'updateTime'>): Promise<Store> => {
  const url = await getApiUrl('apiStoreCreateUrl');
  
  return sendRequestViaBackground({
    funName: 'createStore',
    url,
    method: 'post',
    data: store,
    auth: true
  });
};

/**
 * 更新店铺
 * @param id 店铺ID
 * @param store 店铺信息
 * @returns 更新结果
 */
export const updateStore = async (store: Partial<Store>): Promise<Store> => {
  const baseUrl = await getApiUrl('apiStoreUpdateUrl');
  const url = `${baseUrl}`;
  
  return sendRequestViaBackground({
    funName: 'updateStore',
    url,
    method: 'post',
    data: store,
    auth: true
  });
};

/**
 * 删除店铺
 * @param id 店铺ID
 * @returns 删除结果
 */
export const deleteStore = async (id: number): Promise<void> => {
  const baseUrl = await getApiUrl('apiStoreDeleteUrl');
  const url = `${baseUrl}/${id}`;
  
  return sendRequestViaBackground({
    funName: 'deleteStore',
    url,
    method: 'delete',
    auth: true
  });
};

/**
 * 批量更新店铺价格上调
 * @param params 批量更新参数
 * @returns 更新结果
 */
export const batchUpdateStores = async (params: BatchUpdateParams): Promise<void> => {
  const url = await getApiUrl('apiStoreBatchUpdateUrl');
  
  return sendRequestViaBackground({
    funName: 'batchUpdateStores',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 获取店铺详情
 * @param id 店铺ID
 * @returns 店铺详情
 */
export const getStoreDetail = async (id: number): Promise<Store> => {
  const baseUrl = await getApiUrl('apiStoreDetailUrl');
  const url = `${baseUrl}/${id}`;
  
  return sendRequestViaBackground({
    funName: 'getStoreDetail',
    url,
    method: 'get',
    auth: true
  });
};