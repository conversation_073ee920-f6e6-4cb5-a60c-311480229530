<?php

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserTaskService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class UserTaskController extends Controller
{
    protected UserTaskService $userTaskService;

    public function __construct(UserTaskService $userTaskService)
    {
        $this->userTaskService = $userTaskService;
        parent::__construct();
    }

    public function addTask(Request $request){

        $user = $request->attributes->get('user');
        $user_id = $user['id'];
        $params = $request->all();
        
        try {
            $result = $this->userTaskService->addTask($user_id,$params);
            return $this->apiSuccess($result, '任务创建成功');
        } catch (\Exception $e) {
            return $this->apiError($e->getMessage());
        }
    }
    
    /**
     * 获取任务列表（分页）
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only(['page', 'pageSize', 'task_over', 'day_start', 'day_end']);
        $result = $this->userTaskService->getTaskList($userId, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 开启任务
     */
    public function startTask(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        // 验证任务ID参数
        $taskId = $request->input('task_id');
        if (!$taskId || !is_numeric($taskId)) {
            return $this->apiError('任务ID参数无效');
        }
        
        $result = $this->userTaskService->startTask($userId, (int)$taskId);
        return $this->apiSuccess($result, '任务开启成功');
    }

    public function updateTask(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        $params = $request->all();
        $result = $this->userTaskService->updateTask($userId, $params);
        return $this->apiSuccess($result);
    }



}