<?php
declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\MyException;
use App\Utils\AdSecurity;
use Closure;
use Illuminate\Http\Request;

class DecryptRequest
{
    /**
     * 处理加密请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $params = $request->all();

        // 获取当前请求方法和应用URL的主机名
        /* $isPostRequest = $request->isMethod('post');
        $appUrl = config('app.url');
        $appEnv = config('app.env');
        $appHost = parse_url($appUrl, PHP_URL_HOST);
        $currentHost = $request->getHost(); */

        // 传递参数时才检查 get请求可无参数 需要强制验证加密参数格式
        if(count($params)>0){
            // 检查是否是加密请求（2个参数 并且是固定KEY形式）
            if ( count($params) === 2 && isset($params['encrypted_data']) && isset($params['sign']) ) {
                // 获取解密密钥
                $appKey = config('ad.appkey');
                // 使用 AdSecurity 解密
                $decryptedData = AdSecurity::decryptFromClient($params, $appKey);
                if ($decryptedData !== false) {
                    // 清除原始加密参数
                    $request->replace([]);
                    unset($decryptedData['system_sign']);
                    // 将解密后的参数合并到请求中
                    $request->merge($decryptedData);
                }
            }else{
                throw new MyException('请求错误');
            }
        }

        return $next($request);
    }
}
