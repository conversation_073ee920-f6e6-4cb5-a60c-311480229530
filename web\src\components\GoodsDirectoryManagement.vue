<template>
  <div class="directory-management">
    <div class="page-header">
      <h2>商品目录</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新增目录
        </el-button>
        <el-button type="warning" @click="showBatchDialog" :disabled="selectedDirectories.length === 0">
          <el-icon><Setting /></el-icon>
          批量设置
        </el-button>
      </div>
    </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="目录名称">
          <el-input v-model="searchForm.name" placeholder="请输入目录名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table 
        :data="directoryList" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" :index="getIndex" />
        <el-table-column label="目录名称" prop="name" min-width="200" show-overflow-tooltip />
        <el-table-column label="描述" prop="description" min-width="250" show-overflow-tooltip />
        <el-table-column label="商品数量" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.goods_count }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ row.status_name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="created_at" width="160" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewGoods(row)">
              查看商品
            </el-button>
            <el-button type="success" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(row)"
              :disabled="row.goods_count > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑目录对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="目录名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入目录名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入目录描述（可选）" 
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量设置对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量设置"
      width="500px"
    >
      <el-form
        ref="batchFormRef"
        :model="batchForm"
        :rules="batchFormRules"
        label-width="100px"
      >
        <el-form-item label="状态">
          <el-select v-model="batchForm.status" placeholder="请选择状态" clearable style="width: 100%">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <div class="batch-info">
          <p>将对以下 {{ selectedDirectories.length }} 个目录进行批量设置：</p>
          <ul class="selected-directories">
            <li v-for="directory in selectedDirectories" :key="directory.id">
              {{ directory.name }}
            </li>
          </ul>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchSubmit" :loading="batchSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Plus, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  getDirectoryList,
  createDirectory,
  updateDirectory,
  deleteDirectory,
  batchUpdateDirectory,
  type Directory,
  type DirectoryListParams,
  type DirectoryFormData,
  type DirectoryBatchUpdateData
} from '../utils/directoryApi'

// 接口定义
interface SearchForm {
  name: string
  status: number | null | undefined
  start_date?: string
  end_date?: string
}

interface BatchForm {
  status: number | null | undefined
}

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const batchSubmitting = ref(false)
const directoryList = ref<Directory[]>([])
const selectedDirectories = ref<Directory[]>([])

const router = useRouter()

// 搜索表单
const searchForm = reactive<SearchForm>({
  name: '',
  status: null
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 对话框状态
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const isEdit = ref(false)

// 表单数据
const form = reactive<DirectoryFormData>({
  name: '',
  description: '',
  sort_order: 0,
  status: 1
})

const batchForm = reactive<BatchForm>({
  status: undefined
})

const formRef = ref()
const batchFormRef = ref()

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入目录名称', trigger: 'blur' },
    { min: 1, max: 100, message: '目录名称长度在 1 到 100 个字符', trigger: 'blur' }
  ]
}

const batchFormRules = {}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑目录' : '新增目录')

// 获取序号
const getIndex = (index: number) => {
  return (pagination.currentPage - 1) * pagination.pageSize + index + 1
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'danger'
}

// 处理日期范围变化
const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.start_date = dates[0]
    searchForm.end_date = dates[1]
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

// 加载目录列表
const loadDirectoryList = async () => {
  loading.value = true
  try {
    const params: DirectoryListParams = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: searchForm.name || undefined,
      status: (searchForm.status === null || searchForm.status === undefined) ? undefined : searchForm.status,
      start_date: searchForm.start_date,
      end_date: searchForm.end_date
    }
    
    const response = await getDirectoryList(params)
    directoryList.value = response.list
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
  } catch (error) {
    console.error('获取目录列表失败:', error)
    ElMessage.error('获取目录列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadDirectoryList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: null,
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = null
  pagination.currentPage = 1
  loadDirectoryList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadDirectoryList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadDirectoryList()
}

// 选择变化
const handleSelectionChange = (selection: Directory[]) => {
  selectedDirectories.value = selection
}

// 显示新增对话框
const showCreateDialog = () => {
  isEdit.value = false
  Object.assign(form, {
    name: '',
    description: '',
    sort_order: 0,
    status: 1
  })
  dialogVisible.value = true
}

// 显示批量设置对话框
const showBatchDialog = () => {
  batchForm.status = undefined
  batchDialogVisible.value = true
}

// 编辑
const handleEdit = (row: Directory) => {
  isEdit.value = true
  Object.assign(form, {
    id: row.id,
    name: row.name,
    description: row.description,
    sort_order: row.sort_order,
    status: row.status
  })
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: Directory) => {
  if (row.goods_count > 0) {
    ElMessage.warning(`该目录下还有 ${row.goods_count} 个商品，无法删除`)
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除目录"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteDirectory(row.id)
    ElMessage.success('目录删除成功')
    loadDirectoryList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除目录失败:', error)
      ElMessage.error('删除目录失败')
    }
  }
}

// 查看商品
const handleViewGoods = (row: Directory) => {
  // 跳转到商品列表页面，并传递目录ID
  router.push({
    name: 'GoodsListByDirectory',
    params: { directoryId: row.id.toString() },
    query: { directoryName: row.name }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    if (isEdit.value) {
      await updateDirectory(form)
      ElMessage.success('目录更新成功')
    } else {
      await createDirectory(form)
      ElMessage.success('目录创建成功')
    }

    dialogVisible.value = false
    loadDirectoryList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 批量设置提交
const handleBatchSubmit = async () => {
  try {
    await batchFormRef.value.validate()

    // 检查是否选择了状态
    if (batchForm.status === undefined || batchForm.status === null) {
      ElMessage.warning('请选择要设置的状态')
      return
    }

    batchSubmitting.value = true

    const ids = selectedDirectories.value.map(directory => directory.id)
    const updateData: DirectoryBatchUpdateData = {
      ids,
      status: batchForm.status
    }

    await batchUpdateDirectory(updateData)

    const statusText = batchForm.status === 1 ? '启用' : '禁用'
    const message = `已成功设置 ${selectedDirectories.value.length} 个目录的状态为${statusText}。`

    ElNotification({
      title: '批量设置成功',
      message,
      type: 'success'
    })

    batchDialogVisible.value = false
    selectedDirectories.value = []
    loadDirectoryList()
  } catch (error) {
    console.error('批量设置失败:', error)
    ElMessage.error('批量设置失败')
  } finally {
    batchSubmitting.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadDirectoryList()
})
</script>

<style scoped>
.directory-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.batch-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.batch-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-weight: bold;
}

.selected-directories {
  margin: 0;
  padding-left: 20px;
  max-height: 150px;
  overflow-y: auto;
}

.selected-directories li {
  color: #409eff;
  margin-bottom: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style> 