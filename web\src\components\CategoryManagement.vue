<template>
  <div class="category-management">
    <div class="page-header">
      <h2>TeMu商品分类</h2>
      <div class="header-actions">
        <el-switch
          v-model="useVirtualTable"
          active-text="虚拟表格"
          inactive-text="懒加载表格"
          @change="handleTableModeChange"
        />
      </div>
    </div>

    <!-- 搜索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="商品分类">
          <el-select 
            v-model="searchForm.parent_id" 
            placeholder="请选择主分类" 
            clearable
            style="width: 200px"
          >
            <el-option 
              v-for="category in mainCategories" 
              :key="category.id" 
              :label="category.name" 
              :value="category.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称">
          <el-input 
            v-model="searchForm.name" 
            placeholder="请输入分类名称" 
            clearable 
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="关联状态">
          <el-select 
            v-model="searchForm.relation_status" 
            placeholder="请选择关联状态" 
            clearable
            style="width: 150px"
          >
            <el-option label="否" :value="0" />
            <el-option label="是" :value="1" />
            <el-option label="无需关联" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 懒加载树形表格 -->
    <div v-if="!useVirtualTable" class="table-section">
      <el-table 
        :data="categoryList" 
        v-loading="loading"
        stripe
        border
        row-key="id"
        lazy
        :load="loadChildren"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
        ref="tableRef"
        @expand-change="handleTreeExpandChange"
      >
        <el-table-column label="ID" prop="id" width="120" align="right" />
        <el-table-column label="分类名称" prop="name" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="category-name" :class="{ 
              'level-1': row.level === 1, 
              'level-2': row.level === 2,
              'level-3': row.level === 3,
              'linked': row.is_linked && row.is_leaf == 1
            }">
              {{ row.name }}
              <template v-if="row.parent_id !== 0">
                <span class="path-display">【{{ row.path_name.replace(/,/g, '->') }}】</span>
              </template>
              <template v-if="row.hasChildren && row.childrenCount">
                <el-tag size="small" type="info" class="children-count">
                  {{ row.childrenCount }}个子分类
                </el-tag>
              </template>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="关联N11分类" min-width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <template v-if="row.is_leaf == 0">
              <span class="not-applicable">----</span>
            </template>
            <template v-else-if="row.linked_n11_category">
              <div class="linked-category-info">
                <div class="category-names">
                  <div class="category-name">{{ row.linked_n11_category.name }}</div>
                  <div class="category-name-tl" v-if="row.linked_n11_category.name_tl">
                    {{ row.linked_n11_category.name_tl }}
                  </div>
                </div>
                <div class="category-paths">
                  <div class="category-path" v-if="row.linked_n11_category.path_name">
                    <el-tag size="small" type="info">{{ row.linked_n11_category.path_name }}</el-tag>
                  </div>
                  <div class="category-path-tl" v-if="row.linked_n11_category.path_name_tl">
                    <el-tag size="small" type="success">{{ row.linked_n11_category.path_name_tl }}</el-tag>
                    <el-button 
                      size="small" 
                      type="text" 
                      @click="copyToClipboard(row.linked_n11_category.path_name_tl)"
                      class="copy-btn"
                      title="复制N11路径"
                    >
                      <el-icon><DocumentCopy /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
            <template v-else>
              <span class="no-link">未关联</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="层级" prop="level" width="80" align="center" />
        <el-table-column label="关联平台" width="100" align="center">
          <template #default="{ row }">
            <template v-if="row.is_leaf == 0">
              <span class="not-applicable">----</span>
            </template>
            <template v-else>
              <el-tag 
                :type="row.is_linked === 1 ? 'success' : (row.is_linked === 0 ? 'info' : 'warning')" 
                size="small"
              >
                {{ 
                  row.is_linked === 1 ? '是' : 
                  (row.is_linked === 0 ? '否' : '无需关联') 
                }}
              </el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updated_at" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.is_leaf == 1" 
              type="primary" 
              size="small" 
              @click="handleLinkThirdParty(row)"
            >
              关联第三方平台分类
            </el-button>
            <span v-else>----</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 虚拟化表格 -->
    <div v-else class="virtual-table-section">
      <div class="virtual-table-container" style="height: 600px;">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :columns="virtualTableColumns"
              :data="flatCategoryList"
              :width="width"
              :height="height"
              :row-height="50"
              fixed
              v-loading="loading"
            />
          </template>
        </el-auto-resizer>
      </div>
      
      <!-- 虚拟表格分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="virtualPagination.currentPage"
          v-model:page-size="virtualPagination.pageSize"
          :page-sizes="[1000, 2000, 5000]"
          :total="virtualPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleVirtualSizeChange"
          @current-change="handleVirtualCurrentChange"
        />
      </div>
    </div>

    <!-- 分类详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="分类详情"
      width="600px"
    >
      <div class="category-detail" v-if="currentCategory">
        <div class="detail-row">
          <span class="detail-label">分类ID:</span>
          <span class="detail-value">{{ currentCategory.id }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">分类名称:</span>
          <span class="detail-value">{{ currentCategory.name }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">英文名称:</span>
          <span class="detail-value">{{ currentCategory.name_en || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">父级分类:</span>
          <span class="detail-value">{{ currentCategory.parent_id === 0 ? '顶级分类' : currentCategory.parent_id }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">分类层级:</span>
          <span class="detail-value">第{{ currentCategory.level }}级</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">分类路径:</span>
          <span class="detail-value">{{ currentCategory.path_name }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">是否叶子节点:</span>
          <span class="detail-value">
            <el-tag :type="currentCategory.is_leaf ? 'success' : 'info'">
              {{ currentCategory.is_leaf ? '是' : '否' }}
            </el-tag>
          </span>
        </div>
        <div class="detail-row">
          <span class="detail-label">状态:</span>
          <span class="detail-value">
            <el-tag :type="currentCategory.status === 1 ? 'success' : 'danger'">
              {{ currentCategory.status_text }}
            </el-tag>
          </span>
        </div>
        <div class="detail-row">
          <span class="detail-label">创建时间:</span>
          <span class="detail-value">{{ currentCategory.created_at }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">更新时间:</span>
          <span class="detail-value">{{ currentCategory.updated_at }}</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 关联第三方平台分类对话框 -->
    <el-dialog
      v-model="linkDialogVisible"
      title="关联第三方平台分类"
      width="80%"
      :close-on-click-modal="false"
      class="link-third-party-dialog"
    >
      <ThirdPartyLinkage
        v-if="linkDialogVisible && currentLinkCategory"
        :temu-category="currentLinkCategory"
        :platforms="availablePlatforms"
        @close="linkDialogVisible = false"
        @success="handleLinkSuccess"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h, nextTick } from 'vue'
import type { VNode } from 'vue'
import { ElMessage, ElNotification, ElTag, ElButton } from 'element-plus'
import type { ElTable } from 'element-plus'
import { Search, Refresh, DocumentCopy } from '@element-plus/icons-vue'
import { 
  getCategoryListLazy, 
  getMainCategoryList,
  getCategoryListFlat,
  getCategoryChildrenCounts,
  getCategoryDetail,
  type Category as CategoryType,
  type CategoryLazyListParams,
  type CategoryFlatListParams
} from '../utils/categoryApi'
import ThirdPartyLinkage from './ThirdPartyLinkage.vue'

// 接口定义
interface SearchForm {
  parent_id: number | null | undefined
  name: string
  relation_status: number | null | undefined
}

interface Platform {
  id: number
  name: string
  code: string
  apiUrl: string
}

// 响应式数据
const loading = ref(false)
const useVirtualTable = ref(false) // 表格模式切换
const categoryList = ref<CategoryType[]>([])
const flatCategoryList = ref<CategoryType[]>([])
const mainCategories = ref<CategoryType[]>([])
const currentCategory = ref<CategoryType | null>(null)
const currentLinkCategory = ref<CategoryType | null>(null)
const tableRef = ref<InstanceType<typeof ElTable>>()

// 搜索表单
const searchForm = reactive<SearchForm>({
  parent_id: null,
  name: '',
  relation_status: null
})

// 懒加载表格分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 50, // 优化：减少单页数据量
  total: 0,
  totalPages: 0,
  hasNext: false,
  hasPrevious: false
})

// 虚拟表格分页数据
const virtualPagination = reactive({
  currentPage: 1,
  pageSize: 2000, // 虚拟表格支持更大的数据量
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const linkDialogVisible = ref(false)

// 可用平台列表
const availablePlatforms = ref<Platform[]>([
  {
    id: 1,
    name: 'N11',
    code: 'n11',
    apiUrl: 'apiCatN11ListUrl'
  }
])

// 虚拟表格列配置
const virtualTableColumns = computed(() => [
  {
    key: 'id',
    title: 'ID',
    dataKey: 'id',
    width: 120,
    align: 'right'
  },
  {
    key: 'name',
    title: '分类名称',
    dataKey: 'name',
    width: 300,
    cellRenderer: ({ rowData }: any): VNode => {
      const indent = '　'.repeat(rowData.level - 1)
      return h('span', { 
        class: ['category-name', `level-${rowData.level}`, { linked: rowData.is_linked && rowData.is_leaf == 1 }]
      }, [
        indent + rowData.name,
        rowData.parent_id !== 0 ? h('span', { class: 'path-display' }, `【${rowData.path_name.replace(/,/g, '->')}】`) : null
      ])
    }
  },
  {
    key: 'linked_n11_category',
    title: '关联N11分类',
    dataKey: 'linked_n11_category',
    width: 300,
    cellRenderer: ({ rowData }: any): VNode => {
      if (rowData.is_leaf == 0) {
        return h('span', { class: 'not-applicable' }, '----')
      }
      if (rowData.linked_n11_category) {
        return h('div', { class: 'linked-category-info' }, [
          h('div', { class: 'category-names' }, [
            h('div', { class: 'category-name' }, rowData.linked_n11_category.name),
            rowData.linked_n11_category.name_tl ? 
              h('div', { class: 'category-name-tl' }, rowData.linked_n11_category.name_tl) : null
          ]),
          h('div', { class: 'category-paths' }, [
            rowData.linked_n11_category.path_name ? 
              h('div', { class: 'category-path' }, [
                h(ElTag, { size: 'small', type: 'info' }, () => rowData.linked_n11_category.path_name)
              ]) : null,
            rowData.linked_n11_category.path_name_tl ? 
              h('div', { class: 'category-path-tl' }, [
                h(ElTag, { size: 'small', type: 'success' }, () => rowData.linked_n11_category.path_name_tl),
                h(ElButton, {
                  size: 'small',
                  type: 'text',
                  class: 'copy-btn',
                  title: '复制N11路径',
                  onClick: () => handleCopyToClipboard(rowData.linked_n11_category.path_name_tl)
                }, () => h(DocumentCopy))
              ]) : null
          ])
        ])
      }
      return h('span', { class: 'no-link' }, '未关联')
    }
  },
  {
    key: 'level',
    title: '层级',
    dataKey: 'level',
    width: 80,
    align: 'center'
  },
  {
    key: 'is_linked',
    title: '关联平台',
    dataKey: 'is_linked',
    width: 100,
    align: 'center',
    cellRenderer: ({ rowData }: any): VNode => {
      if (rowData.is_leaf == 0) {
        return h('span', { class: 'not-applicable' }, '----')
      }
      return h(ElTag, {
        type: rowData.is_linked === 1 ? 'success' : (rowData.is_linked === 0 ? 'info' : 'warning'),
        size: 'small'
      }, () => 
        rowData.is_linked === 1 ? '是' : 
        (rowData.is_linked === 0 ? '否' : '无需关联')
      )
    }
  },
  {
    key: 'updated_at',
    title: '更新时间',
    dataKey: 'updated_at',
    width: 160
  },
  {
    key: 'actions',
    title: '操作',
    dataKey: 'actions',
    width: 200,
    cellRenderer: ({ rowData }: any): VNode => {
      if (rowData.is_leaf == 1) {
        return h(ElButton, {
          type: 'primary',
          size: 'small',
          onClick: () => handleLinkThirdParty(rowData)
        }, () => '关联第三方平台分类')
      }
      return h('span', '----')
    }
  }
])

// 加载主分类列表
const loadMainCategories = async () => {
  try {
    const response = await getMainCategoryList()
    console.log('获取主分类列表成功:', response)
    mainCategories.value = response.list.map(item => ({
      ...item,
      hasChildren: !item.is_leaf
    }))
  } catch (error) {
    console.error('获取主分类列表失败:', error)
  }
}

// 懒加载分类列表（顶级分类）
const loadCategoryList = async () => {
  loading.value = true
  try {
    const params: CategoryLazyListParams = {
      parent_id: searchForm.parent_id || 0,
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...(searchForm.name && { name: searchForm.name }),
      ...(searchForm.relation_status !== null && searchForm.relation_status !== undefined && { relation_status: searchForm.relation_status })
    }
    
    const response = await getCategoryListLazy(params)
    console.log('获取懒加载分类列表成功:', response)
    
    // 处理数据，添加hasChildren字段
    const processedList = response.list.map(item => ({
      ...item,
      hasChildren: !item.is_leaf,
      children: [] // 初始化children数组
    }))
    
    categoryList.value = processedList
    pagination.total = response.pagination.total
    pagination.totalPages = response.pagination.totalPages
    pagination.hasNext = response.pagination.hasNext
    pagination.hasPrevious = response.pagination.hasPrevious
    
    // 批量获取子分类数量
    await loadChildrenCounts(processedList)
    
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 批量获取子分类数量
const loadChildrenCounts = async (categories: CategoryType[]) => {
  try {
    const parentIds = categories.filter(c => c.hasChildren).map(c => c.id)
    if (parentIds.length > 0) {
      const response = await getCategoryChildrenCounts(parentIds)
      console.log('获取子分类数量响应:', response)
      
      // 处理返回的数据格式：将对象转换为Map
      let countsMap: Map<number, number>
      if (response.counts && typeof response.counts === 'object') {
        // 如果counts是对象格式 {id: count}
        if (Array.isArray(response.counts)) {
          // 如果是数组格式 [{parent_id: id, count: count}]
          countsMap = new Map(response.counts.map(c => [c.parent_id, c.count]))
        } else {
          // 如果是对象格式 {id: count}
          countsMap = new Map(Object.entries(response.counts).map(([id, count]) => [parseInt(id), count as number]))
        }
      } else {
        countsMap = new Map()
      }
      
      categories.forEach(category => {
        if (category.hasChildren) {
          category.childrenCount = countsMap.get(category.id) || 0
        }
      })
    }
  } catch (error) {
    console.error('获取子分类数量失败:', error)
  }
}

// 懒加载子分类
const loadChildren = async (row: CategoryType, treeNode: any, resolve: Function) => {
  try {
    const response = await getCategoryListLazy({
      parent_id: row.id,
      pageSize: 100 // 子分类一次性加载更多
    })
    
    const children = response.list.map(item => ({
      ...item,
      hasChildren: !item.is_leaf,
      children: []
    }))
    
    // 批量获取子分类数量
    await loadChildrenCounts(children)
    
    resolve(children)
  } catch (error) {
    console.error('加载子分类失败:', error)
    ElMessage.error('加载子分类失败')
    resolve([])
  }
}

// 加载扁平化分类列表（虚拟表格）
const loadFlatCategoryList = async () => {
  loading.value = true
  try {
    const params: CategoryFlatListParams = {
      page: virtualPagination.currentPage,
      pageSize: virtualPagination.pageSize,
      ...(searchForm.parent_id && { parent_id: searchForm.parent_id }),
      ...(searchForm.name && { name: searchForm.name }),
      ...(searchForm.relation_status !== null && searchForm.relation_status !== undefined && { relation_status: searchForm.relation_status })
    }
    
    const response = await getCategoryListFlat(params)
    console.log('获取扁平化分类列表成功:', response)
    
    flatCategoryList.value = response.list
    virtualPagination.total = response.pagination.total
    
  } catch (error) {
    console.error('获取扁平化分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 表格模式切换
const handleTableModeChange = (useVirtual: boolean) => {
  if (useVirtual) {
    loadFlatCategoryList()
  } else {
    loadCategoryList()
  }
}

// 搜索
const handleSearch = () => {
  if (useVirtualTable.value) {
    virtualPagination.currentPage = 1
    loadFlatCategoryList()
  } else {
    pagination.currentPage = 1
    loadCategoryList()
  }
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    parent_id: null,
    name: '',
    relation_status: null
  })
  
  if (useVirtualTable.value) {
    virtualPagination.currentPage = 1
    loadFlatCategoryList()
  } else {
    pagination.currentPage = 1
    loadCategoryList()
  }
}

// 懒加载表格分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadCategoryList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadCategoryList()
}

// 虚拟表格分页处理
const handleVirtualSizeChange = (size: number) => {
  virtualPagination.pageSize = size
  virtualPagination.currentPage = 1
  loadFlatCategoryList()
}

const handleVirtualCurrentChange = (page: number) => {
  virtualPagination.currentPage = page
  loadFlatCategoryList()
}

// 处理树形表格展开变化
const handleTreeExpandChange = (row: CategoryType, expanded: boolean) => {
  console.log('树形表格展开变化:', { id: row.id, name: row.name, expanded })
}

// 关联第三方平台分类
const handleLinkThirdParty = (row: CategoryType) => {
  currentLinkCategory.value = row
  linkDialogVisible.value = true
}

// 关联成功回调
const handleLinkSuccess = async (updatedCategoryInfo?: CategoryType) => {
  linkDialogVisible.value = false
  
  // 如果是虚拟表格模式，直接重新加载数据
  if (useVirtualTable.value) {
    loadFlatCategoryList()
    return
  }
  
  // 懒加载模式下的处理策略
  if (currentLinkCategory.value) {
    console.log('处理分类关联成功回调:', {
      categoryId: currentLinkCategory.value.id,
      categoryName: currentLinkCategory.value.name
    })
    
    try {
      // 获取更新后的分类信息
      const updatedCategory = await getCategoryUpdatedInfo(currentLinkCategory.value.id)
      
      if (updatedCategory) {
        // 尝试智能更新：先在分类列表中查找，再在表格数据中查找
        const updatedInList = updateCategoryInList(categoryList.value, currentLinkCategory.value.id, updatedCategory)
        const updatedInTable = updateCategoryInExpandedChildren(currentLinkCategory.value.id, updatedCategory)
        
        // 如果在任一位置找到并更新了分类
        if (updatedInList || updatedInTable) {
          console.log('✅ 成功更新分类关联状态:', {
            categoryId: currentLinkCategory.value.id,
            updatedInList,
            updatedInTable
          })
          
          // 强制触发表格重新渲染
          if (tableRef.value) {
            await nextTick()
            tableRef.value.doLayout()
          }
          
          ElMessage.success('分类关联状态已更新')
        } else {
          // 如果无法找到目标分类，说明可能在其他页面或未展开的子分类中
          // 对于懒加载模式，最可靠的方案是重新加载当前页面数据
          console.log('⚠️ 未找到目标分类，重新加载当前页面数据')
          await loadCategoryList()
          ElMessage.success('分类关联状态已更新，页面已刷新')
        }
      } else {
        // 如果获取更新信息失败，重新加载整个列表
        console.warn('获取更新后的分类信息失败，重新加载列表')
        await loadCategoryList()
        ElMessage.success('分类关联操作完成，页面已刷新')
      }
    } catch (error) {
      console.error('更新分类状态失败:', error)
      // 发生错误时重新加载整个列表
      await loadCategoryList()
      ElMessage.success('分类关联操作完成，页面已刷新')
    }
  } else {
    // 如果没有当前操作的分类，则重新加载整个列表
    await loadCategoryList()
    ElMessage.success('分类关联操作完成，页面已刷新')
  }
}

// 获取分类的更新信息
const getCategoryUpdatedInfo = async (categoryId: number): Promise<CategoryType | null> => {
  try {
    // 通过单独的API获取分类的最新信息
    const response = await getCategoryDetail(categoryId)
    
    return response || null
  } catch (error) {
    console.error('获取分类更新信息失败:', error)
    return null
  }
}

// 递归更新分类列表中的指定分类
const updateCategoryInList = (list: CategoryType[], targetId: number, updatedData: Partial<CategoryType>): boolean => {
  console.log('在分类列表中查找目标分类:', { listLength: list.length, targetId })
  
  for (let i = 0; i < list.length; i++) {
    if (list[i].id === targetId) {
      // 找到目标分类，更新其数据
      Object.assign(list[i], updatedData)
      console.log('✅ 在分类列表中更新了分类:', { id: targetId, index: i })
      return true
    }
    
    // 如果有子分类，递归查找更新
    if (list[i].children && list[i].children!.length > 0) {
      const found = updateCategoryInList(list[i].children as CategoryType[], targetId, updatedData)
      if (found) return true
    }
  }
  
  console.log('❌ 在分类列表中未找到目标分类:', { targetId })
  return false
}

// 更新已展开的子分类中的分类状态
const updateCategoryInExpandedChildren = (targetId: number, updatedData: Partial<CategoryType>): boolean => {
  // 这个方法用于处理在懒加载表格中已经展开的子分类
  // 由于Element Plus的懒加载表格特性，我们需要通过表格实例来更新数据
  if (tableRef.value) {
    try {
      // 获取表格的所有数据（包括已展开的子分类）
      const tableData = (tableRef.value as any).store?.states?.data?.value || []
      console.log('检查表格数据:', { tableDataLength: tableData.length, targetId })
      
      // 递归查找并更新目标分类
      const updateInTableData = (data: any[], path = ''): boolean => {   
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          
          if (item.id === targetId) {
            Object.assign(item, updatedData)
            console.log('✅ 在表格数据中更新了分类:', { id: targetId, path: path || `[${i}]` })
            return true
          }
          if (item.children && item.children.length > 0) {
            if (updateInTableData(item.children, `${path || `[${i}]`}.children`)) return true
          }
        }
        return false
      }
      
      const updated = updateInTableData(tableData)
      
      // 如果找到并更新了数据，还需要检查表格内部的懒加载数据
      if (!updated) {
        // 尝试从表格的懒加载缓存中查找和更新
        const treeStore = (tableRef.value as any).store?.states?.lazyTreeNodeMap?.value
        console.log('检查懒加载缓存:', { hasTreeStore: !!treeStore, targetId })
        
        if (treeStore) {
          for (const [key, children] of Object.entries(treeStore)) {
            if (Array.isArray(children)) {
              const foundInLazy = updateInTableData(children as any[], `lazy[${key}]`)
              if (foundInLazy) {
                console.log('✅ 在懒加载缓存中更新了分类:', { id: targetId, parentKey: key })
                return true
              }
            }
          }
        }
      }
      
      if (!updated) {
        console.log('❌ 在表格数据中未找到目标分类:', { targetId })
      }
      
      return updated
    } catch (error) {
      console.error('更新表格中的分类数据失败:', error)
      return false
    }
  }
  return false
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    // 降级方案：使用传统的复制方法
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 处理复制操作（用于虚拟表格）
const handleCopyToClipboard = (text: string) => {
  copyToClipboard(text)
}

// 查看详情
const handleViewDetails = (row: CategoryType) => {
  currentCategory.value = row
  detailDialogVisible.value = true
}

// 组件挂载时加载数据
onMounted(() => {
  loadMainCategories()
  loadCategoryList()
})
</script>

<style scoped>
.category-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.header-actions {
  display: none;  /*flex */
  align-items: center;
  gap: 10px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section, .virtual-table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.virtual-table-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 20px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.category-name {
  font-weight: 500;
}

.category-name.level-1 {
  color: #409eff;
  font-weight: 600;
}

.category-name.level-2 {
  color: #67c23a;
}

.category-name.level-3 {
  color: #e6a23c;
}

.category-name.linked {
  background: linear-gradient(45deg, #67c23a, #409eff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.path-display {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.children-count {
  margin-left: 8px;
}

.not-applicable {
  color: #c0c4cc;
  font-style: italic;
}

.no-link {
  color: #909399;
  font-size: 12px;
}

.linked-category-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.category-names {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.linked-category-info .category-name {
  font-weight: 500;
  color: #303133;
  font-size: 13px;
}

.linked-category-info .category-name-tl {
  font-weight: 400;
  color: #606266;
  font-size: 12px;
  font-style: italic;
}

.category-paths {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.linked-category-info .category-path {
  font-size: 11px;
}

.linked-category-info .category-path-tl {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-btn {
  padding: 2px 4px !important;
  min-height: auto !important;
  color: #409eff !important;
}

.copy-btn:hover {
  background-color: #ecf5ff !important;
}

.category-detail {
  padding: 20px 0;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.detail-label {
  width: 120px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: #303133;
}

.link-third-party-dialog {
  min-height: 600px;
}

/* 虚拟表格样式优化 */
:deep(.el-table-v2__header-row) {
  background-color: #f5f7fa;
}

:deep(.el-table-v2__row:hover) {
  background-color: #f5f7fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-management {
    padding: 10px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style> 