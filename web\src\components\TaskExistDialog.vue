<template>
  <el-dialog
    v-model="visible"
    title=""
    width="500px"
    :close-on-click-modal="false"
    center
    class="task-exist-dialog"
  >
    <div class="exist-content">
      <!-- 状态图标 -->
      <div class="status-icon">
        <div class="icon-wrapper warning">
          <el-icon size="48"><WarningFilled /></el-icon>
        </div>
      </div>
      
      <!-- 标题 -->
      <div class="dialog-title">
        <h3>商品已存在</h3>
        <p>检测到以下商品已存在于店铺中</p>
      </div>
      
      <!-- 商品信息卡片 -->
      <div class="goods-card">
        <div class="goods-image">
          <el-image
            :src="goodsInfo.thumb_url"
            fit="cover"
            class="product-image"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
        
        <div class="goods-details">
          <div class="goods-name">
            <span class="label">商品名称：</span>
            <span class="value">{{ goodsInfo.goods_name }}</span>
          </div>
          
          <div class="store-name">
            <span class="label">店铺名称：</span>
            <span class="value">{{ goodsInfo.store_name }}</span>
          </div>
          
          <div class="sku-info" v-if="goodsInfo.goods_sku_name">
            <span class="label">SKU规格：</span>
            <span class="value">{{ goodsInfo.goods_sku_name }}</span>
          </div>
          
          <div class="price-info" v-if="goodsInfo.price">
            <span class="label">价格：</span>
            <span class="value price">{{ goodsInfo.price }} {{ goodsInfo.currentcy || '' }}</span>
          </div>
        </div>
      </div>
      
      <!-- 存在状态提示 -->
      <div class="exist-status">
        <el-tag type="warning" size="large" effect="light">
          <el-icon><InfoFilled /></el-icon>
          <span>该商品已存在</span>
        </el-tag>
      </div>
      
      <!-- 操作提示 -->
      <div class="action-hint">
        <p>您可以选择跳过此商品继续处理下一个任务，或者停止当前操作。</p>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" size="large">
          <el-icon><Close /></el-icon>
          停止任务
        </el-button>
        <el-button type="primary" @click="handleSkip" size="large">
          <el-icon><Right /></el-icon>
          跳过，继续下一个
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { WarningFilled, InfoFilled, Picture, Close, Right } from '@element-plus/icons-vue'

interface GoodsInfo {
  goods_name: string
  thumb_url: string
  store_name: string
  goods_sku_name?: string
  price?: number
  currentcy?: string
}

interface Props {
  visible: boolean
  goodsInfo: GoodsInfo
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  skip: []
  cancel: []
}>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleSkip = () => {
  emit('skip')
  visible.value = false
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}
</script>

<style scoped>
.task-exist-dialog {
  --warning-color: #e6a23c;
  --warning-light: #fdf6ec;
  --warning-lighter: #fef7e6;
}

:deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

:deep(.el-dialog__header) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 30px;
}

:deep(.el-dialog__footer) {
  padding: 20px 30px 30px;
  border-top: 1px solid #f0f0f0;
}

.exist-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-icon {
  margin-bottom: 20px;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-wrapper.warning {
  background: var(--warning-lighter);
  color: var(--warning-color);
  border: 3px solid var(--warning-light);
}

.icon-wrapper::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--warning-lighter);
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.dialog-title {
  text-align: center;
  margin-bottom: 25px;
}

.dialog-title h3 {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.dialog-title p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.goods-card {
  width: 100%;
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
  display: flex;
  gap: 15px;
}

.goods-image {
  flex-shrink: 0;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 24px;
}

.goods-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.goods-details > div {
  display: flex;
  align-items: flex-start;
  line-height: 1.4;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.value.price {
  color: var(--warning-color);
  font-weight: 600;
  font-size: 16px;
}

.exist-status {
  margin-bottom: 20px;
}

.exist-status .el-tag {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 20px;
}

.exist-status .el-icon {
  margin-right: 6px;
}

.action-hint {
  text-align: center;
  margin-bottom: 10px;
}

.action-hint p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.dialog-footer .el-button {
  min-width: 140px;
  border-radius: 8px;
  font-weight: 500;
}

.dialog-footer .el-icon {
  margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 600px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 15px 20px 20px;
  }
  
  .goods-card {
    flex-direction: column;
    text-align: center;
  }
  
  .goods-details > div {
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }
  
  .label {
    min-width: auto;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
  
  .dialog-footer .el-button {
    width: 100%;
  }
}
</style> 