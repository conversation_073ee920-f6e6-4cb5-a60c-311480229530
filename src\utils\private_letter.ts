export const clickElement = (index)=>{
    if (index >= click_list.length) {
      // Send message to content page after all elements have been clicked
      // Replace with your actual message sending code
      console.log("All elements have been clicked");
      return;
    }
  
    chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', { 
      nodeId: secondGroupNodeId, 
      selector: `tr:nth-child(${click_list[index]}) td:nth-child(5) span:first-child a:first-child`
    }, function(result:any) {
      //点击获取到的元素
      let click_nodeId = result.nodeIds[0];
      chrome.debugger.sendCommand({tabId: tabId}, 'DOM.getOuterHTML', {nodeId: click_nodeId}, function(result:any) { 
        let html = result.outerHTML; 
        let text = html.replace(/<[^>]*>/g, '');  
        console.log("当前点击的元素"+text);  
        chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
          chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', { 
            objectId: object.object.objectId, 
            functionDeclaration: 'function() { this.click(); }'
          }, function() { 
            // 在这里返回响应 
            console.log("已点击元素");
  
            // Wait for 5 seconds
            setTimeout(function() {
              // Click on the element with class 'leads-drawer-mask-show'
              chrome.debugger.sendCommand({tabId: tabId}, 'DOM.querySelectorAll', {
                nodeId: root.root.nodeId,
                selector: '.leads-drawer-mask-show'
              }, function(result:any) {
                if (result.nodeIds.length > 0) {
                  let click_nodeId = result.nodeIds[0];
                  chrome.debugger.sendCommand({tabId: tabId}, 'DOM.resolveNode', {nodeId: click_nodeId}, function(object:any) {
                    chrome.debugger.sendCommand({tabId: tabId}, 'Runtime.callFunctionOn', {
                      objectId: object.object.objectId,
                      functionDeclaration: 'function() { this.click(); }'
                    }, function() {
                      // Click on the next element
                      clickElement(index + 1);
                    });
                  });
                }
              });
            }, 5000);
          });
        });
      });
    });
  }