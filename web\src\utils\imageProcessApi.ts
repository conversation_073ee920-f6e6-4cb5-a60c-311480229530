import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

/**
 * 获取需要处理图片的商品ID列表
 * @param directoryId 目录ID
 * @returns Promise<any>
 */
export const getNeedImageProcessGoods = async (directoryId: number): Promise<any> => {
  const url = await getApiUrl('apiGoodsNeedImageProcessUrl');
  console.log('获取需要处理图片的商品列表URL:', url)
  
  return sendRequestViaBackground({
    funName: 'getNeedImageProcessGoods',
    url,
    method: 'get',
    params: { directory_id: directoryId },
    auth: true
  });
};

/**
 * 处理商品图片本地化
 * @param goodsId 商品ID
 * @param processStep 处理步骤
 * @returns Promise<any>
 */
export const processGoodsImages = async (goodsId: number, processStep: number): Promise<any> => {
  const url = await getApiUrl('apiUserProcessGoodsImagesUrl');
  console.log('处理商品图片本地化URL:', url)
  
  return sendRequestViaBackground({
    funName: 'processGoodsImages',
    url,
    method: 'post',
    data: {
      goods_id: goodsId,
      process_step: processStep
    },
    auth: true
  });
}; 