<?php
declare(strict_types=1);

namespace App\Service\System;

use App\Models\System\PayConfig;
use App\Models\System\SystemLog;
use App\Service\BaseService;

class SystemLogService extends BaseService
{
    protected SystemLog $systemLogModel;
    public function __construct(SystemLog $systemLog)
    {
        $this->systemLogModel = $systemLog;
        parent::__construct();
    }

    public function add(array $data=[])
    {
        $data = $this->systemLogModel->schemaFieldsFromArray($data);
        return $this->systemLogModel::query()->create($data);
    }

}
