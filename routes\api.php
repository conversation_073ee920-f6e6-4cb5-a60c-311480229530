<?php
declare(strict_types=1);

use App\Http\Controllers\Api\V1\Wap;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::namespace('Api')->group(function () {
    Route::get('captcha',[Wap\Common\CaptchaController::class,'getCaptcha']);
    Route::post('login/loginSms',[Wap\User\UserController::class,'loginSms']);
    Route::post('user/register',[Wap\User\UserController::class,'register']);
    
    Route::get('index',[Wap\Index\IndexController::class,'index']);
    Route::get('game/list',[Wap\Index\IndexController::class,'gameList']);
    Route::get('app/download',[Wap\Index\IndexController::class,'appDownload']);
    Route::get('game/download',[Wap\Index\IndexController::class,'gameDownload']);
    Route::match(['get', 'post'], 'Config/getProtocol',[Wap\Index\IndexController::class,'getProtocol']);

    Route::post('Withdrawal/adRewardInfoByUser',[Wap\User\UserController::class,'adRewardInfoByUser']);//查看用户广告奖励数据

});

Route::namespace('Api')->middleware('userCanEmptyLogin')->group(function(){
    Route::get('services',[Wap\Index\IndexController::class,'getServices']);
});

Route::namespace('Api')->middleware('userLogin')->group(function(){



    Route::post('rewardGromore',[Wap\User\UserController::class,'rewardGromore']);
    Route::post('rewardGdt',[Wap\User\UserController::class,'rewardGdt']);
    Route::post('rewardKs',[Wap\User\UserController::class,'rewardKs']);

    Route::get('moneyAndAn/data',[Wap\Index\IndexController::class,'moneyAndAn']);//没用到
    Route::get('Config/getIcon',[Wap\Index\IndexController::class,'getIcon']);
    Route::get('Config/getOdds',[Wap\Index\IndexController::class,'getOdds']);
    Route::get('Withdrawal/selectMoney',[Wap\Index\IndexController::class,'selectMoney']);//获取提现金额数组 含URL信息
    Route::get('Withdrawal/WithdrawalList',[Wap\User\UserController::class,'withdrawList']);//获取提现列表
    Route::match(['get', 'post'], 'RedPacket/MoneyList',[Wap\User\UserController::class,'moneyList']);//获取红包金额列表
    Route::post('Withdrawal/WithdrawalAdd',[Wap\User\UserController::class,'withdrawAdd']);//提现申请
    Route::get('User/getUser',[Wap\User\UserController::class,'getUser']);
    Route::post('RedPacket/canAd',[Wap\User\UserController::class,'canAd']);//是否可以查看广告
    Route::post('RedPacket/PacketDescribe',[Wap\User\UserController::class,'packetDescribe']);//红包描述
    Route::post('RedPacket/collectPacketOne',[Wap\User\UserController::class,'collectPacketOne']);//领取红包
    Route::post('Uplaode/uplaodImage',[Wap\Common\UploadController::class,'upload']);
    Route::post('Withdrawal/bindCard',[Wap\User\UserController::class,'bindCard']);//用户绑定微信收款码
    Route::get('Withdrawal/info',[Wap\User\UserController::class,'withdrawInfo']);//用户微信收款二维码及提现说明

    Route::get('Withdrawal/adReward',[Wap\User\UserController::class,'adReward']);//用户广告奖励数据
    Route::post('Withdrawal/adRewardAdd',[Wap\User\UserController::class,'adRewardAdd']);//用户提交广告奖励数据
    Route::get('Withdrawal/adRewardList',[Wap\User\UserController::class,'adRewardList']);//用户广告奖励列表

    Route::post('temu/goodsAdd',[Wap\TeMu\GoodsController::class,'goodsAdd']);
    
    // 店铺管理路由组
    Route::prefix('store')->group(function(){
        Route::get('list',[Wap\User\StoreController::class,'list']);//获取店铺列表
        Route::post('create',[Wap\User\StoreController::class,'create']);//创建店铺
        Route::post('update',[Wap\User\StoreController::class,'update']);//更新店铺
        Route::post('batch-update',[Wap\User\StoreController::class,'batchUpdate']);//批量更新店铺
        Route::post('delete',[Wap\User\StoreController::class,'delete']);//删除店铺
        Route::get('detail',[Wap\User\StoreController::class,'detail']);//获取店铺详情
    });
    
    // 商品管理路由组
    Route::prefix('goods')->group(function(){
        Route::get('list',[Wap\User\GoodsController::class,'list']);//获取商品列表
        Route::post('batch-update',[Wap\User\GoodsController::class,'batchUpdate']);//批量更新商品
        Route::get('need-image-process',[Wap\User\GoodsController::class,'getNeedImageProcessGoods']);//获取需要处理图片的商品ID列表
        Route::get('statistics',[Wap\User\GoodsController::class,'getGoodsStatistics']);//获取商品统计信息
        /* Route::post('create',[Wap\User\GoodsController::class,'create']);//创建商品
        Route::post('update',[Wap\User\GoodsController::class,'update']);//更新商品
        Route::post('delete',[Wap\User\GoodsController::class,'delete']);//删除商品
        Route::get('detail',[Wap\User\GoodsController::class,'detail']);//获取商品详情 */
    });

    // 商品目录管理路由组
    Route::prefix('goods_directory')->group(function(){
        Route::get('list',[Wap\User\UserGoodsDirectoryController::class,'list']);//获取目录列表
        Route::post('create',[Wap\User\UserGoodsDirectoryController::class,'create']);//创建目录
        Route::post('update',[Wap\User\UserGoodsDirectoryController::class,'update']);//更新目录
        Route::post('delete',[Wap\User\UserGoodsDirectoryController::class,'delete']);//删除目录
        Route::post('batch-update',[Wap\User\UserGoodsDirectoryController::class,'batchUpdate']);//批量更新目录
        Route::get('detail',[Wap\User\UserGoodsDirectoryController::class,'detail']);//获取目录详情
        Route::post('update-goods-count',[Wap\User\UserGoodsDirectoryController::class,'updateGoodsCount']);//更新目录商品数量
        Route::post('update-all-goods-count',[Wap\User\UserGoodsDirectoryController::class,'updateAllGoodsCount']);//批量更新所有目录商品数量
    });

    Route::prefix('goods_category')->group(function(){
        Route::get('cat_temu_webpage_main',[Wap\User\GoodsCategoryController::class,'catTemuWebpageMain']);//获取temu商品网页前端主分类列表
        Route::get('cat_temu_webpage_list',[Wap\User\GoodsCategoryController::class,'catTemuWebpageList']);//获取temu网页前端商品分类列表
        Route::get('cat_temu_detail',[Wap\User\GoodsCategoryController::class,'catTemuDetail']);//获取temu网页前端商品分类详情
        Route::get('cat_temu_main',[Wap\User\GoodsCategoryController::class,'catTemuMain']);//获取temu商品主分类列表
        Route::get('cat_temu_list',[Wap\User\GoodsCategoryController::class,'catTemuList']);//获取temu商品分类列表
        Route::get('cat_temu_list_lazy',[Wap\User\GoodsCategoryController::class,'catTemuListLazy']);//获取temu商品分类列表（懒加载）
        Route::get('cat_temu_list_flat',[Wap\User\GoodsCategoryController::class,'catTemuListFlat']);//获取temu商品分类列表（虚拟表格专用）
        Route::get('cat_temu_children_count',[Wap\User\GoodsCategoryController::class,'catTemuChildrenCount']);//获取分类子分类数量
        Route::get('cat_temu_children_counts',[Wap\User\GoodsCategoryController::class,'catTemuChildrenCounts']);//批量获取分类子分类数量
        Route::get('cat_n11_list',[Wap\User\GoodsCategoryController::class,'catN11List']);//获取n11商品分类
        Route::get('cat_n11_detail',[Wap\User\GoodsCategoryController::class,'catN11Detail']);//获取n11商品分类详情
        Route::post('cat_n11_update',[Wap\User\GoodsCategoryController::class,'catN11Update']);//更新n11商品分类
        Route::post('cat_relation_set',[Wap\User\GoodsCategoryController::class,'catRelationSet']);//分类关联设置
        Route::get('cat_relation_list',[Wap\User\GoodsCategoryController::class,'catRelationList']);//获取分类关联列表
    });
    
    Route::prefix('user')->group(function(){
        Route::get('info',[Wap\User\UserController::class,'getUserInfo']);
        Route::get('store_info',[Wap\User\UserController::class,'getStoreInfo']);
        Route::get('logout',[Wap\User\UserController::class,'logout']);
        Route::post('task/add',[Wap\User\UserTaskController::class,'addTask']);//添加任务
        Route::get('task/list',[Wap\User\UserTaskController::class,'list']);//获取任务列表
        Route::get('task/start',[Wap\User\UserTaskController::class,'startTask']);//开启任务
        Route::post('task/update',[Wap\User\UserTaskController::class,'updateTask']);//更新任务
        Route::get('task/detail',[Wap\User\UserTaskDetailController::class,'getTaskDetail']);//获取任务详情
        Route::get('task/detail-list',[Wap\User\UserTaskDetailController::class,'getTaskDetailList']);//获取任务详情列表
        Route::get('task/detail-statistics',[Wap\User\UserTaskDetailController::class,'getTaskDetailStatistics']);//获取任务详情统计
        Route::post('task/detail/batch-retry',[Wap\User\UserTaskDetailController::class,'batchRetryFailedTasks']);//批量重试失败任务
        Route::post('task/detail/query-results',[Wap\User\UserTaskDetailController::class,'queryTaskResults']);//查询任务结果
        Route::get('task/detail/pending-query',[Wap\User\UserTaskDetailController::class,'getPendingQueryTasks']);//获取待查询任务列表
        Route::post('task/detail/batch-update',[Wap\User\UserTaskDetailController::class,'batchUpdateTaskDetails']);//批量更新任务状态
        Route::post('task/detail/save-upload-params',[Wap\User\UserTaskDetailController::class,'saveUploadParams']);//保存上传参数
        Route::get('task/detail/retry-upload-params',[Wap\User\UserTaskDetailController::class,'getRetryUploadParams']);//获取重新上传参数
        Route::post('task/detail/batch-retry-upload',[Wap\User\UserTaskDetailController::class,'batchRetryUpload']);//批量重新上传失败任务【使用这个】
        Route::get('task/detail/find-by-stock-code',[Wap\User\UserTaskDetailController::class,'findByStockCode']);//根据stockCode查找任务详情
        
        // 商品采集设置相关路由
        Route::get('collection-settings',[Wap\User\UserGoodsCollectionSettingsController::class,'getSettings']);//获取采集设置
        Route::post('collection-settings',[Wap\User\UserGoodsCollectionSettingsController::class,'saveSettings']);//保存采集设置
        Route::get('available-directories',[Wap\User\UserGoodsCollectionSettingsController::class,'getAvailableDirectories']);//获取可用目录
        Route::get('check-need-setup',[Wap\User\UserGoodsCollectionSettingsController::class,'checkNeedSetup']);//检查是否需要设置
        
        // 图片处理相关路由
        Route::post('process-goods-images',[Wap\User\ImageProcessController::class,'processGoodsImages']);//处理商品图片本地化
    });

    // N11被拒绝商品管理路由组
    Route::prefix('n11/rejected-products')->group(function(){
        Route::post('batch-save', [Wap\N11\N11RejectedProductController::class, 'batchSave']); // 批量保存被拒绝商品
        Route::get('pending-count', [Wap\N11\N11RejectedProductController::class, 'getPendingCount']); // 获取待处理数量
        Route::get('next-pending', [Wap\N11\N11RejectedProductController::class, 'getNextPending']); // 获取下一个待处理商品
        Route::post('update-status', [Wap\N11\N11RejectedProductController::class, 'updateStatus']); // 更新处理状态
        Route::get('statistics', [Wap\N11\N11RejectedProductController::class, 'getStatistics']); // 获取统计信息
    });

});

// 测试解密中间件的路由
/* Route::post('test-decrypt', function (\Illuminate\Http\Request $request) {
    // 返回解密后的请求数据
    return $request->all();
}); */