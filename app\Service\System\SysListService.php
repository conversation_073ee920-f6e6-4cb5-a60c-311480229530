<?php
declare(strict_types=1);

namespace App\Service\System;

use App\Jobs\SSEDataDealJob;
use App\Service\BaseService;
use App\Models\Land\LandModel;
use App\Exceptions\MyException;
use App\Models\Device\DeviceModel;
use Illuminate\Support\Facades\DB;
use App\Models\System\SysListModel;
use App\Models\System\SysTypeModel;

class SysListService extends BaseService
{
    protected $sysTypeModel;
    protected $sysListModel;

    private $no_del_ids = [1,2,19,3,4,5,21,22,23,24,25,26,27,6,7,8];

    public function __construct(SysTypeModel $sysTypeModel, SysListModel $sysListModel)
    {
        $this->sysTypeModel = $sysTypeModel;
        $this->sysListModel = $sysListModel;
        parent::__construct();
    }

    public function getTypeList()
    {
        $list = $this->sysTypeModel::query()
                    ->where('status',1)
                    ->get()
                    ->toArray();
        $list = array_map(function($item){
            unset($item['ctime']);
            unset($item['utime']);
            unset($item['status']);
            return $item;
        }, $list);
        return $list;
    }

    public function getTypeById($id=0){
        if((int)$id>=0){
            $list = $this->sysTypeModel::query()
                    ->where('status',1)
                    ->find($id)
                    ->toArray();
            unset($list['ctime']);
            unset($list['utime']);
            unset($list['status']);
            return $list;
        }
        return [];
    }

    //批量操作
    public function multiOpt(){
        $data = request()->all();
        if(empty($data['ids'])){
            throw new MyException('请选择要操作的信息');
        }
        if(!is_array($data['ids'])){
            $data['ids'] = dealNumDivideByComma($data['ids']);
            if(empty($data['ids'])){
                throw new MyException('请选择要操作的信息');
            }
        }
        $data['ids'] = explode(",",$data['ids']);
        if(empty($data['opt'])){
            throw new MyException('请选择操作');
        }
        if(!in_array($data['opt'],['status','del'])){
            throw new MyException('操作类型错误');
        }

        if(array_intersect($data['ids'], $this->no_del_ids)){
            throw new MyException("您选择的信息中存在不允许删除的系统默认配置");
        }


        $id = $data['ids'][0];
        $info = SysListModel::query()
                    ->where('id',(int)$id)
                    ->first();
        if(!$info){
            throw new MyException("信息不存在");
        }
        $type = $info->type;

        switch($data['opt']){
            case 'status':
                if(!is_numeric($data['status'])){
                    throw new MyException('请选择状态');
                }
                if(!in_array($data['status'],[0,1])){
                    throw new MyException('状态错误');
                }
                if($data['status']==0){
                    $cn = $this->infoNum($data['ids'],$type);
                    if($cn>0){
                        throw new MyException("您选择的类型下总共有{$cn}条信息，不允许禁用");
                    }
                }
                return SysListModel::query()->whereIn('id',$data['ids'])->update(['status'=>$data['status']]);
                break;
            case 'del':
                $cn = $this->infoNum($data['ids'],$type);
                if($cn>0){
                    throw new MyException("您选择的类型下总共有{$cn}条信息，不允许删除");
                }
                return SysListModel::query()->whereIn('id',$data['ids'])->delete();
                break;
        }
        return [];
    }

    public function getListAllByTypeId($type_id=0){
        $list = SysListModel::query()->where('type',$type_id)->where('status',1)->pluck('name','id')->toArray();
        return arrayToValueName($list);
    }

    public function getListByTypeId($type_id=0)
    {
        $type_info = $this->getTypeById($type_id);
        if(!$type_info){
            throw new MyException("类型不存在");
        }
        $data = request()->all();
        $list = SysListModel::query()
                    ->where('type',$type_id)
                    ->when(!empty($data['name']),function($query) use ($data){
                        $query->where('name','like','%'.$data['name'].'%');
                    })
                    ->orderByDesc('utime')
                    ->orderByDesc('id')
                    ->paginate($this->per_page);
        $list->getCollection()->transform(function($item)use($type_info){
            $item->status_name = $item->status == 1 ? '开启' : '关闭';
            $item->ctime = date('Y-m-d H:i:s',$item->ctime);
            $item->utime = date('Y-m-d H:i:s',$item->utime);
            if(!empty($item->image)){
                $item->image = uploadFilePath($item->image);
            }
            $item->price = removeTrailingZeros($item->price);
            $item->type_name         = $type_info['name'];
            $item->type_show_sn      = $type_info['show_sn'];
            $item->type_show_click   = $type_info['show_click'];
            $item->type_show_image   = $type_info['show_image'];
            $item->type_image_width  = $type_info['image_width'];
            $item->type_image_height = $type_info['image_height'];
            return $item;
        });
        return $list;
    }

    public function add(){
        $data = request()->all();
        if(!$data){
            throw new MyException("数据错误");
        }
        foreach($data as $key => $value){
            if($value === null){
                $data[$key] = '';
            }
        }
        if(!$data['type']){
            throw new MyException("类型错误");
        }
        $type_info = $this->getTypeById($data['type']);
        if(!$type_info){
            throw new MyException("类型不存在");
        }
        if(empty($data['name'])){
            throw new MyException("请填写名称");
        }
        if(mb_strlen($data['name'])>80){
            throw new MyException("名称不能超过80个字符");
        }

        $existingItem = SysListModel::query()
                            ->where('type', $data['type'])
                            ->where('name', $data['name'])
                            ->first();
        if($existingItem){
            throw new MyException("您输入的名称已存在");
        }

        if(!empty($data['sn'])){
            if(mb_strlen($data['sn'])>80){
                throw new MyException("SN不能超过80个字符");
            }
            $existingItem = SysListModel::query()
                            ->where('type', $data['type'])
                            ->where('sn', $data['sn'])
                            ->first();
            if($existingItem){
                throw new MyException("您输入的编号已存在");
            }
        }



        $data['status'] = $data['status'] ?? 1;
        $data['status'] = (int)$data['status'];
        if(!in_array($data['status'],[0,1])){
            $data['status'] = 1;
        }
        $data['ctime'] = time();
        $data['utime'] = time();
        $model = new SysListModel();
        $data = $model->schemaFieldsFromArray($data);
        //
        $model->insert($data);

        if($data['type']==1){
            //公告
            SSEDataDealJob::dispatch(1)->onConnection('sse_data_database')->onQueue('SSEDataDealQueue');
        }

        return $model;
    }

    public function detail($id=0){
        $info = SysListModel::query()
                    ->where('id',(int)$id)
                    ->first();
        if(!$info){
            throw new MyException("信息不存在");
        }
        $info = $info->toArray();
        $type_info = $this->getTypeById($info['type']);
        $info['status_name'] = $info['status'] == 1 ? '开启' : '关闭';
        $info['type_name'] = $type_info['name'];
        $info['type_show_sn'] = $type_info['show_sn'];
        $info['type_show_click'] = $type_info['show_click'];
        $info['type_show_image'] = $type_info['show_image'];
        $info['type_image_width'] = $type_info['image_width'];
        $info['type_image_height'] = $type_info['image_height'];
        $info['price'] = removeTrailingZeros($info['price']);
        $info['ctime'] = date('Y-m-d H:i:s',$info['ctime']);
        $info['utime'] = date('Y-m-d H:i:s',$info['utime']);
        if(!empty($info['image'])){
            $info['image'] = uploadFilePath($info['image']);
        }
        return $info;
    }

    public function update(){
        $data = request()->all();
        if(!$data){
            throw new MyException("数据错误");
        }
        foreach($data as $key => $value){
            if($value === null){
                $data[$key] = '';
            }
        }
        $id = $data['id'] ?? 0;
        if(!$id){
            throw new MyException("信息ID错误");
        }
        $id = (int)$id;
        $info = SysListModel::query()
                    ->where('id',(int)$id)
                    ->where('status',1)
                    ->first();
        if(!$info){
            throw new MyException("信息不存在");
        }
        $type = $info->type;
        if(!empty($data['id'])){
            unset($data['id']);
        }
        if(!empty($data['type'])){
            unset($data['type']);//编辑不允许修改类型
        }
        if(empty($data['name'])){
            throw new MyException("请填写名称");
        }

        $existingInfo = SysListModel::query()
                        ->where('type', $type)
                        ->where('name', $data['name'])
                        ->where('id', '!=', $id)
                        ->first();
        if($existingInfo){
            throw new MyException("您输入的名称已存在");
        }

        if(!empty($data['sn'])){
            $existingInfo = SysListModel::query()
                        ->where('type', $type)
                        ->where('sn', $data['sn'])
                        ->where('id', '!=', $id)
                        ->first();
            if($existingInfo){
                throw new MyException("您输入的编号已存在");
            }
        }

        $data['status'] = $data['status'] ?? 1;
        $data['status'] = (int)$data['status'];
        if(!in_array($data['status'],[0,1])){
            $data['status'] = 1;
        }
        $data['utime'] = time();
        if($data['status']==0){
            $cn = $this->infoNum($id,$type);
            if($cn>0){
                throw new MyException("该类型下有{$cn}条信息，不允许禁用");
            }
        }
        $model = new SysListModel();
        $data = $model->schemaFieldsFromArray($data);
        if(!empty($data['ctime'])){
            unset($data['ctime']);
        }
        $model->where('id',$id)
            ->where('status',1)
            ->update($data);
        if($type==1){
            //公告
            SSEDataDealJob::dispatch(1)->onConnection('sse_data_database')->onQueue('SSEDataDealQueue');
        }
        return [];
    }

    public function delete($id=0){
        $id = (int)$id;
        if(!$id){
            throw new MyException("信息ID错误");
        }

        if(in_array($id,$this->no_del_ids)){
            throw new MyException("系统默认配置不能删除");
        }

        $info = SysListModel::query()
                    ->where('id',(int)$id)
                    ->where('status',1)
                    ->first();
        if(!$info){
            throw new MyException("信息不存在");
        }
        $type = $info->type;
        $cn = $this->infoNum($id,$type);
        if($cn>0){
            throw new MyException("该类型下有{$cn}条信息，不允许删除");
        }
        $info->delete();
        return [];
    }

    public function infoNum($id,$type=0){
        if(!is_array($id)){
            $id = (string)$id;
            $id = explode(",",$id);
        }
        if(is_array($id)){
            foreach($id as $item){
                $item = (int)$item;
            }
        }
        if(empty($id)){
            return 0;
        }
        $type   = (int)$type;
        $cn     = 0;
        switch($type){
            case 1://公告
                break;
            case 2://设备
                $cn = DeviceModel::query()->whereIn('type',$id)->count();
                break;
            case 3://作物
                foreach($id as $item){
                    $cn += LandModel::query()->whereRaw("FIND_IN_SET(".$item.", type)")->count();
                }
                break;
        }
        return $cn;
    }


}
