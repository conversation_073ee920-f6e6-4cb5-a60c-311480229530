<?php
declare(strict_types=1);

namespace App\Service\System;
use App\Exceptions\MyException;
use App\Models\City\City;
use App\Service\BaseService;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\ArrayShape;

class CityService extends BaseService
{

    protected City $cityModel;

    public function __construct(City $city)
    {
        $this->cityModel = $city;
        parent::__construct();
    }



    public function areaListByLevelType(int $levelType=0)
    {
        $data = $this->request->all();
        $fields = ['id','name','shortName','firstChar','parentId as pid','levelType as level'];
        $map = [];
        $map[] = ['levelType',$levelType];
        if(!empty($data['name'])){
            $map[] = ['name','like','%'.$data['name'].'%'];
        }
        if(!empty($data['py'])){
            $map[] = ['jianpin','like',strtoupper($data['py']).'%'];
        }
        $list = $this->cityModel::query()->where($map)->select($fields)->groupBy(['firstChar','id'])->orderBy('firstChar')->get();
        if($list->isEmpty()){
            return [];
        }
        $tmp = [];
        foreach($list as $item){
            $tmp[$item['firstChar']][] = $item;
        }
        return $tmp;
    }

    public function cityInfoByCityCode(string $cityCode): array
    {
        $default_city = [
            'province'=>[
                'id'   => 110000,
                'name' => '北京'
            ],
            'city'=>[
                'id'   => 110100,
                'name' => '北京'
            ],
            'cityCode'=>'010'
        ];
        $city = $this->cityModel::query()->where('cityCode',$cityCode)->where('levelType',2)->select(['id','shortName as name','parentId as pid'])->first();
        if(!$city) return $default_city;
        $province = $this->cityModel::query()->where('id',$city['pid'])->select(['id','shortName as name'])->first();
        if(!$province) return $default_city;
        $city = $city->toArray();
        $province = $province->toArray();
        $value = [
            'province'  => [
                'id'    => $province['id'],
                'name'  => $province['name']
            ],
            'city'      =>[
                'id'    => $city['id'],
                'name'  => $city['name']
            ],
            'cityCode'  => $cityCode
        ];
        return $value;
    }

    public function cityInfoByCityId(int $cityId): array
    {
        $default_city = [
            'province'=>[
                'id'   => 110000,
                'name' => '北京'
            ],
            'city'=>[
                'id'   => 110100,
                'name' => '北京'
            ],
            'cityCode'=>'010'
        ];
        $city = $this->cityModel::query()->where('id',$cityId)->select(['id','cityCode','shortName as name','parentId as pid'])->first();
        if(!$city) return $default_city;
        $province = $this->cityModel::query()->where('id',$city['pid'])->select(['id','shortName as name'])->first();
        if(!$province) return $default_city;
        $city = $city->toArray();
        $province = $province->toArray();
        $value = [
            'province'  => [
                'id'    => $province['id'],
                'name'  => $province['name']
            ],
            'city'      =>[
                'id'    => $city['id'],
                'name'  => $city['name']
            ],
            'cityCode'  => $city['cityCode']
        ];
        return $value;
    }

    public function getCityByLongLatNew()
    {
        $data = $this->request->all();
        if(empty($data['lng']) || empty($data['lat'])){
            throw new MyException('缺少经纬度');
        }

        $default_city = [
            'province'=>[
                'id'   => 110000,
                'name' => '北京'
            ],
            'city'=>[
                'id'   => 110100,
                'name' => '北京'
            ],
            'cityCode'=>'010'
        ];


        $city = getCityByLongLat((string)$data['lng'],(string)$data['lat']);
        $city = str_ireplace(['市','自治区','县','镇','区'],'',$city);
        $fields = $this->cityModel->selectFields();
        $city = $this->cityModel::query()->where('name','like',$city.'%')->select($fields)->orderBy('id')->first();
        if(!$city) return $default_city;
        return $this->cityInfoByCityId($city['id']);
    }



    public function provinceList(): array
    {
        $map   = [];
        $map[] = ['levelType',1];
        $map[] = ['status',1];
        $fields = ['id','name','shortName','firstChar','parentId as pid','levelType as level'];
        $list = $this->cityModel::query()->where($map)->orderByDesc('recommend')->orderBy('firstChar','ASC')->select($fields)->get();
        if($list->isEmpty()){
            return [];
        }
        return $list->toArray();
    }

    public function cityListByPid(int $pid=0)
    {
        if($pid<=0){
            throw new MyException("参数错误");
        }
        $pid = (int)$pid;
        $map   = [];
        $map[] = ['parentId',$pid];
        $map[] = ['status',1];
        $fields = ['id','name','shortName','firstChar','parentId as pid','levelType as level'];
        $list = $this->cityModel::query()->where($map)->orderByDesc('recommend')->orderBy('firstChar','ASC')->select($fields)->get();
        return $list ? $list->toArray() : [];
    }

    public function getCityByLongLat()
    {
        $data = $this->request->all();
        if(empty($data['lng']) || empty($data['lat'])){
            throw new MyException('缺少经纬度');
        }
        $city = getCityByLongLat((string)$data['lng'],(string)$data['lat']);
        $city = str_ireplace(['市','自治区','县','镇','区'],'',$city);
        $fields = $this->cityModel->selectFields();
        $city = $this->cityModel::query()->where('name','like',$city.'%')->select($fields)->orderBy('id')->first();
        if(!$city) return new \stdClass();
        return ['id'=>$city['id'],'name'=>$city['shortname']];
    }


    /*
     * 根据JSON文件 初始化CITY数据
     * */
    public function init()
    {
        $city_data = file_get_contents(resource_path().'/js/city.json');
        $city_data = json_decode($city_data,true);
        if(is_array($city_data) && count($city_data)>0){
            DB::beginTransaction();
            try {
                $city1 = $city_data;
                unset($city1['childNode']);
                $id = $city1['id'];
                unset($city1['id']);
                $this->city::query()->updateOrCreate(['id'=>$id],$city1);
                $this->dealCity($city_data['childNode']);
                DB::commit();
                return ['status'=>1];
            } catch (\Throwable $exception) {
                DB::rollBack();
                if($exception instanceof MyException){
                    throw new MyException($exception->getMessage());
                }else{
                    throw new MyException("操作失败" . $exception->getMessage() . basename($exception->getFile()) . $exception->getLine());
                }
            }
        }
    }
    /*
     * 递归处理城市JSON数据
     * */
    public function dealCity(array $city=[]): array
    {
        DB::beginTransaction();
        try {
            foreach ($city as $item){
                if(isset($item['childNode']) && is_array($item['childNode']) && count($item['childNode'])>0){
                    //先处理自己
                    $city1 = $item;
                    unset($city1['childNode']);
                    $id = $city1['id'];
                    unset($city1['id']);
                    $this->city::query()->updateOrCreate(['id'=>$id],$city1);

                    self::dealCity($item['childNode']);
                }else{
                    if(isset($item['childNode'])){
                        unset($item['childNode']);
                    }
                    /*var_dump($city);
                    exit();*/
                    $id = $item['id'];
                    unset($item['id']);
                    $this->city::query()->updateOrCreate(['id'=>$id],$item);
                }
            }
            DB::commit();
            return ['status'=>1];
        } catch (\Throwable $exception) {
            DB::rollBack();
            if($exception instanceof MyException){
                throw new MyException($exception->getMessage());
            }else{
                throw new MyException("操作失败" . $exception->getMessage() . basename($exception->getFile()) . $exception->getLine());
            }
        }
    }

    public function detail(int $id=0): array
    {
        $city = $this->cityModel::query()->where('id',$id)->select(['id','shortName','parentId'])->first();
        return $city ? $city->toArray() : [];
    }

    /*
     * 根据AREA_ID 获取所有信息
     * */
    public function area_info_all(int $id=0): array
    {
        $area = $this->detail($id);
        $city = [];
        $province = [];
        if(!empty($area['parentId'])){
            $city = $this->detail($area['parentId']);
        }
        if(!empty($city['parentId'])){
            $province = $this->detail($city['parentId']);
        }
        return array_values(compact('province','city','area'));
    }

}
