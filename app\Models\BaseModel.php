<?php
declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class BaseModel extends Model
{
    protected $guarded = [];
    protected $connection='mysql';
    public $timestamps = false;

    public function setTabel(string $table): BaseModel
    {
        $this->setTable($table);
        return $this;
    }

        /*
     * 数组变量 返回数据表中存在字段为KEY的数组变量
     * $data  准备处理的数组
     * $exclude  排除的字段
     * */
    public function schemaFieldsFromArray($data,array $exclude=[]){
        $fields = $this->getSchemaFields();
        if (count($fields) > 0) {
            $fields = array_map(function ($item) {
                if (strpos($item, '`') !== false) {
                    $item = str_replace('`', '', $item);
                }
                return $item;
            }, $fields);
            $data =  array_intersect_key($data, array_flip($fields));
            if(count($exclude)>0){
                $data = array_filter($data,function($item,$key)use($exclude){
                    return !in_array($key,$exclude);
                },ARRAY_FILTER_USE_BOTH);
            }
        }
        return $data;
    }

        /*
     * 获取数据库中除主键外所有字段
     * */
    public function getSchemaFields()
    {
        // 直接返回表的所有字段信息
        $columns = array_flip($this->getFields());
        unset($columns[$this->getKeyName()]);//去除主键
        return array_keys($columns);
    }

        /*
     * 根据传递的参数获取查询字段
     * 参数示例
     * ['*']  查询所有字段  自动获取所有字段
     * ['*',['id','name']]     排除 id,name字段
     * ['id','name','title']   查询指定的字段
     * */
    public function getFields(array $fields = ['*']){
        $table = $this->getTable();
        $prefix = $this->getConnection()->getTablePrefix();
        $key = $prefix.$table;
        $isLocal = app()->isLocal();
        if($isLocal){
            //本地开发不缓存
            $columns = array_keys($this->getConnection()->getDoctrineSchemaManager()->listTableColumns($key));
        }else{
            $columns = Cache::rememberForever($key,function()use($key){
                return array_keys($this->getConnection()->getDoctrineSchemaManager()->listTableColumns($key));
            });
        }

        if(count($fields)==0){
            $columns = array_values($columns);
        }else{
            if(count($fields)==1){
                if($fields[0]=='*'){
                    $columns = array_values($columns);
                }else{
                    $columns = $fields;
                }
            }else{
                if(is_array($fields[1])){
                    //排除指定的字段
                    $unExpect = $fields[1];
                    $columns = array_filter($columns,function($item)use($unExpect){
                        return !in_array($item,$unExpect);
                    });
                    $columns = array_values($columns);
                }else{
                    $columns = $fields;
                }
            }
        }
        return $columns;
    }

    protected function createTime(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if (empty($value) || strlen((string)$value) !== 10) {
                    return '';
                }
                return date('Y-m-d H:i:s', (int)$value);
            }
        );
    }

    protected function updateTime(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if (empty($value) || strlen((string)$value) !== 10) {
                    return '';
                }
                return date('Y-m-d H:i:s', (int)$value);
            }
        );
    }

    protected function deleteTime(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if (empty($value) || strlen((string)$value) !== 10) {
                    return '';
                }
                return date('Y-m-d H:i:s', (int)$value);
            }
        );
    }

}
