<template>
  <el-dialog
    v-model="visible"
    title=""
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    center
    class="task-loading-dialog"
  >
    <div class="loading-content">
      <div class="loading-animation">
        <div class="spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
      </div>
      
      <div class="loading-text">
        <h3>{{ title }}</h3>
        <p>{{ message }}</p>
      </div>
      
      <div class="loading-progress" v-if="showProgress">
        <el-progress 
          :percentage="progress" 
          :stroke-width="6"
          :show-text="false"
          color="#409eff"
        />
        <div class="progress-text">{{ progressText }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  visible: boolean
  title?: string
  message?: string
  showProgress?: boolean
  progress?: number
  progressText?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '任务数据加载中',
  message: '正在处理任务数据，请稍候...',
  showProgress: false,
  progress: 0,
  progressText: ''
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
</script>

<style scoped>
.task-loading-dialog {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
}

:deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

:deep(.el-dialog__header) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 40px 30px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.loading-animation {
  margin-bottom: 30px;
}

.spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: var(--primary-color);
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: var(--success-color);
  animation-delay: 0.3s;
  animation-duration: 1.5s;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: var(--warning-color);
  animation-delay: 0.6s;
  animation-duration: 1.8s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-bottom: 20px;
}

.loading-text h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
  background: linear-gradient(135deg, var(--primary-color), var(--success-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.loading-progress {
  width: 100%;
  margin-top: 20px;
}

.progress-text {
  margin-top: 10px;
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 480px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 0 auto;
  }
  
  :deep(.el-dialog__body) {
    padding: 30px 20px;
  }
  
  .spinner {
    width: 60px;
    height: 60px;
  }
  
  .loading-text h3 {
    font-size: 18px;
  }
  
  .loading-text p {
    font-size: 13px;
  }
}
</style> 