<template>
  <el-dialog
    v-model="visible"
    title="TEMU商品采集设置"
    width="500px"
    :close-on-click-modal="false"
    :z-index="10002"
    append-to-body
    @open="handleDialogOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="采集模式" prop="collection_mode">
        <el-radio-group v-model="form.collection_mode" class="collection-mode-group">
          <div class="radio-item">
            <el-radio :value="1">详情页自动采集入库</el-radio>
          </div>
          <div class="radio-item">
            <el-radio :value="2">详情页手动确认入库</el-radio>
          </div>
          <div class="radio-item">
            <el-radio :value="3">详情页不采集入库</el-radio>
          </div>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="商品存储目录" prop="default_directory_id">
        <div class="directory-select-wrapper">
          <el-select 
            v-model="form.default_directory_id" 
            placeholder="请选择存储目录"
            class="directory-select"
            :teleported="false"
            popper-class="select-dropdown-in-dialog"
            :fit-input-width="true"
          >
            <el-option
              v-for="directory in availableDirectories"
              :key="directory.id"
              :label="`${directory.name} (${directory.goods_count}个商品)`"
              :value="directory.id"
            />
          </el-select>
          <el-button type="success" @click="$emit('createDirectory')" class="add-directory-btn">
            新增目录
          </el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="form.no_remind_24h">24小时内不再提醒设置商品存储目录</el-checkbox>
      </el-form-item>

      <!-- 显示下次提醒时间 -->
      <el-form-item v-if="nextRemindTime" label="下次提醒时间">
        <div class="remind-time-info">
          <span :class="{ 'expired': isRemindTimeExpired }">
            {{ nextRemindTime }}
          </span>
          <span v-if="isRemindTimeExpired" class="expired-tag">【已到期】</span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed } from 'vue';
import type { Directory, SaveSettingsData } from '../utils/collectionSettingsApi';

interface Props {
  modelValue: boolean;
  availableDirectories: Directory[];
  loading?: boolean;
  initialData?: {
    default_directory_id?: number;
    collection_mode?: number;
    no_remind_until?: string | null;
  };
}

interface Emits {
  'update:modelValue': [value: boolean];
  'confirm': [data: SaveSettingsData];
  'createDirectory': [];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const formRef = ref();

const form = reactive({
  default_directory_id: undefined as number | undefined,
  collection_mode: 1,
  no_remind_24h: false
});

const formRules = {
  default_directory_id: [
    { required: true, message: '请选择存储目录', trigger: 'change' }
  ]
};

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
});

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

const handleDialogOpen = async () => {
  // 填充初始数据
  if (props.initialData) {
    form.default_directory_id = props.initialData.default_directory_id || undefined;
    form.collection_mode = props.initialData.collection_mode || 1;
  } else {
    form.default_directory_id = undefined;
    form.collection_mode = 1;
  }
  form.no_remind_24h = false;
  
  await nextTick();
  
  // 强制设置 Select 下拉选项的 z-index
  setTimeout(() => {
    const selectDropdowns = document.querySelectorAll('.el-select-dropdown, .el-popper');
    selectDropdowns.forEach(dropdown => {
      (dropdown as HTMLElement).style.zIndex = '10004';
    });
  }, 100);
};

const handleConfirm = async () => {
  try {
    await formRef.value.validate();
    
    emit('confirm', {
      default_directory_id: form.default_directory_id ?? 0,
      collection_mode: form.collection_mode,
      no_remind_24h: form.no_remind_24h
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const handleCancel = () => {
  visible.value = false;
};

// 计算下次提醒时间
const nextRemindTime = computed(() => {
  return props.initialData?.no_remind_until || null;
});

// 检查提醒时间是否已到期
const isRemindTimeExpired = computed(() => {
  if (!nextRemindTime.value) return false;
  const remindTime = new Date(nextRemindTime.value);
  const now = new Date();
  return now >= remindTime;
});
</script>

<style scoped>
.collection-mode-group {
  width: 100px;
}

.radio-item {
  margin-bottom: 8px;
  padding: 4px 0;
  line-height: 1.5;
}

.radio-item:last-child {
  margin-bottom: 0;
}

:deep(.el-radio) {
  margin-right: 0;
  width: 100%;
  height: auto;
  line-height: 1.5;
}

:deep(.el-radio__label) {
  font-size: 14px;
  color: #606266;
  padding-left: 8px;
}

.directory-select-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;
}

.directory-select {
  flex: 1;
  min-width: 0;
}

:deep(.directory-select .el-select) {
  width: 100% !important;
}

:deep(.directory-select .el-input) {
  width: 100% !important;
}

:deep(.directory-select .el-input__wrapper) {
  width: 100% !important;
  min-width: 200px !important;
}

.add-directory-btn {
  flex-shrink: 0;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Select 下拉选项样式修复 */
:deep(.el-select-dropdown) {
  z-index: 10004 !important;
  min-width: 300px !important;
}

:deep(.select-dropdown-in-dialog) {
  z-index: 10004 !important;
  min-width: 300px !important;
  width: auto !important;
}

:deep(.select-dropdown-in-dialog .el-select-dropdown__item) {
  background: #ffffff !important;
  color: #606266 !important;
  padding: 0 20px !important;
  height: 34px !important;
  line-height: 34px !important;
  font-size: 14px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-width: 280px !important;
}

:deep(.select-dropdown-in-dialog .el-select-dropdown__item:hover) {
  background-color: #f5f7fa !important;
}

:deep(.select-dropdown-in-dialog .el-select-dropdown__item.selected) {
  background-color: #409eff !important;
  color: #ffffff !important;
  font-weight: bold !important;
}

/* 强制修复下拉框宽度 */
:deep(.el-popper) {
  min-width: 300px !important;
}

:deep(.el-select-dropdown__wrap) {
  min-width: 300px !important;
}

:deep(.el-scrollbar__view) {
  min-width: 300px !important;
}

/* 提醒时间显示样式 */
.remind-time-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.remind-time-info .expired {
  color: #f56c6c;
}

.remind-time-info .expired-tag {
  color: #f56c6c;
  font-weight: bold;
  font-size: 12px;
}
</style> 