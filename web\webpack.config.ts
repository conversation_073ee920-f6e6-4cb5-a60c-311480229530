import path from 'path';
import { VueLoaderPlugin } from 'vue-loader';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import webpack from 'webpack';
import type { Configuration } from 'webpack';
import { EsbuildPlugin } from 'esbuild-loader';
// 确保 copy-webpack-plugin 类型被正确引用
import 'copy-webpack-plugin';

const isDevelopment = process.env.NODE_ENV !== 'production';
const isFastBuild = process.env.BUILD_MODE === 'fast';

interface WebpackConfig extends Configuration {
  devServer?: any;
}

const config: WebpackConfig = {
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  entry: './src/main.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: isDevelopment ? 'main.js' : 'main.js',//main.[contenthash:8].js
    publicPath: isDevelopment ? '/' : './dist/',
    clean: true
  },
  // 快速构建模式下使用简单的source map，生产环境禁用，开发环境使用安全的 source map
  devtool: isDevelopment ? 'eval-cheap-module-source-map' : (isFastBuild ? 'cheap-source-map' : false),
  resolve: {
    extensions: ['.ts', '.js', '.vue', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.ts$/,
        loader: 'ts-loader',
        exclude: /node_modules/,
        options: {
          appendTsSuffixTo: [/\.vue$/],
          // 快速构建模式下跳过类型检查
          transpileOnly: isFastBuild
        }
      },
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  plugins: [
    new VueLoaderPlugin(),
    // 定义环境变量，确保 Vue 使用与 webpack mode 相同的环境
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV === 'production' ? 'production' : 'development'),
      '__VUE_OPTIONS_API__': true,
      '__VUE_PROD_DEVTOOLS__': process.env.NODE_ENV !== 'production'
    }),
    // 在开发模式下生成HTML文件用于独立开发
    ...(isDevelopment ? [
      new HtmlWebpackPlugin({
        template: path.resolve(__dirname, 'home.html'),
        filename: 'index.html',
        inject: true
      })
    ] : [])
  ],
  // 开发服务器配置
  devServer: isDevelopment ? {
    static: {
      directory: path.join(__dirname, 'dist'),
    },
    compress: true,
    port: 8080,
    hot: true,
    open: true,
    historyApiFallback: true,
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
    },
  } : undefined,
  // 优化配置 - 快速构建模式下跳过复杂优化，正常生产构建使用完整优化
  optimization: {
    minimize: process.env.NODE_ENV === 'production' && !isFastBuild,
    minimizer: process.env.NODE_ENV === 'production' && !isFastBuild ? [
      new EsbuildPlugin({
        target: 'es2015', // 指定目标环境
        css: true,  // 允许压缩 CSS
        minify: true,
        minifyWhitespace: true,
        minifyIdentifiers: true,
        minifySyntax: true,
        drop: ['console', 'debugger'], // 移除 console 和 debugger
        treeShaking: true,
        legalComments: 'none', // 移除法律注释
        charset: 'utf8'
      })
    ] : []
  }
};

export default config;
