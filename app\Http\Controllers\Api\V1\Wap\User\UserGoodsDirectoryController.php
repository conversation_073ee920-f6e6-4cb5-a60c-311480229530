<?php
namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserGoodsDirectoryService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserGoodsDirectoryController extends Controller
{
    protected UserGoodsDirectoryService $directoryService;

    public function __construct(UserGoodsDirectoryService $directoryService)
    {
        $this->directoryService = $directoryService;
        parent::__construct();
    }

    /**
     * 获取目录列表（分页）
     */
    public function list(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $params = $request->only(['page', 'pageSize', 'status', 'name', 'start_date', 'end_date']);
        $result = $this->directoryService->getDirectoryList($userId, $params);
        
        return $this->apiSuccess($result);
    }

    /**
     * 创建目录
     */
    public function create(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'name', 'description', 'sort_order', 'status'
        ]);
        
        $result = $this->directoryService->createDirectory($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新目录
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'id', 'name', 'description', 'sort_order', 'status'
        ]);
        
        $result = $this->directoryService->updateDirectory($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 批量更新目录
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only(['ids', 'status']);
        $result = $this->directoryService->batchUpdateDirectory($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 删除目录
     */
    public function delete(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $directoryId = (int)$request->input('id');
        $result = $this->directoryService->deleteDirectory($userId, $directoryId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取目录详情
     */
    public function detail(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $directoryId = (int)$request->input('id');
        $result = $this->directoryService->getDirectoryDetail($userId, $directoryId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 更新目录商品数量
     */
    public function updateGoodsCount(Request $request): JsonResponse
    {
        $directoryId = (int)$request->input('directory_id');
        $this->directoryService->updateDirectoryGoodsCount($directoryId);
        
        return $this->apiSuccess(['message' => '商品数量更新成功']);
    }

    /**
     * 批量更新所有目录的商品数量
     */
    public function updateAllGoodsCount(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $this->directoryService->updateAllDirectoryGoodsCount($userId);
        
        return $this->apiSuccess(['message' => '所有目录商品数量更新成功']);
    }
}