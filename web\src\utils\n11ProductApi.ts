/**
 * N11商品上传API
 * 用于处理N11平台的商品上传相关接口
 */
import { sendRequestViaBackground } from './api'
import { handleN11ApiResponse } from './responseHandler'
import { formatDateTime } from './date'

// N11分类属性接口
export interface N11CategoryAttribute {
  attributeId: number
  categoryId: number
  attributeName: string
  isMandatory: boolean
  isVariant: boolean
  isSlicer: boolean
  isCustomValue: boolean
  isN11Grouping: boolean
  attributeOrder: number
  attributeValues: Array<{
    id: number
    value: string
  }>
}

// N11分类响应接口
export interface N11CategoryResponse {
  id: number
  name: string
  categoryAttributes: N11CategoryAttribute[]
}

// 商品属性接口
export interface ProductAttribute {
  id: number
  valueId: number | null
  customValue: string | null
}

// 商品图片接口
export interface ProductImage {
  url: string
  order: number
}

// 商品数据接口
export interface ProductData {
  title: string
  description: string
  categoryId: number
  currencyType: string
  productMainId: string
  preparingDay: number
  shipmentTemplate: string
  stockCode: string
  quantity: number
  salePrice: number
  listPrice: number
  vatRate: number
  images: ProductImage[]
  attributes: ProductAttribute[]
}

// N11商品上传响应接口
/**
 
上传成功的格式示例
{
    "id": 1135035494,
    "type": "PRODUCT_CREATE",
    "status": "IN_QUEUE",
    "reasons": [
        "1 sku işlenmeye alındı."
    ]
}

上传失败 500 错误

{
    "exceptionType": "com.n11.task.server.exception.TaskInvalidContentException",
    "errorCode": "TASK_ERR_006",
    "errorMessage": "Unrecognized field \"encrypted_data\" (class com.n11.task.server.dto.TaskPayloadDTO), not marked as ignorable (one known property: \"payload\"])\n at [Source: (StringReader); line: 1, column: 20] (through reference chain: com.n11.task.server.dto.TaskPayloadDTO[\"encrypted_data\"])"
}


**/
export interface N11ProductUploadResponse {
  id?: number;
  type?: string;
  status?: string;
  reasons?: string[];
  error?: string;
  response?: string;
}

/**
 * 获取N11分类属性
 * @param categoryId 分类ID
 * @param appKey N11应用密钥
 * @returns 分类属性数据
 */
export const getN11CategoryAttributes = async (
  categoryId: number,
  appKey: string
): Promise<N11CategoryResponse> => {
  const url = `https://api.n11.com/cdn/category/${categoryId}/attribute`
  
  const headers = {
    "Content-Type": "text/json; charset=utf-8",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "appkey": appKey
  }

  try {
    const response = await sendRequestViaBackground({
      funName: 'getN11CategoryAttributes',
      url,
      method: 'GET',
      headers,
      auth: false,
      encrypto: false,
      timeout: 8000
    })

    console.log('N11分类属性响应:', response)
    return response as N11CategoryResponse

  } catch (error: any) {
    console.error('获取N11分类属性失败:', error)
    throw error
  }
}

/**
 * 上传商品到N11平台
 * @param productData 商品数据
 * @param appKey N11应用密钥
 * @param appSecret N11应用秘钥
 * @param integratorName 集成商名称
 * @param taskDetailId 任务详情ID（用于保存参数）
 * @param debugMode 调试模式，true为调试模式（不真正调用API）
 * @returns 上传结果
 */
export const uploadProductToN11 = async (
  productData: ProductData,
  appKey: string,
  appSecret: string,
  integratorName: string,
  taskDetailId?: number,
  debugMode?: boolean
): Promise<N11ProductUploadResponse> => {
  const url = "https://api.n11.com/ms/product/tasks/product-create"
  
  const payload = {
    payload: {
      integrator: integratorName,
      skus: [productData]
    }
  }
  
  const headers = {
    "Content-Type": "application/json",
    "appkey": appKey,
    "appsecret": appSecret
  }

  // 获取调试模式设置（如果未传入则从配置中获取）
  let actualDebugMode = debugMode
  if (actualDebugMode === undefined) {
    try {
      actualDebugMode = await new Promise<boolean>((resolve) => {
        chrome.runtime.sendMessage({
          funType: 'getConfig',
          configKey: 'n11DebugMode'
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.warn('获取调试模式配置失败，使用默认值true');
            resolve(true);
            return;
          }
          // 修复：background返回的是url字段，不是value字段
          if (response && response.hasOwnProperty('url')) {
            resolve(response.url);
          } else {
            console.warn('获取调试模式配置失败，使用默认值true:', response);
            resolve(true);
          }
        });
      });
    } catch (error) {
      console.warn('获取调试模式配置异常，使用默认值true:', error);
      actualDebugMode = true;
    }
  }

  // 构建完整的请求参数信息
  const requestInfo = {
    url: url,
    method: 'POST',
    headers: headers,
    payload: payload,
    datetime: formatDateTime(new Date()),
    debug_mode: actualDebugMode
    //debug_mode: false
  }

  console.log("------------------上传商品到N11调试数据开始---------------------");
  console.log('请求URL:', url);
  console.log('请求方式:', 'POST');
  console.log('请求头:', headers);
  console.log('请求载荷:', payload);
  console.log('调试模式:', actualDebugMode);
  console.log("------------------上传商品到N11调试数据结束---------------------");

  // 保存上传参数到数据库（无论调试模式是否开启）
  if (taskDetailId) {
    try {
      // 动态导入saveUploadParams以避免循环依赖
      const { saveUploadParams } = await import('./taskApi')
      await saveUploadParams(taskDetailId, 'n11', JSON.stringify(requestInfo))
      console.log('上传参数已保存到数据库')
    } catch (error) {
      console.error('保存上传参数失败:', error)
      // 不影响主流程，继续执行
    }
  }

  // 如果是调试模式，返回模拟结果
  if (actualDebugMode) {
    console.log('调试模式：不真正调用N11 API，返回模拟结果')
    return {
      id: Math.floor(Math.random() * 1000000) + 1000000, // 模拟任务ID
      type: 'PRODUCT_CREATE',
      status: 'IN_QUEUE',
      reasons: ['调试模式 - 模拟上传成功']
    } as N11ProductUploadResponse
  }

  // 正常模式：真正调用N11 API
  try {
    const response = await sendRequestViaBackground({
      funName: 'uploadProductToN11',
      url,
      method: 'POST',
      headers,
      data: JSON.stringify(payload),
      auth: false,
      encrypto: false,
      timeout: 30000
    })

    console.log('N11商品上传响应:', response)
    console.log("------------------上传商品到N11响应数据开始---------------------");
    console.log(response);
    console.log("------------------上传商品到N11响应数据结束---------------------");

    // 成功响应格式处理
    if (response && response.id) {
      return {
        id: response.id,
        type: response.type,
        status: response.status,
        reasons: response.reasons
      } as N11ProductUploadResponse
    } else {
      // 如果没有id但有响应，可能是其他格式的成功响应
      return response as N11ProductUploadResponse
    }

  } catch (error: any) {
    console.error('N11商品上传失败:', error)
    
    // 处理错误信息
    let errorMessage = '上传失败'
    
    // 如果error是对象且包含响应数据
    if (error && typeof error === 'object') {
      // 检查是否有具体的错误信息
      if (error.errorMessage) {
        errorMessage = error.errorMessage
      } else if (error.exceptionType && error.errorCode) {
        errorMessage = `${error.exceptionType}: ${error.errorMessage || error.errorCode}`
      } else if (error.message) {
        errorMessage = error.message
      } else if (error.data && typeof error.data === 'object') {
        // 处理嵌套的错误数据
        if (error.data.errorMessage) {
          errorMessage = error.data.errorMessage
        } else if (error.data.exceptionType) {
          errorMessage = `${error.data.exceptionType}: ${error.data.errorMessage || error.data.errorCode || ''}`
        }
      }
    } else if (typeof error === 'string') {
      errorMessage = error
    }
    
    // 返回失败格式的响应
    return {
      error: errorMessage,
      response: JSON.stringify(error)
    } as N11ProductUploadResponse
  }
}

/**
 * 处理商品属性，根据分类属性和商品信息生成属性列表
 * @param categoryAttributes 分类属性列表
 * @param specKeyValues 商品规格键值对字符串
 * @param brandName 品牌名称
 * @returns 处理后的属性列表
 */
export const processProductAttributes = (
  categoryAttributes: N11CategoryAttribute[],
  specKeyValues: string,
  brandName: string
): ProductAttribute[] => {
  const attributes: ProductAttribute[] = []
  
  // 解析规格键值对
  const specMap = new Map<string, string>()
  if (specKeyValues) {
    const specs = specKeyValues.split(',')
    specs.forEach(spec => {
      const [key, value] = spec.split(':')
      if (key && value) {
        specMap.set(key.trim().toLowerCase(), value.trim())
      }
    })
  }

  // 遍历分类属性，只处理必填项
  categoryAttributes.forEach(attr => {
    if (!attr.isMandatory) return

    const attribute: ProductAttribute = {
      id: attr.attributeId,
      valueId: null,
      customValue: null
    }

    // 处理品牌属性
    if (attr.attributeName === 'Marka') {
      if (attr.isCustomValue) {
        attribute.customValue = brandName
      } else {
        // 如果不能自定义，取第一个值
        if (attr.attributeValues && attr.attributeValues.length > 0) {
          attribute.valueId = attr.attributeValues[0].id
        }
      }
    }
    // 处理尺码属性
    else if (attr.attributeName === 'Beden') {
      // 查找规格中的尺码信息
      const sizeKeys = ['boyut/ebat', 'beden', 'size']
      let sizeValue = ''
      
      for (const key of sizeKeys) {
        for (const [specKey, specVal] of specMap.entries()) {
          if (specKey.includes(key)) {
            sizeValue = specVal
            break
          }
        }
        if (sizeValue) break
      }

      if (sizeValue && attr.attributeValues) {
        // 在属性值中查找匹配的尺码
        const matchedValue = attr.attributeValues.find(val => 
          val.value.toLowerCase() === sizeValue.toLowerCase()
        )
        if (matchedValue) {
          attribute.valueId = matchedValue.id
        }
      }

      // 如果没找到匹配的值且可以自定义
      if (!attribute.valueId && attr.isCustomValue && sizeValue) { 
        attribute.customValue = sizeValue
      }
    }
    // 处理其他属性
    else {
      // 检查规格中是否有对应的属性
      const attrNameLower = attr.attributeName.toLowerCase()
      let matchedSpecValue = ''
      
      for (const [specKey, specVal] of specMap.entries()) {
        if (specKey.includes(attrNameLower) || attrNameLower.includes(specKey)) {
          matchedSpecValue = specVal
          break
        }
      }

      if (matchedSpecValue) {
        if (attr.isCustomValue) {
          attribute.customValue = matchedSpecValue
        } else if (attr.attributeValues) {
          // 在属性值中查找匹配项
          const matchedValue = attr.attributeValues.find(val => 
            val.value.toLowerCase() === matchedSpecValue.toLowerCase()
          )
          if (matchedValue) {
            attribute.valueId = matchedValue.id
          }
        }
      }

      // 如果没有匹配的规格值，使用默认值
      if (!attribute.valueId && !attribute.customValue) {
        if (attr.isCustomValue) {
          // 对于必填属性，如果可以自定义但没有匹配值，优先取第一个预定义值
          if (attr.attributeValues && attr.attributeValues.length > 0) {
            attribute.valueId = attr.attributeValues[0].id
          } else {
            // 如果没有预定义值，设置为空字符串（但这种情况很少见）
            attribute.customValue = ''
          }
        } else if (attr.attributeValues && attr.attributeValues.length > 0) {
          // 不能自定义，取第一个值
          attribute.valueId = attr.attributeValues[0].id
        }
      }
    }

    attributes.push(attribute)
  })

  return attributes
} 