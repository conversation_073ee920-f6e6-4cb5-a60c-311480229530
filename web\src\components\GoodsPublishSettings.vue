<template>
  <div class="goods-publish-settings">
    <!-- 发布说明 -->
    <div class="publish-instructions">
      <h4>商品发布说明</h4>
      <ol>
        <li>同一店铺已发布过的商品，不会重复发布</li>
        <li>任务执行期间请保持浏览器打开状态，电脑屏幕常亮，不自动熄屏</li>
        <li>
          上传数量 可上传数量: <span class="info-value">{{ userStoreInfo.number_all || 0 }}</span> 已上传数量: <span class="info-value used">{{ userStoreInfo.number_used || 0 }}</span> 剩余上传数量:<span class="info-value left">{{ userStoreInfo.number_left || 0 }}</span>
        </li>
      </ol>
    </div>

    <!-- 设置表单 -->
    <el-form
      ref="publishFormRef"
      :model="publishForm"
      :rules="publishFormRules"
      label-width="120px"
      class="publish-form"
    >
      <!-- 当前目录显示 -->
      <el-form-item v-if="currentDirectoryName" label="商品目录">
        <div class="directory-info">
          <el-icon><Folder /></el-icon>
          <span class="directory-name">{{ currentDirectoryName }}</span>
        </div>
      </el-form-item>

      <!-- 选中商品显示区域 -->
      <el-form-item v-if="isSelectedMode" label="选择的商品">
        <div class="selected-goods-container">
          <div 
            v-for="goods in selectedGoods.slice(0, 3)" 
            :key="goods.id"
            class="selected-goods-item"
          >
            <div class="goods-image">
              <el-image
                :src="goods.goods_thumb"
                fit="cover"
                style="width: 50px; height: 50px; border-radius: 4px;"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
            <div class="goods-info">
              <div class="goods-name" :title="goods.goods_name">{{ goods.goods_name }}</div>
              <div class="goods-category">{{ goods.front_cat_2_path_name || '未分类' }}</div>
            </div>
          </div>
          <!-- 显示总商品数量提示 -->
          <div v-if="selectedGoods.length > 3" class="goods-count-tip">
            等共 <strong>{{ selectedGoods.length }}</strong> 个商品
          </div>
          <div v-else-if="selectedGoods.length > 0" class="goods-count-tip">
            共 <strong>{{ selectedGoods.length }}</strong> 个商品
          </div>
        </div>
      </el-form-item>

      <!-- 商品添加时间 - 仅在非选中模式显示 -->
      <el-form-item v-if="!isSelectedMode" label="商品添加时间" prop="timeRange">
        <el-radio-group v-model="publishForm.timeRange" @change="handleTimeRangeChange">
          <el-radio value="all">全部</el-radio>
          <el-radio value="today">今日</el-radio>
          <el-radio value="yesterday">昨日</el-radio>
          <el-radio value="lastweek">近一周</el-radio>
          <el-radio value="custom">自定义</el-radio>
        </el-radio-group>
        
        <!-- 自定义时间范围 -->
        <div v-if="publishForm.timeRange === 'custom'" class="custom-time-range">
          <el-date-picker
            v-model="publishForm.customDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            style="margin-top: 10px; width: 320px;"
            @change="handleCustomDateRangeChange"
          />
        </div>
      </el-form-item>

      <!-- 商品统计信息 - 独立区域 -->
      <el-form-item v-if="!isSelectedMode" label="商品统计" class="statistics-form-item">
        <div class="goods-statistics-card">
          <div v-if="statisticsLoading" class="statistics-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在统计商品数量...</span>
          </div>
          <div v-else-if="statisticsData" class="statistics-info">
            <div class="statistics-item">
              <el-icon><Goods /></el-icon>
              <span class="label">商品数量：</span>
              <span class="value">{{ statisticsData.goods_count }}</span>
            </div>
            <div class="statistics-item">
              <el-icon><SuitcaseLine /></el-icon>
              <span class="label">SKU数量：</span>
              <span class="value">{{ statisticsData.sku_count }}</span>
            </div>
          </div>
          <div v-else class="statistics-empty">
            <el-icon><DataAnalysis /></el-icon>
            <span>暂无统计数据</span>
          </div>
        </div>
      </el-form-item>

      <!-- 商品排序 - 仅在非选中模式显示 -->
      <el-form-item v-if="!isSelectedMode" label="商品排序" prop="sortOrder">
        <el-radio-group v-model="publishForm.sortOrder">
          <el-radio value="desc">添加时间降序</el-radio>
          <el-radio value="asc">添加时间升序</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 发布设置 -->
      <el-form-item label="发布设置" prop="accountSettingType">
        <el-radio-group v-model="publishForm.accountSettingType" @change="handleSettingTypeChange">
          <el-radio :value="1">使用店铺设置</el-radio>
          <el-radio :value="2">自定义设置</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 自定义设置区域 -->
      <div v-if="publishForm.accountSettingType === 2" class="custom-settings-section">
        <!-- 价格设置 -->
        <el-form-item label="价格设置" class="price-setting-item">
          <div class="price-formula">
            <span class="formula-text">原价 ×</span>
            <el-input
              v-model="publishForm.priceRate"
              placeholder="倍数"
              class="price-input-underline"
              type="number"
              step="0.01"
              min="0"
              max="999.99"
            />
            <span class="formula-text formula-operator">+</span>
            <el-input
              v-model="publishForm.priceAdd"
              placeholder="加值"
              class="price-input-underline"
              type="number"
              step="0.01"
              min="0"
              max="999999.99"
            />
            <span class="formula-text formula-operator">−</span>
            <el-input
              v-model="publishForm.priceSubtract"
              placeholder="减值"
              class="price-input-underline"
              type="number"
              step="0.01"
              min="0"
              max="999999.99"
            />
          </div>
          <div class="price-tip">例如：原价100元，设置为 ×1.1 +5 −2，最终价格为 100×1.1+5−2=113元</div>
        </el-form-item>

        <!-- 库存数量 -->
        <el-form-item label="库存数量" prop="quantity">
          <el-input-number
            v-model="publishForm.quantity"
            :min="1"
            :max="9999"
            :precision="0"
            placeholder="请输入商品库存数量"
          />
        </el-form-item>

        <!-- 增值税率 -->
        <el-form-item label="增值税率" prop="vatRate">
          <el-select v-model="publishForm.vatRate" placeholder="请选择增值税率">
            <el-option label="0%" :value="0" />
            <el-option label="1%" :value="1" />
            <el-option label="10%" :value="10" />
            <el-option label="20%" :value="20" />
          </el-select>
        </el-form-item>

        <!-- 备货天数 -->
        <el-form-item label="备货天数" prop="preparingDay">
          <el-input-number
            v-model="publishForm.preparingDay"
            :min="1"
            :max="365"
            :precision="0"
            placeholder="请输入备货天数"
          />
          <span class="form-tip">天</span>
        </el-form-item>
      </div>

      <!-- 发布店铺 -->
      <el-form-item label="发布店铺" prop="selectedStores">
        <div class="store-selection" :class="{ scrollable: storeList.length > 9 }">
          <el-checkbox-group v-model="publishForm.selectedStores">
            <el-checkbox 
              v-for="store in storeList" 
              :key="store.id" 
              :value="store.id"
              class="store-checkbox"
            >
              {{ store.account_name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>  
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">
        确定
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, Folder, Loading, Goods, SuitcaseLine, DataAnalysis } from '@element-plus/icons-vue'
import { getUserStoreInfo, type UserStoreInfo } from '../utils/userApi'
import { addTask } from '../utils/taskApi'
import { getGoodsStatistics, type GoodsStatisticsParams, type GoodsStatisticsResponse } from '../utils/goodsStatisticsApi'

// 接口定义
interface Store {
  id: number
  account_name: string
}

interface PublishForm {
  timeRange: string
  customDateRange: string[]
  sortOrder: string
  selectedStores: number[]
  executeTime: string
  accountSettingType: number
  priceRate: number
  priceAdd: number
  priceSubtract: number
  quantity: number
  vatRate: number
  preparingDay: number
}

interface SelectedGoods {
  id: number
  goods_name: string
  goods_thumb: string
  front_cat_2_path_name: string
}

// Props定义
const props = defineProps<{
  selectedGoods?: SelectedGoods[]
  currentDirectoryName?: string
  directoryId?: number
}>()

// 计算属性
const isSelectedMode = computed(() => {
  return props.selectedGoods && props.selectedGoods.length > 0
})

// 响应式数据
const submitting = ref(false)
const publishFormRef = ref()
const storeList = ref<Store[]>([])
const userStoreInfo = ref<UserStoreInfo>({
  is_admin: 0,
  phone: '',
  is_vip: 0,
  vip_end_time: '',
  number_all: 0,
  number_used: 0,
  number_left: 0,
  store_list: []
})

// 统计相关数据
const statisticsLoading = ref(false)
const statisticsData = ref<GoodsStatisticsResponse | null>(null)
let statisticsTimer: ReturnType<typeof setTimeout> | null = null

// 表单数据
const publishForm = reactive<PublishForm>({
  timeRange: 'today',
  customDateRange: [],
  sortOrder: 'desc',
  selectedStores: [],
  executeTime: 'manual',
  accountSettingType: 1, // 默认使用店铺设置
  priceRate: 1.00,
  priceAdd: 0.00,
  priceSubtract: 0.00,
  quantity: 1000,
  vatRate: 0,
  preparingDay: 3
})

// 计算日期限制
const disabledDate = (time: Date) => {
  const today = new Date();  
  today.setHours(0, 0, 0, 0);

  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(today.getMonth() - 5);
  sixMonthsAgo.setDate(1);
  sixMonthsAgo.setHours(0, 0, 0, 0);

  // 对于开始日期，禁用早于六个月前的日期
  // 对于结束日期，禁用晚于今天的日期
  // Element Plus 的 daterange picker 会自动处理开始/结束日期的联动禁用
  return time.getTime() < sixMonthsAgo.getTime() || time.getTime() > today.getTime();
};

// 表单验证规则 - 根据模式动态调整
const publishFormRules = computed(() => {
  const baseRules = {
    selectedStores: [
      { 
        required: true, 
        type: 'array', 
        min: 1, 
        message: '请至少选择一个店铺', 
        trigger: 'change' 
      }
    ],
    accountSettingType: [
      { required: true, message: '请选择发布设置', trigger: 'change' }
    ]
  }

  // 如果不是选中模式，添加时间和排序的验证规则
  if (!isSelectedMode.value) {
    return {
      ...baseRules,
      timeRange: [{ required: true, message: '请选择商品添加时间', trigger: 'change' }],
      sortOrder: [{ required: true, message: '请选择商品排序', trigger: 'change' }]
    }
  }

  return baseRules
})

// 事件定义
const emit = defineEmits<{
  close: []
  success: []  
}>()

// 获取用户店铺信息
const loadUserStoreInfo = async () => {
  try {
    const response = await getUserStoreInfo()
    console.log('获取用户店铺信息成功:', response)
    userStoreInfo.value = response
    storeList.value = response.store_list || []
  } catch (error) {
    console.error('获取用户店铺信息失败:', error)
    ElMessage.error('获取用户店铺信息失败')
  }
}

// 处理时间范围变化
const handleTimeRangeChange = (value: string) => {
  if (value !== 'custom') {
    publishForm.customDateRange = []
  }
  // 触发商品统计更新
  updateGoodsStatistics()
}

// 处理自定义日期范围变化
const handleCustomDateRangeChange = (value: string[]) => {
  // 触发商品统计更新
  updateGoodsStatistics()
}

// 更新商品统计
const updateGoodsStatistics = async () => {
  // 只在非选中模式下进行统计
  if (isSelectedMode.value || !props.directoryId) {
    return
  }

  // 清除之前的定时器
  if (statisticsTimer) {
    clearTimeout(statisticsTimer)
  }

  // 防抖处理，避免频繁请求
  statisticsTimer = setTimeout(async () => {
    try {
      statisticsLoading.value = true
      statisticsData.value = null

      // 构建统计参数
      const params: GoodsStatisticsParams = {
        directory_id: props.directoryId!,
        time_range: publishForm.timeRange
      }

      // 根据时间范围设置参数
      if (publishForm.timeRange === 'today') {
        const today = new Date().toISOString().split('T')[0]
        params.day_start = today
        params.day_end = today
      } else if (publishForm.timeRange === 'yesterday') {
        const yesterday = new Date()
        yesterday.setDate(yesterday.getDate() - 1)
        const yesterdayStr = yesterday.toISOString().split('T')[0]
        params.day_start = yesterdayStr
        params.day_end = yesterdayStr
      } else if (publishForm.timeRange === 'lastweek') {
        const today = new Date()
        const lastWeek = new Date()
        lastWeek.setDate(today.getDate() - 7)
        params.day_start = lastWeek.toISOString().split('T')[0]
        params.day_end = today.toISOString().split('T')[0]
      } else if (publishForm.timeRange === 'custom' && publishForm.customDateRange.length === 2) {
        params.day_start = publishForm.customDateRange[0]
        params.day_end = publishForm.customDateRange[1]
      }
      // 如果是 'all'，不设置具体的日期参数

      const response = await getGoodsStatistics(params)
      statisticsData.value = response
    } catch (error) {
      console.error('获取商品统计失败:', error)
      statisticsData.value = null
    } finally {
      statisticsLoading.value = false
    }
  }, 500) // 500ms 防抖
}

// 处理设置类型变化
const handleSettingTypeChange = (value: number) => {
  if (value === 1) {
    // 使用店铺设置时，重置自定义设置为默认值
    publishForm.priceRate = 1.00
    publishForm.priceAdd = 0.00
    publishForm.priceSubtract = 0.00
    publishForm.quantity = 1000
    publishForm.vatRate = 0
    publishForm.preparingDay = 3
  }
}

// 取消操作
const handleCancel = () => {
  emit('close')
}

// 确认操作
const handleConfirm = async () => {
  // 防重复提交检查
  if (submitting.value) {
    ElMessage.warning('正在提交中，请勿重复操作')
    return
  }

  try {
    await publishFormRef.value.validate()

    // 检查自定义时间范围（仅在非选中模式下）
    if (!isSelectedMode.value && publishForm.timeRange === 'custom' && publishForm.customDateRange.length !== 2) {
      ElMessage.warning('请选择自定义时间范围')
      return
    }

    // 验证选中的店铺数据
    const uniqueStores = [...new Set(publishForm.selectedStores)]
    if (uniqueStores.length !== publishForm.selectedStores.length) {
      console.warn('检测到重复的店铺选择，已自动去重:', publishForm.selectedStores, '去重后:', uniqueStores)
      publishForm.selectedStores = uniqueStores
    }

    // 构建确认消息
    const selectedStoreNames = storeList.value
      .filter(store => publishForm.selectedStores.includes(store.id))
      .map(store => store.account_name)
      .join('、')

    let confirmMessage = ''

    if (isSelectedMode.value) {
      // 选中商品模式的确认消息
      confirmMessage = `
        <div style="text-align: left; line-height: 1.6;">
          <p>• 商品目录：${props.currentDirectoryName || '未知目录'}</p>
          <p>• 发布商品：已选择 ${props.selectedGoods!.length} 个商品</p>
          <p>• 发布店铺：${selectedStoreNames}</p>
          <p>• 发布设置：${publishForm.accountSettingType === 1 ? '使用店铺设置' : '自定义设置'}</p>
          ${publishForm.accountSettingType === 2 ? `
          <p>• 价格设置：×${publishForm.priceRate} +${publishForm.priceAdd} −${publishForm.priceSubtract}</p>
          <p>• 库存数量：${publishForm.quantity}</p>
          <p>• 增值税率：${publishForm.vatRate}%</p>
          <p>• 备货天数：${publishForm.preparingDay}天</p>
          ` : ''}
          
          <br>
          <p style="color: #e6a23c;"><strong>注意：</strong>任务执行期间请保持浏览器打开状态，电脑屏幕常亮，不自动熄屏。</p>
        </div>
      `
    } else {
      // 普通模式的确认消息
      let timeRangeText = ''
      switch (publishForm.timeRange) {
        case 'today':
          timeRangeText = '今日'
          break
        case 'yesterday':
          timeRangeText = '昨日'
          break
        case 'lastweek':
          timeRangeText = '近一周'
          break
        case 'custom':
          timeRangeText = `${publishForm.customDateRange[0]} 至 ${publishForm.customDateRange[1]}`
          break
        default:
          timeRangeText = '全部'
      }

      const sortOrderText = publishForm.sortOrder === 'desc' ? '按添加时间降序' : '按添加时间升序'

      confirmMessage = `
        <div style="text-align: left; line-height: 1.6;">
          <p>• 商品目录：${props.currentDirectoryName || '未知目录'}</p>
          <p>• 商品添加时间：${timeRangeText}</p>
          <p>• 商品排序：${sortOrderText}</p>
          <p>• 发布店铺：${selectedStoreNames}</p>
          <p>• 发布设置：${publishForm.accountSettingType === 1 ? '使用店铺设置' : '自定义设置'}</p>
          ${publishForm.accountSettingType === 2 ? `
          <p>• 价格设置：×${publishForm.priceRate} +${publishForm.priceAdd} −${publishForm.priceSubtract}</p>
          <p>• 库存数量：${publishForm.quantity}</p>
          <p>• 增值税率：${publishForm.vatRate}%</p>
          <p>• 备货天数：${publishForm.preparingDay}天</p>
          ` : ''}
          
          <br>
          <p style="color: #e6a23c;"><strong>注意：</strong>任务执行期间请保持浏览器打开状态，电脑屏幕常亮，不自动熄屏。</p>
        </div>
      `
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '确认发布',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    submitting.value = true

    // 构建提交数据
    const submitData: any = {
      selectedStores: publishForm.selectedStores,
      executeTime: publishForm.executeTime,
      directory_id: props.directoryId || 0,
      account_setting_type: publishForm.accountSettingType
    }

    // 如果是自定义设置，添加自定义参数
    if (publishForm.accountSettingType === 2) {
      submitData.price_rate = publishForm.priceRate
      submitData.price_add = publishForm.priceAdd
      submitData.price_subtract = publishForm.priceSubtract
      submitData.quantity = publishForm.quantity
      submitData.vat_rate = publishForm.vatRate
      submitData.preparing_day = publishForm.preparingDay
    }

    if (isSelectedMode.value) {
      // 选中商品模式：传递选中的商品ID
      submitData.selected_ids = props.selectedGoods!.map(goods => goods.id)
    } else {
      // 普通模式：传递时间范围和排序
      submitData.timeRange = publishForm.timeRange
      submitData.customDateRange = publishForm.timeRange === 'custom' ? publishForm.customDateRange : undefined
      submitData.sortOrder = publishForm.sortOrder
    }

    // 提交任务到服务器
    console.log('准备提交任务数据:', submitData)
    console.log('选中的店铺列表:', publishForm.selectedStores)
    console.log('去重后的店铺列表:', [...new Set(publishForm.selectedStores)])
    
    await addTask(submitData)

    ElMessage.success('商品发布提交成功')
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交商品发布失败:', error)
    }
  } finally {
    submitting.value = false
  }
}

// 监听时间范围变化，自动更新统计
watch(
  () => [publishForm.timeRange, publishForm.customDateRange],
  () => {
    if (!isSelectedMode.value) {
      updateGoodsStatistics()
    }
  },
  { deep: true }
)

// 组件挂载时加载数据
onMounted(() => {
  loadUserStoreInfo()
  // 如果不是选中模式，初始化时获取统计数据
  if (!isSelectedMode.value) {
    updateGoodsStatistics()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (statisticsTimer) {
    clearTimeout(statisticsTimer)
    statisticsTimer = null
  }
})

// 定义组件名称
defineOptions({
  name: 'GoodsPublishSettings'
})
</script>

<script lang="ts">
export default {}
</script>

<style scoped>
.goods-publish-settings {
  padding: 10px 0;
}

.publish-instructions {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.publish-instructions h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;  
}

.publish-instructions ol {
  margin: 0;
  padding-left: 20px;
  color: #666;
  line-height: 1.6;
}

.publish-instructions li {
  margin-bottom: 5px;
}

.publish-form {
  margin-bottom: 20px;
}

.custom-time-range {
  width: 100%;
}

.directory-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: #e8f4fd;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #409eff;
  font-weight: 500;
}

.directory-name {
  font-size: 14px;
  line-height: 1.2;
}

.custom-settings-section {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin: 15px 0;
  border-left: 4px solid #409eff;
}

.price-formula {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.formula-text {
  color: #606266;
  font-weight: bold;
  white-space: nowrap;
  font-size: 14px;
}

.formula-operator {
  color: #409eff;
  font-size: 18px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d1ecf1;
}

.price-input-underline {
  width: 100px;
}

.price-input-underline :deep(.el-input__wrapper) {
  border: none;
  border-bottom: 2px solid #dcdfe6;
  border-radius: 0;
  box-shadow: none;
  padding: 8px 0;
  background: transparent;
  transition: border-color 0.3s;
}

.price-input-underline :deep(.el-input__wrapper:hover) {
  border-bottom-color: #c0c4cc;
}

.price-input-underline :deep(.el-input__wrapper.is-focus) {
  border-bottom-color: #409eff;
}

.price-input-underline :deep(.el-input__inner) {
  text-align: center;
  font-weight: 500;
  color: #303133;
}

.price-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.form-tip {
  margin-left: 8px;
  color: #909399;
}

.selected-goods-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: none;
  overflow: visible;
}

.selected-goods-container.scrollable {
  max-height: 300px; /* 增加容器高度以适应商品名称换行 */
  overflow-y: auto;
}

.selected-goods-item {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，以适应多行文本 */
  gap: 12px;
  padding: 12px; /* 增加内边距 */
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.selected-goods-item:last-child {
  margin-bottom: 0;
}

.goods-image {
  flex-shrink: 0;
}

.goods-image .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: #f5f7fa;
  color: #909399;
  font-size: 16px;
  border-radius: 4px;
}

.goods-info {
  flex: 1;
  min-width: 0;
  padding-right: 8px; /* 添加右侧内边距 */
}

.goods-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.4; /* 添加行高 */
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制最多显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word; /* 允许在任意字符间换行 */
}

.goods-category {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goods-count-tip {
  text-align: center;
  padding: 12px;
  margin-top: 8px;
  background: #f0f9ff;
  border: 1px solid #d1ecf1;
  border-radius: 4px;
  color: #409eff;
  font-size: 14px;
}

.goods-count-tip strong {
  font-weight: bold;
  color: #1890ff;
}

.store-selection {
  /* border: 1px solid #dcdfe6; */
  border-radius: 4px;
  padding: 10px;
  max-height: none;
  overflow: visible;
}

.store-selection .el-checkbox-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
}

.store-selection .el-checkbox-group .el-checkbox {
  margin: 0 !important;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: auto;
  line-height: normal;
}

.store-selection .el-checkbox-group .el-checkbox .el-checkbox__label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-selection.scrollable {
  max-height: 180px;
  overflow-y: auto;
}

.store-checkbox {
  display: flex;
  align-items: center;
  margin: 0 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.upload-info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  font-weight: bold;
  color: #333;
}

.info-value.used {
  color: #e6a23c;
}

.info-value.left {
  color: #67c23a;
}

.goods-statistics {
  margin-top: 15px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.statistics-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.statistics-info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.statistics-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.statistics-item .label {
  color: #666;
  font-size: 14px;
}

.statistics-item .value {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.goods-statistics-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
}

.goods-statistics-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.statistics-form-item {
  margin-bottom: 24px;
}

.statistics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
  padding: 10px 0;
}

.statistics-info {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: space-around;
}

.statistics-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f0f9ff;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.statistics-item:hover {
  background: #ecf5ff;
  transform: scale(1.02);
}

.statistics-item .el-icon {
  font-size: 20px;
  color: #409eff;
}

.statistics-item .label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.statistics-item .value {
  font-weight: bold;
  color: #409eff;
  font-size: 18px;
}

.statistics-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
}

.statistics-empty .el-icon {
  font-size: 32px;
  color: #c0c4cc;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .store-selection .el-checkbox-group {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .store-selection.scrollable {
    max-height: 150px;
  }
}

@media (max-width: 480px) {
  .store-selection .el-checkbox-group {
    grid-template-columns: 1fr;
  }
  
  .store-selection .el-checkbox-group .el-checkbox {
    justify-content: flex-start;
  }
}
</style>