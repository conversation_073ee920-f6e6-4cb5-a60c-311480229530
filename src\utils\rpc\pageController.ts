// 页面控制器文件 - 整合DOM操作和网络监听功能
declare const chrome: any;

import type { N11RejectedProduct } from '@/utils/n11/n11RejectedApi';
import { 
  setPageSize100, 
  clickNextPage, 
  getPageStatus, 
  isN11ProductListPage,
  waitForElement,
  type PageStatus,
  type DomOperationResult 
} from './domOperations';
import { 
  createNetworkListener, 
  convertCapturedDataToN11Products,
  type NetworkListener,
  type CapturedResponseData 
} from './networkListener';

/**
 * 页面控制器配置
 */
export interface PageControllerConfig {
  tabId?: number;
  maxRetries?: number;
  pageWaitTime?: number;
  requestTimeout?: number;
}

/**
 * 数据获取进度信息
 */
export interface DataFetchProgress {
  currentPage: number;
  totalPages: number;
  processedItems: number;
  totalItems: number;
  message: string;
}

/**
 * 数据获取结果
 */
export interface DataFetchResult {
  success: boolean;
  message: string;
  data: N11RejectedProduct[];
  totalCount: number;
}

/**
 * 页面控制器类
 */
export class PageController {
  private config: Required<PageControllerConfig>;
  private tabId: number;
  private networkListener: NetworkListener | null = null;
  private isRunning: boolean = false;
  private capturedData: CapturedResponseData[] = [];

  constructor(config: PageControllerConfig = {}) {
    this.config = {
      tabId: config.tabId || 0,
      maxRetries: config.maxRetries || 3,
      pageWaitTime: config.pageWaitTime || 2000,
      requestTimeout: config.requestTimeout || 10000
    };
    this.tabId = this.config.tabId;
  }

  /**
   * 获取所有被拒绝商品数据
   */
  async getAllRejectedProducts(
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<DataFetchResult> {
    if (this.isRunning) {
      return {
        success: false,
        message: '数据获取任务已在运行中',
        data: [],
        totalCount: 0
      };
    }

    this.isRunning = true;
    this.capturedData = [];

    try {
      // 1. 获取当前活动标签页
      const tabResult = await this.getCurrentActiveTab();
      if (!tabResult.success) {
        return {
          success: false,
          message: tabResult.message,
          data: [],
          totalCount: 0
        };
      }

      this.tabId = tabResult.tabId!;
      console.log('使用标签页ID:', this.tabId);

      // 2. 验证页面环境
      const isValidPage = await isN11ProductListPage(this.tabId);
      if (!isValidPage) {
        return {
          success: false,
          message: '当前页面不是N11产品列表页面，请先导航到正确的页面',
          data: [],
          totalCount: 0
        };
      }

      if (onProgress) {
        onProgress(0, 0, '正在初始化网络监听器...');
      }

      // 3. 启动网络监听器（启用智能冲突检测和处理）
      this.networkListener = createNetworkListener({ 
        tabId: this.tabId,
        forceDetach: true // 启用智能debugger冲突检测和处理
      });
      
      const startResult = await this.networkListener.start((data) => {
        this.capturedData.push(data);
        console.log('捕获到新的响应数据');
      });

      if (!startResult.success) {
        // 检查是否是debugger冲突错误，如果是，提供更友好的错误信息
        const errorMsg = startResult.message || '';
        if (errorMsg.includes('Another debugger is already attached') || 
            errorMsg.includes('already attached')) {
          
          console.warn('检测到debugger冲突:', errorMsg);
          
          // 如果有回退模式，返回对应信息
          if (startResult.fallbackMode) {
            return {
              success: false,
              message: `网络监听器启动失败，建议刷新页面后重试: ${errorMsg}`,
              data: [],
              totalCount: 0
            };
          }
        }
        
        return {
          success: false,
          message: `启动网络监听器失败: ${startResult.message}`,
          data: [],
          totalCount: 0
        };
      }

      if (onProgress) {
        onProgress(0, 0, '正在设置每页显示数量...');
      }

      // 4. 设置每页显示100条
      const pageSizeResult = await this.setPageSizeWithRetry();
      if (!pageSizeResult.success) {
        console.warn('设置页面大小失败，继续使用默认设置:', pageSizeResult.message);
      }

      // 等待页面更新
      await this.delay(this.config.pageWaitTime);

      if (onProgress) {
        onProgress(0, 0, '正在获取页面信息...');
      }

      // 5. 获取页面状态信息
      const pageStatus = await getPageStatus(this.tabId);
      if (!pageStatus) {
        return {
          success: false,
          message: '无法获取页面状态信息',
          data: [],
          totalCount: 0
        };
      }

      console.log('页面状态:', pageStatus);

      // 6. 等待第一页数据加载
      await this.waitForPageData(1);

      // 7. 遍历所有分页
      const fetchResult = await this.fetchAllPages(pageStatus, onProgress);
      
      // 8. 转换数据格式
      const allProducts = convertCapturedDataToN11Products(this.capturedData);

      return {
        success: fetchResult.success,
        message: fetchResult.message,
        data: allProducts,
        totalCount: allProducts.length
      };

    } catch (error: any) {
      console.error('获取数据过程中发生错误:', error);
      return {
        success: false,
        message: `获取数据失败: ${error.message}`,
        data: [],
        totalCount: 0
      };
    } finally {
      await this.cleanup();
      this.isRunning = false;
    }
  }

  /**
   * 获取当前活动标签页
   */
  private async getCurrentActiveTab(): Promise<{ success: boolean; message: string; tabId?: number }> {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs: any[]) => {
        if (chrome.runtime.lastError) {
          resolve({
            success: false,
            message: `获取活动标签页失败: ${chrome.runtime.lastError.message}`
          });
          return;
        }

        if (!tabs || tabs.length === 0) {
          resolve({
            success: false,
            message: '未找到活动标签页'
          });
          return;
        }

        const activeTab = tabs[0];
        resolve({
          success: true,
          message: '成功获取活动标签页',
          tabId: activeTab.id
        });
      });
    });
  }

  /**
   * 设置页面大小（带重试）
   */
  private async setPageSizeWithRetry(): Promise<DomOperationResult> {
    for (let i = 0; i < this.config.maxRetries; i++) {
      try {
        const result = await setPageSize100(this.tabId);
        if (result.success) {
          return result;
        }
        
        console.log(`第 ${i + 1} 次设置页面大小失败:`, result.message);
        
        if (i < this.config.maxRetries - 1) {
          await this.delay(1000);
        }
      } catch (error: any) {
        console.error(`第 ${i + 1} 次设置页面大小异常:`, error);
        if (i === this.config.maxRetries - 1) {
          return { success: false, message: `设置失败: ${error.message}` };
        }
      }
    }

    return { success: false, message: '设置页面大小失败，已达到最大重试次数' };
  }

  /**
   * 等待页面数据加载
   */
  private async waitForPageData(expectedPage: number): Promise<boolean> {
    const maxWaitTime = 20000; // 增加到20秒，避免超时问题
    const checkInterval = 500;
    const startTime = Date.now();

    console.log(`开始等待第 ${expectedPage} 页数据加载...`);

    let lastDataCount = this.capturedData.length;

    while (Date.now() - startTime < maxWaitTime) {
      // 检查是否有新的响应数据
      if (this.capturedData.length > lastDataCount) {
        const latestData = this.capturedData[this.capturedData.length - 1];
        if (latestData.timestamp > startTime) {
          console.log(`第 ${expectedPage} 页数据已加载，响应时间戳: ${latestData.timestamp}`);
          return true;
        }
      }

      // 检查页面状态是否已更新到期望页码
      try {
        const currentPageStatus = await getPageStatus(this.tabId);
        if (currentPageStatus && currentPageStatus.currentPage === expectedPage) {
          console.log(`页面状态已更新到第 ${expectedPage} 页`);
          // 再等待一点时间确保数据完全加载
          await this.delay(1000);
          return true;
        }
      } catch (error) {
        console.warn('检查页面状态时出错:', error);
      }

      await this.delay(checkInterval);
    }

    console.warn(`等待第 ${expectedPage} 页数据超时（${maxWaitTime}ms）`);

    // 超时后检查是否至少有一些数据
    if (this.capturedData.length > lastDataCount) {
      console.log('虽然超时，但检测到有新数据，继续处理');
      return true;
    }

    return false;
  }

  /**
   * 获取所有分页数据
   */
  private async fetchAllPages(
    initialPageStatus: PageStatus,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<{ success: boolean; message: string }> {
    try {
      let currentPage = initialPageStatus.currentPage;
      let totalPages = initialPageStatus.totalPages; // 初始值，会从API响应中更新

      console.log(`开始获取分页数据，当前页: ${currentPage}，初始总页数: ${totalPages}`);

      // 等待第一页的API响应，从中获取真实的总页数
      await this.delay(2000); // 等待API响应

      // 从捕获的数据中获取真实的总页数
      const realTotalPages = this.extractTotalPagesFromCapturedData();
      if (realTotalPages > 0) {
        totalPages = realTotalPages;
        console.log(`从API响应中获取到真实总页数: ${totalPages}`);
      } else {
        console.warn('无法从API响应中获取总页数，将使用动态检测方式遍历所有页面');
        // 设置一个较大的值，实际遍历时会动态检测是否到达最后一页
        totalPages = 999;
      }

      // 报告初始进度
      if (onProgress) {
        onProgress(currentPage, totalPages, `正在处理第 ${currentPage} 页...`);
      }

      // 如果总页数大于1，需要遍历其他页面
      let processedPages = currentPage;
      let actualTotalPages = totalPages; // 实际总页数，可能会在遍历过程中更新

      while (processedPages < actualTotalPages) {
        const nextPage = processedPages + 1;

        // 检查当前页面状态，确保没有到达最后一页
        const currentPageStatus = await getPageStatus(this.tabId);
        if (currentPageStatus) {
          const currentActivePageNum = currentPageStatus.currentPage;
          console.log(`当前激活页码: ${currentActivePageNum}`);

          // 如果使用动态检测模式（totalPages=999），尝试从最新的API响应更新总页数
          if (actualTotalPages === 999) {
            const latestTotalPages = this.extractTotalPagesFromCapturedData();
            if (latestTotalPages > 0 && latestTotalPages < 999) {
              actualTotalPages = latestTotalPages;
              console.log(`动态更新总页数为: ${actualTotalPages}`);
            }
          }

          // 如果当前激活页码等于实际总页数，说明已到最后一页
          if (actualTotalPages < 999 && currentActivePageNum >= actualTotalPages) {
            console.log('已到达最后一页，停止分页');
            break;
          }

          // 检查是否还有下一页按钮可用
          if (!currentPageStatus.hasNextPage) {
            console.log('下一页按钮不可用，停止分页');
            break;
          }
        }

        if (onProgress) {
          const displayTotalPages = actualTotalPages === 999 ? '未知' : actualTotalPages.toString();
          onProgress(processedPages, actualTotalPages === 999 ? processedPages + 1 : actualTotalPages,
                    `正在切换到第 ${nextPage} 页...（共 ${displayTotalPages} 页）`);
        }

        // 点击下一页
        const clickResult = await this.clickNextPageWithRetry();
        if (!clickResult.success) {
          console.error(`切换到第 ${nextPage} 页失败:`, clickResult.message);

          // 如果点击失败，检查是否已到最后一页
          const finalPageStatus = await getPageStatus(this.tabId);
          if (finalPageStatus && finalPageStatus.currentPage >= totalPages) {
            console.log('点击失败但已到最后一页，结束分页');
            break;
          }

          // 否则继续尝试下一页
          console.log('点击失败，尝试继续下一页');
          processedPages++;
          continue;
        }

        // 等待页面加载
        await this.delay(this.config.pageWaitTime);

        // 等待数据加载
        const dataLoaded = await this.waitForPageData(nextPage);
        if (!dataLoaded) {
          console.warn(`第 ${nextPage} 页数据加载超时`);
        }

        processedPages = nextPage;

        if (onProgress) {
          const displayTotalPages = actualTotalPages === 999 ? '未知' : actualTotalPages.toString();
          onProgress(processedPages, actualTotalPages === 999 ? processedPages : actualTotalPages,
                    `已处理第 ${processedPages} 页（共 ${displayTotalPages} 页）`);
        }

        console.log(`第 ${processedPages} 页处理完成`);
      }

      // 最终确定实际总页数
      const finalTotalPages = actualTotalPages === 999 ? processedPages : actualTotalPages;

      return {
        success: true,
        message: `成功获取 ${processedPages} 页数据（共 ${finalTotalPages} 页）`
      };

    } catch (error: any) {
      console.error('获取分页数据失败:', error);
      return {
        success: false,
        message: `获取分页数据失败: ${error.message}`
      };
    }
  }

  /**
   * 从捕获的API响应数据中提取总页数
   */
  private extractTotalPagesFromCapturedData(): number {
    try {
      if (this.capturedData.length === 0) {
        console.log('没有捕获到API响应数据');
        return 0;
      }

      // 查找最新的API响应数据
      const latestData = this.capturedData[this.capturedData.length - 1];

      // 修正：使用responseBody而不是responseData
      if (latestData && latestData.responseBody) {
        const responseData = latestData.responseBody;

        console.log('分析API响应数据结构:', {
          hasResponseBody: !!latestData.responseBody,
          responseKeys: Object.keys(responseData),
          totalPages: responseData.totalPages,
          totalElements: responseData.totalElements,
          pageSize: responseData.pageSize,
          size: responseData.size,
          numberOfElements: responseData.numberOfElements
        });

        // 方法1：直接检查totalPages字段
        if (responseData.totalPages && typeof responseData.totalPages === 'number') {
          console.log('从API响应中提取到总页数:', responseData.totalPages);
          return responseData.totalPages;
        }

        // 方法2：检查pageable对象中的totalPages
        if (responseData.pageable && responseData.pageable.totalPages) {
          console.log('从pageable中提取到总页数:', responseData.pageable.totalPages);
          return responseData.pageable.totalPages;
        }

        // 方法3：通过totalElements和size计算总页数
        if (responseData.totalElements && responseData.size) {
          const calculatedPages = Math.ceil(responseData.totalElements / responseData.size);
          console.log('通过totalElements和size计算总页数:', calculatedPages,
                     `(${responseData.totalElements} / ${responseData.size})`);
          return calculatedPages;
        }

        // 方法4：通过totalElements和pageSize计算总页数
        if (responseData.totalElements && responseData.pageSize) {
          const calculatedPages = Math.ceil(responseData.totalElements / responseData.pageSize);
          console.log('通过totalElements和pageSize计算总页数:', calculatedPages,
                     `(${responseData.totalElements} / ${responseData.pageSize})`);
          return calculatedPages;
        }

        // 方法5：检查是否有numberOfElements，如果等于size且size小于默认值，可能是最后一页
        if (responseData.numberOfElements && responseData.size) {
          console.log('当前页元素数量:', responseData.numberOfElements, '页面大小:', responseData.size);
          // 如果当前页元素数量小于页面大小，可能是最后一页，但这不能确定总页数
        }

        console.log('完整的API响应数据结构:', JSON.stringify(responseData, null, 2));
      }

      console.warn('无法从API响应中提取总页数，将使用动态检测方式');
      return 0;
    } catch (error) {
      console.error('提取总页数时发生错误:', error);
      return 0;
    }
  }

  /**
   * 点击下一页（带重试）
   */
  private async clickNextPageWithRetry(): Promise<DomOperationResult> {
    for (let i = 0; i < this.config.maxRetries; i++) {
      try {
        const result = await clickNextPage(this.tabId);
        if (result.success) {
          return result;
        }

        console.log(`第 ${i + 1} 次点击下一页失败:`, result.message);

        if (i < this.config.maxRetries - 1) {
          await this.delay(1000);
        }
      } catch (error: any) {
        console.error(`第 ${i + 1} 次点击下一页异常:`, error);
        if (i === this.config.maxRetries - 1) {
          return { success: false, message: `点击失败: ${error.message}` };
        }
      }
    }

    return { success: false, message: '点击下一页失败，已达到最大重试次数' };
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    try {
      if (this.networkListener) {
        await this.networkListener.stop();
        this.networkListener = null;
      }
      console.log('页面控制器资源已清理');
    } catch (error) {
      console.error('清理资源时发生错误:', error);
    }
  }

  /**
   * 停止数据获取
   */
  async stop(): Promise<void> {
    this.isRunning = false;
    await this.cleanup();
  }
}

/**
 * 创建页面控制器实例
 */
export function createPageController(config?: PageControllerConfig): PageController {
  return new PageController(config);
}

/**
 * 通过RPC方式获取所有被拒绝商品（便捷函数）
 */
export async function getAllRejectedProductsViaRPC(
  onProgress?: (current: number, total: number) => void
): Promise<N11RejectedProduct[]> {
  const controller = createPageController();
  
  const result = await controller.getAllRejectedProducts((current, total, message) => {
    console.log(`进度: ${current}/${total} - ${message}`);
    if (onProgress) {
      onProgress(current, total);
    }
  });

  if (!result.success) {
    throw new Error(result.message);
  }

  return result.data;
} 