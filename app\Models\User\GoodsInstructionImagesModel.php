<?php
declare(strict_types=1);

namespace App\Models\User;
use App\Models\BaseModel;
use App\Models\TeMu\ProductCategoryTeMuModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\TeMu\ProductCategoryWebPageTeMuModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\System\CatRelationSystemModel;

class GoodsInstructionImagesModel extends BaseModel
{
    protected $table = 'user_goods_instruction_images';
    
    protected $fillable = [
        'user_goods_id',
        'goods_id',
        'urls'
    ];

}