// 专门用于调试页面大小设置的简单工具
// 避免在模块级别声明chrome，在函数内部使用时再声明

/**
 * 简单的调试页面大小设置结果
 */
export interface SimpleDebugResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * 获取页面大小设置脚本函数
 * 返回一个可以在executeScript中使用的函数
 */
export function getPageSizeScript() {
  return () => {
    return new Promise((resolve) => {
      try {
        console.log('调试脚本: 开始执行页面大小设置');
        
        // 根据实际DOM结构查找100选项
        // 先找到下拉内容容器
        const dropdownContent = document.querySelector('.dropdown-content .simpleSelect-content') ||
                               document.querySelector('.simpleSelect-content') ||
                               document.querySelector('.dropdown-content');
        
        if (!dropdownContent) {
          console.log('调试脚本: 未找到下拉内容容器，尝试点击下拉按钮');
          
          // 如果没有找到展开的下拉菜单，先尝试点击下拉按钮
          const selectContainer = document.querySelector('.simpleSelect') || 
                                document.querySelector('[class*="select"]') ||
                                document.querySelector('.per-page-wrapper');
          
          if (!selectContainer) {
            resolve({ success: false, message: '未找到页面大小选择器容器' });
            return;
          }

          const dropdownButton = selectContainer.querySelector('button') ||
                                selectContainer.querySelector('.dropdown-toggle') ||
                                selectContainer.querySelector('[role="button"]') ||
                                selectContainer.querySelector('.simpleSelect-btn');
          
          if (!dropdownButton) {
            resolve({ success: false, message: '未找到下拉按钮' });
            return;
          }

          console.log('调试脚本: 点击下拉按钮');
          const buttonElement = dropdownButton as HTMLElement;
          buttonElement.click();
          
          // 等待下拉菜单出现后再查找选项
          setTimeout(() => {
            const newDropdownContent = document.querySelector('.dropdown-content .simpleSelect-content') ||
                                     document.querySelector('.simpleSelect-content') ||
                                     document.querySelector('.dropdown-content');
            
            if (!newDropdownContent) {
              resolve({ success: false, message: '点击后仍未找到下拉内容容器' });
              return;
            }
            
            console.log('调试脚本: 下拉菜单已展开，查找100选项');
            findAndClick100Option(newDropdownContent, resolve);
          }, 500);
        } else {
          console.log('调试脚本: 下拉菜单已展开，直接查找100选项');
          findAndClick100Option(dropdownContent, resolve);
        }
        
        // 查找并点击100选项的函数
        function findAndClick100Option(container: Element, resolveCallback: Function) {
          const optionButtons = container.querySelectorAll('li button[type="button"]') ||
                               container.querySelectorAll('button');
          
          console.log('调试脚本: 找到选项按钮数量:', optionButtons.length);
          
          let option100Button: Element | null = null;
          
          for (const button of Array.from(optionButtons)) {
            const text = button.textContent?.trim();
            console.log('调试脚本: 检查按钮文本:', text);
            if (text === '100 Ürün' || text === '100' || (text && text.includes('100'))) {
              option100Button = button;
              console.log('调试脚本: 找到100选项按钮:', button);
              break;
            }
          }
          
          if (!option100Button) {
            // 备用查找
            const foundButton = Array.from(document.querySelectorAll('button')).find(btn => 
              btn.textContent?.includes('100')
            );
            option100Button = foundButton || null;
            console.log('调试脚本: 备用查找结果:', option100Button);
          }
          
          if (option100Button) {
            console.log('调试脚本: 点击100选项按钮');
            const buttonElement = option100Button as HTMLElement;
            buttonElement.click();
            
            setTimeout(() => {
              resolveCallback({ 
                success: true, 
                message: '成功通过Background executeScriptInTab设置页面大小为100条' 
              });
            }, 1000);
          } else {
            resolveCallback({ 
              success: false, 
              message: '未找到100选项按钮' 
            });
          }
        }
        
      } catch (error: any) {
        console.error('调试脚本执行错误:', error);
        resolve({ 
          success: false, 
          message: `脚本执行错误: ${error.message}` 
        });
      }
    });
  };
}

/**
 * 通过executeScriptInTab设置页面大小为100条
 * 专门为Service Worker环境设计，避免全局对象引用
 */
export async function debugSetPageSize100(tabId: number): Promise<SimpleDebugResult> {
  try {
    console.log('调试: 开始通过executeScriptInTab设置页面大小为100条, tabId:', tabId);

    // 检查运行环境
    if (typeof globalThis === 'undefined') {
      return { success: false, message: '运行环境不支持globalThis' };
    }

    // 安全地获取chrome对象
    const chromeAPI = (globalThis as any).chrome;
    if (!chromeAPI || !chromeAPI.scripting || !chromeAPI.scripting.executeScript) {
      return { success: false, message: 'Chrome scripting API不可用' };
    }

    // 使用分离的脚本函数
    const scriptFunc = getPageSizeScript();

    const results = await chromeAPI.scripting.executeScript({
      target: { tabId },
      func: scriptFunc
    });

    if (results && results[0] && results[0].result) {
      const result = results[0].result;
      console.log('调试: executeScriptInTab执行结果:', result);
      return result;
    }

    return { success: false, message: '脚本执行无返回结果' };

  } catch (error: any) {
    console.error('调试: executeScriptInTab执行失败:', error);
    return {
      success: false,
      message: `executeScriptInTab执行失败: ${error.message}`
    };
  }
}