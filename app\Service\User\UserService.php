<?php
namespace App\Service\User;

use App\Exceptions\MyException;
use App\Models\Game\AdRewardGdtModel;
use App\Models\Game\AdRewardGromoreModel;
use App\Models\Game\AdRewardKsModel;
use App\Models\System\AdvertisementModel;
use App\Models\System\AdvertiserModel;
use App\Models\System\AppConfig;
use App\Models\System\WithdrawMoney;
use App\Models\User\AdminUser as AdminUserModel;
use App\Models\User\RedPacket as RedPacketModel;
use App\Models\User\User as UserModel;
use App\Models\User\UserAccountModel;
use App\Models\User\UserAdReward;
use App\Models\User\UserLogin as UserLoginModel;
use App\Models\User\UserMaxAdModel;
use App\Models\User\UserRelation as UserRelationModel;
use App\Models\User\Withdraw;
use App\Models\User\WithdrawReward;
use App\Service\BaseService;
use App\Utils\Jwt\Jwt;
use App\Utils\Tools;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UserService extends BaseService{

    public $jwtService;
    public function __construct(Jwt $jwtService)
    {
        $this->jwtService = $jwtService;
    }

    //绑定设备用
    public function handleRegOrLoginRecord($user_id,$admin_id,$bind_type,$login_info=[]){}  

    public function userAdInfo($user_id=0){}

    /**
     * 格式化小数，去除末尾多余的0
     * @param float $number 要格式化的数字
     * @param int $decimals 最大小数位数
     * @return string|int 格式化后的数字
     */
    private function formatDecimal($number, $decimals)
    {
        // 使用number_format格式化到指定小数位
        $formatted = number_format((float)$number, $decimals, '.', '');
        
        // 去掉末尾的0
        $formatted = rtrim($formatted, '0');
        
        // 如果最后是小数点，去掉小数点
        $formatted = rtrim($formatted, '.');
        
        return $formatted;
    }

    public function rewardGdt($user){}

    public function rewardGdtExecute($data){}

    public function rewardKs($user){}

    public function rewardKsExecute($data){
        $user_id = $data['user_id'];
        $trans_id = $data['trans_id'];
        $money = (int)$data['money'];
        
        $update_reward_ks = 0;
        $update_reward_ks_result = '';

        // 使用事务和状态更新来处理并发
        DB::beginTransaction();
        try {
            // 获取排他锁
            $reward_record = AdRewardKsModel::where('trans_id', $trans_id)
                            ->lockForUpdate()
                            ->first();
            if(!$reward_record) {
                throw new MyException('trans_id:'.$trans_id.'不存在');
            }

            if($reward_record->status != 0) {
                throw new MyException('trans_id:'.$trans_id.'奖励已被处理,请勿重复领取');
            }

            // 更新状态
            $reward_record->status = 2;
            $reward_record->save();

            $reward_record = $reward_record->toArray();

            if($money != (int)$reward_record['reward_amount']){
                $update_reward_ks_result = '奖励金额错误';
                $update_reward_ks = 1;
                throw new MyException('trans_id:'.$trans_id.'奖励金额错误');
            }

            if($reward_record['status'] != 2){
                throw new MyException('trans_id:'.$trans_id.'该奖励已操作');
            }

            $data['reward_id'] = $reward_record['id'];

            $user = UserModel::find($user_id);
            if(!$user){
                $update_reward_ks_result = '用户不存在';
                $update_reward_ks = 1;
                throw new MyException('trans_id:'.$trans_id.'用户不存在');
            }

            $user = $user->toArray();

            if($user['isset'] != 1){
                $update_reward_ks_result = '用户已禁用';
                $update_reward_ks = 1;
                throw new MyException('trans_id:'.$trans_id.'用户已禁用');
            }

            $res = $this->rewardUserPacket($data);
            if(isset($res['ad_id'])){
                DB::table('ad_reward_relation')->insert([
                    'ad_id'         => $res['ad_id'],
                    'type'          => 'ks',
                    'reward_id'     => $data['reward_id'] ?? '',
                    'trans_id'      => $trans_id,
                    'create_time'   => time(),
                    'create_time_f' => date('Y-m-d H:i:s',time())
                ]);
                AdRewardKsModel::where('id',$reward_record['id'])
                ->update([
                    'status' => 1,
                    'receive_over' => 1,
                    'result' => '奖励验证成功',
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            DB::commit();
            return $res;
        } catch(\Exception $e) {
            DB::rollBack();
            if($update_reward_ks == 1){
                AdRewardKsModel::where('trans_id',$trans_id)
                ->update([
                    'status' => 3,
                    'receive_over' => 1,
                    'result' => $update_reward_ks_result,
                    'update_time' => time(),
                    'update_time_f' => date('Y-m-d H:i:s',time())
                ]);
            }
            throw new MyException($e->getMessage());
        }
    }    

    public function rewardGromore($user){}

    public function rewardGromoreExecute($data){}
    public function rewardUserPacket($data){}
    public function rewardGromoreCallback(){}

    public function rewardGdtCallback(){}

    public function rewardKsCallback(){}

    /**
     * 处理用户登录记录
     * @param int $userId 用户ID
     * @param string|array $login_info 用户登录信息
     * @return void
     */
    public function handleLoginRecord($userId, $login_info=[])
    {
        // 确保 $login_info 是数组类型
        if(!is_array($login_info)) {
            return false;
        }
        
        $now = time();
        
        // 查询最近一条登录记录
        $lastLogin = UserLoginModel::where('user_id', $userId)
            ->orderBy('login_time', 'desc')
            ->first();
            
        // 如果存在最近登录记录且时间间隔小于10秒,则不创建新记录    
        /* if($lastLogin && ($now - $lastLogin->login_time < 3600) && date('Y-m-d', $now) == date('Y-m-d', $lastLogin->login_time)) {
            return;
        } */

        if($lastLogin && ($now - $lastLogin->login_time < 10)) {
            return;
        }

        if(empty($login_info['ip'])){
            $login_info['ip'] = Tools::getIp();
        }
        
        // 创建新的登录记录
        UserLoginModel::create([
            'user_id' => $userId,
            'login_time' => $now,
            'ip' => $login_info['ip'] ?? '',
            'ip_address' => $login_info['ip_address'] ?? '',
            'm_type' => $login_info['m_type'] ?? 1,
            'm_version' => $login_info['m_version'] ?? '',
            'm_brand' => $login_info['m_brand'] ?? '',
            'm_model' => $login_info['m_model'] ?? '',
            'm_device_id' => $login_info['m_device_id'] ?? '',
            'm_network' => $login_info['m_network'] ?? 0,
            'm_netserver' => $login_info['m_netserver'] ?? 0,
            'm_simulator' => $login_info['m_simulator'] ?? 0,
            'adv_num' => 0,
            'money' => 0,
            'withdraw' => 0,
            'invite_num' => 0,
            'create_time' => $now,
            'update_time' => $now
        ]);
    }

    public function loginSms(){
        $data = request()->all();
        $phone = $data['phone'] ?? '';
        $pwd = $data['password'] ?? '';
        if(empty($phone) || empty($pwd)){
            throw new MyException('参数错误');
        }
        if(!Tools::isMobileReg($phone)){
            throw new MyException('手机号格式错误');
        }

        $user = UserModel::where('phone',$phone)->first();
        if(!$user){
            throw new MyException('您的账号不存在');
        }
        
        if($user->password != getMd5($pwd)){
            throw new MyException('您的账号密码错误');
        }

        if($user->status != 1){
            throw new MyException('您的账号已禁用');
        }
        $user->login_time = date("Y-m-d H:i:s");
        

        // 处理登录记录

        $domain = config('jwt.domain');
        $jwtExp = config('jwt.expire_time');

        $data = array(
            'id'=>$user['id'],
        );
        $payload=array(
            'iss'=>$domain,
            'iat'=>time(),
            'exp'=>time()+$jwtExp,
            'nbf'=>time(),
            'sub'=>$domain,
            'jti'=>md5(uniqid('JWT').time().mt_rand(10000,99999)),
            'data'=>$data
        );

        $token = $this->jwtService->getToken($payload);
        $user->token_pc = $token;
        if($user->is_admin != 1){
            if($user->vip_end_time && strtotime($user->vip_end_time) > time()) {
                // VIP未过期的处理逻辑
                $user->is_vip = 1;
            } else {
                // VIP已过期或没有VIP的处理逻辑
                $user->is_vip = 0;
            }
        }
        $user->save();

        if($user->is_admin == 1){
            $user->is_vip = 1;
            $user->vip_end_time = "9999-12-31 23:59:59";
        }

        $user_info = [
            'is_admin'     => $user->is_admin,
            'phone'        => $user->phone,
            'is_vip'       => $user->is_vip,
            'vip_end_time' => $user->vip_end_time, 
            'token'        => $token
        ];
        return $user_info;
    }

    public function withdrawInfo($user){
        $user = UserModel::find($user['id']);
        if(!$user){
            throw new MyException('用户不存在');
        }
        $desc = getAppConfig('withdrawal','desc');
        
        // 将提现说明文本按\r\n分割并过滤空值
        if(!empty($desc)) {
            $desc = array_values(array_filter(explode("\r\n", $desc), function($item) {
                return !empty(trim($item));
            }));
        } else {
            $desc = [];
        }

        $site_name = getAppConfig('site','site_name');

        $desc[] = (count($desc)+1)."、您当前使用的应用名称【".$site_name."】登录账号：【".$user['phone']."】，有问题可以通过 红包提现->在线客服 反馈";

        $title = getAppConfig('withdrawal','title');

        return [
            'weixin_image' => $user->weixin_image ? $user->weixin_image : '',
            'title' => $title,
            'desc' => $desc
        ];
    }

    public function getUserInfo($user){
        if($user['is_admin'] == 1){
            $user['is_vip'] = 1;
            $user['vip_end_time'] = "9999-12-31 23:59:59";
        }else{
            if($user['vip_end_time'] && strtotime($user['vip_end_time']) > time()) {
                // VIP未过期的处理逻辑
                $user['is_vip'] = 1;
            } else {
                // VIP已过期或没有VIP的处理逻辑
                $user['is_vip'] = 0;
            }
        }
        $user_info = [
            'is_admin'     => $user['is_admin'], 
            'phone'        => $user['phone'],
            'is_vip'       => $user['is_vip'],
            'vip_end_time' => $user['vip_end_time']
        ];
        return $user_info; 
    }

    public function getStoreInfo($user){
        $user_info = $this->getUserInfo($user);
        $user_info['number_all'] = 5000;
        $user_info['number_used'] = 1000;
        $user_info['number_left'] = $user_info['number_all'] - $user_info['number_used'];
        $store_list = UserAccountModel::where('user_id',$user['id'])
                        ->where('account_type',2)
                        ->where('status',1)
                        ->select(['id','account_name','app_key','app_secret'])
                        ->get()
                        ->toArray();
        $store_list = array_values(array_filter($store_list, function($item) {
            return !empty($item['app_key']) && !empty($item['app_secret']);
        }));
        $store_list = array_map(function($item) {
            return array_diff_key($item, array_flip(['app_secret', 'app_key']));
        }, $store_list);
        $user_info['store_list'] = $store_list;
        return $user_info;
    }

    public function logout($user){
        $user = UserModel::find($user['id']);
        $user->token_pc = '';
        $user->save();
        return [];
    }

    public function canAd($user){}

    public function packetDescribe($user){
        $user_id = $user['id'];
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        return $this->userAdInfo($user_id);
    }

    public function collectPacketOne($user){
        return [];
    }

    private function convert($number){
        $formatted_number = sprintf("%.2f", substr(sprintf("%.4f",$number),0,-2));
        if($formatted_number=='0.00'){
            return 0.01;
        }else{
            return $formatted_number;
        }
    }

    public function withdrawList($user_id){    
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        

        // 获取分页参数
        $page = request()->input('page_no', $this->page_no);
        $perPage = request()->input('per_page', $this->per_page);
        
        // 构建查询
        $query = Withdraw::where('user_id', $user_id)
            ->select('id', 'money', 'real_money', 'create_time', 'status', 'reply')
            ->orderBy('create_time', 'desc');
            
        // 获取总数
        $count = $query->count();
        
        // 获取分页数据
        $list = $query->forPage($page, $perPage)->get()->toArray();
        
        return [
            'list' => $list,
            'page_no' => (int)$page,
            'page_size' => (int)$perPage,
            'count' => $count
        ];
    }

    public function withdrawAdd($user){
        $data = request()->all();
        $user_id = $user['id'];
        
        // 开启事务并加锁
        DB::beginTransaction();
        try {
            
            $key = "request_withdraw_limit:{$user_id}";
            // 尝试设置缓存（3秒过期，仅当键不存在时成功）
            if (!Cache::add($key, now(), 3)) {
                throw new MyException('提现操作过于频繁，请稍候再试');
            }
            

            // 使用 lockForUpdate 加排他锁
            $user = UserModel::where('id', $user_id)->lockForUpdate()->first();
            if(!$user){
                throw new MyException('用户不存在');
            }
            
            if(empty($user['weixin_image'])){
                throw new MyException('请先绑定微信收款码');
            }
            
            $money = $data['money'];
            if($money<=0){
                throw new MyException('提现金额不能小于0');
            }
            
            if($user['money']<$money){
                throw new MyException('您的余额不足');
            }
            
            $money_item = WithdrawMoney::query()
                    ->where('delete_time',0)
                    ->orWhere('delete_time',null)
                    ->where('money',$money) 
                    ->select('id', 'money', 'day_num','commission')
                    ->first();
                    
            if(!$money_item){
                throw new MyException('提现金额不符合要求');
            }
            
            $count = Withdraw::query()->where([
                        'user_id'=>$user_id,
                        'money'=>$money,
                        'add_time'=>date('Y-m-d'),
                    ])->count();
                    
            if($money_item['day_num']>0 && $money_item['day_num']<=$count){
                throw new MyException('您今天不能再提现该金额');
            }
            
            $pt_money = 0;
            $real_money = $money;
            if($money_item['commission']>0){
                $commission = $money_item['commission'];
                $charge     = $commission*0.01;
                $pt_money   = $money*$charge;
                $real_money = $money-$pt_money;
            }
            
            // 创建提现记录
            Withdraw::query()->insert([
                'user_id'=>$user_id,
                'money'=>$money,
                'real_money'=>$real_money,
                'add_time'=>date('Y-m-d'),
                'create_time'=>time(),
                'update_time'=>time()
            ]);

            // 更新用户余额
            $user->money = $user->money-$money;
            $user->save();

            // 提交事务
            DB::commit();
            return [];

        } catch(\Exception $e) {
            // 回滚事务
            DB::rollBack();
            throw new MyException($e->getMessage());
        }
    }

    public function moneyList($user_id){
        $user = UserModel::find($user_id);
        if(!$user){
            throw new MyException('用户不存在');
        }
        // 获取分页参数
        $page = request()->input('page_no', $this->page_no);
        $perPage = request()->input('per_page', $this->per_page);

        $query = RedPacketModel::select('red_packet.*', 'user.phone as from_user_phone')
            ->leftJoin('user', 'red_packet.from_user_id', '=', 'user.id')
            ->where('red_packet.user_id', $user_id)
            ->select('red_packet.*', 'user.phone as from_user_phone')
            ->orderBy('red_packet.create_time', 'desc');
        $count = $query->count();
        $list = $query->forPage($page, $perPage)->get()->toArray();

        $site_name = getAppConfig('site','site_name');
        $site_name = Str::substr($site_name, 0, 4);
        $phone = $user['phone'];
        if($phone){
            $phone = substr($phone, 0, 2) . '******' . substr($phone, -3);
        }
        // 处理手机号隐私
        foreach($list as &$item) {
            if(!empty($item['from_user_phone']) && preg_match('/^1\d{10}$/', $item['from_user_phone'])) {
                $item['from_user_phone'] = substr($item['from_user_phone'], 0, 3) . '****' . substr($item['from_user_phone'], -4);
            } else {
                $item['from_user_phone'] = '';
            }
            $item['create_time'] = $item['create_time']."[".$site_name."-".$phone."]";
        }
        
        return [
            'list' => $list,
            'page_no' => (int)$page,
            'page_size' => (int)$perPage,
            'count' => $count
        ];
    }

    

    /**
     * 生成唯一的6位注册码
     * @return string 生成的注册码
     * @throws MyException 当无法生成唯一注册码时抛出异常
     */

    public function generateInviteCode()
    {
        $maxAttempts = 30; // 最大尝试次数
        $attempts = 0;
        do {
            // 生成6位随机字符串(只包含数字和大写字母,排除易混淆的字符)
            $code = Tools::generateRandomStr(6);
            // 检查是否已存在
            $exists = UserModel::where('invite_code', $code)->exists();
            $attempts++;
            // 如果不存在则返回该码
            if(!$exists) {
                return $code;
            }
        } while($attempts < $maxAttempts);
        
        // 如果尝试次数过多仍未成功,则抛出异常
        throw new MyException('无法生成唯一邀请码,请重试');
    }

    public function register()
    {
        $data = request()->all();
        // 验证必填参数
        if(empty($data['phone']) || empty($data['password']) || empty($data['captcha']) || empty($data['imageKey'])) {
            throw new MyException('请填写完整注册信息');
        }


        // 验证图形验证码
        if(!captcha_api_check($data['captcha'], $data['imageKey'])) {
            return ['result'=>40001,'error'=>'验证码错误'];
            //throw new MyException('验证码错误,请重新获取');
        }


        $phone = $data['phone'] ?? '';
        $pwd = $data['password'] ?? '';
        $reg_code = $data['reg_code'] ?? '';

        // 验证手机号格式
        if(!Tools::isMobileReg($phone)){
            throw new MyException('手机号格式错误');
        }

        // 验证密码长度
        if(strlen($pwd) < 6){
            throw new MyException('密码长度不能小于6位');
        }

        if(strlen($pwd) > 16){
            throw new MyException('密码长度不能大于16位');
        }

        // 处理注册码
        if($reg_code){
            $reg_code = strtolower($reg_code);
            if(strlen($reg_code) != 6){
                throw new MyException('注册码格式不正确');
            }
        }
        
        $is_exists = UserModel::where('phone',$phone)->first();
        if($is_exists){
            throw new MyException('您输入的手机号已存在');
        }

        $invite_code = $this->generateInviteCode();

        DB::beginTransaction();
        try {
            // 创建用户
            $userData = [
                'invite_1' => 0,
                'phone' => $phone,
                'password' => getMd5($pwd),
                'invite_code' => $invite_code,
                'is_vip' => 0,
                'status' => 1,
                'login_time' => date("Y-m-d H:i:s"),
                'created_at' => date("Y-m-d H:i:s"),
                'updated_at' => date("Y-m-d H:i:s")
            ];
            $user = UserModel::create($userData);

            $domain = config('jwt.domain');
            $jwtExp = config('jwt.expire_time');

            $data = array(
                'id'=>$user['id'],
            );
            $payload=array(
                'iss'=>$domain,
                'iat'=>time(),
                'exp'=>time()+$jwtExp,
                'nbf'=>time(),
                'sub'=>$domain,
                'jti'=>md5(uniqid('JWT').time().mt_rand(10000,99999)),
                'data'=>$data
            );
            $token=$this->jwtService->getToken($payload);
            $user->token_pc = $token;
            $user->login_time = date("Y-m-d H:i:s");
            $user->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // 判断是否是唯一索引冲突异常
            if(str_contains($e->getMessage(), 'Duplicate entry') && str_contains($e->getMessage(), 'phone')) {
                throw new MyException('该手机号已被注册,请勿重复提交');
            }
            throw new MyException('注册失败');
        }

        $user_info = [
            'phone'        => $userData['phone'],
            'is_vip'       => $userData['is_vip'],
            'vip_end_time' => 0, 
            'token'        => $token
        ];
        return $user_info;
    }

    /**
     * 生成测试用的trans_id和sign
     * @return array 包含trans_id和sign的数组
     */
    public function generateTestSign()
    {
        $sign_key = config('ad.gromore.sign_key') ?? '';
        if(empty($sign_key)) {
            throw new MyException('签名密钥未配置');
        }
        
        // 生成随机trans_id
        $trans_id = Str::random(16);
        $trans_id = '022b8c20-e172-42c9-a4b2-b5ae978164fb';
        // 使用相同的签名算法
        $sign_str = $sign_key . ':' . $trans_id;
        $sign = hash('sha256', $sign_str);
        
        return [
            'trans_id' => $trans_id,
            'sign' => $sign
        ];
    }

    /**
     * 生成测试用的trans_id和sign
     * @return array 包含trans_id和sign的数组
     */
    public function generateGdtTestSign()
    {
        $sign_key = config('ad.gdt.sign_key') ?? '';
        if(empty($sign_key)) {
            throw new MyException('签名密钥未配置');
        }
        
        // 生成随机trans_id
        $trans_id = Str::random(16);
        $trans_id = 'ec1f223e4c519920c1b8df836b11ff8d';
        // 使用相同的签名算法
        $sign_str = $trans_id . ':' . $sign_key;
        $sign = hash('sha256', $sign_str);
        
        return [
            'trans_id' => $trans_id,
            'sign' => $sign
        ];
    }

    public function generateKsTestSign()
    {
        $sign_key = config('ad.ks.sign_key') ?? '';
        if(empty($sign_key)) {
            throw new MyException('签名密钥未配置');
        }
        $trans_id = "2004323118635656322_105804967669_1740565000251";
        $sign_str =  $sign_key . ':' . $trans_id;
        $server_sign = strtolower(md5($sign_str));
        return [
            'trans_id' => $trans_id,
            'sign' => $server_sign
        ];
    }

    public function adRewardInfoByUser($data){
        $secret_key = "FPJ6wdxoXaVygNjW";
        $token = $data['token'] ?? '';
        if(empty($token)){
            throw new MyException('token不能为空');
        }
        $create_time = $data['create_time'] ?? 0;
        $create_time = intval($create_time);
        if($create_time <= 0){
            throw new MyException('create_time不能为空');
        }
        $user_id = $data['user_id'] ?? 0;
        $user_id = intval($user_id);
        $phone = $data['phone'] ?? '';
        if(empty($phone) && $user_id <= 0){
            throw new MyException('手机号或用户ID不能为空');
        }
        if($user_id > 0){
            $user = UserModel::find($user_id);
        }else{
            $user = UserModel::where('phone',$phone)->first();
        }
        if(!$user){
            throw new MyException('用户不存在');
        }
        if(!is_array($user)){
            $user = $user->toArray();
        }
        $phone = $user['phone'];
        if(md5($phone.$secret_key) != $token){
            throw new MyException('token错误');
        }

        $reward_ad_num = $ad_reward_config['reward_ad_num'] ?? 0;
        $reward_ad_num = intval($reward_ad_num);
        if($reward_ad_num <= 0){
            $reward_ad_num = 30;
        }

        $is_system_tg = 0;
        $system_tg_code = config('ad.system_tg_code');
        $system_tg_code_arr = explode('_', $system_tg_code);
        if(in_array($user['pid'], $system_tg_code_arr)){
            $is_system_tg = 1;
        }

        if ($is_system_tg == 0) {
            return [
                'reward_open' => 1,
                'is_system_tg' => 0,
                'reward_ad_num' => $reward_ad_num,
                'user_ad_number_today' => 0,
                'user_ad_income_today' => 0,
                'user_withdraw_money_success_today' => 0,
                'user_withdraw_money_auditing_today' => 0,
                'user_withdraw_reward_money_success_today' => 0,
                'user_withdraw_reward_money_auditing_today' => 0,
                'max_ad_reward_money' => 0,
                'reward_latest_time' => 0,
                'count_next_number' => 0,
                'user_ad_number_yesterday' => 0,
                'user_ad_income_yesterday' => 0,
                'user_withdraw_money_success_yesterday' => 0,
                'remain_reward_money_yesterday' => 0,
                'remain_reward_money_current' => 0,
                'latest_cal_time' => 0,
                'user_info' => $user
            ];
        }
        
        $today = date('Y-m-d',$create_time);
        $yesterday = date('Y-m-d',strtotime('-1 day',$create_time));

        $user_ad_number_today = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$today)->count();
        $user_ad_income_today = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$today)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
        $user_ad_income_today = floatval(format_money($user_ad_income_today,1));

        $user_withdraw_money_success_today = Withdraw::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_success_today = floatval(format_money($user_withdraw_money_success_today,1));

        $user_withdraw_money_auditing_today = Withdraw::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_auditing_today = floatval(format_money($user_withdraw_money_auditing_today,1));

        $user_withdraw_reward_money_success_today = WithdrawReward::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_success_today = floatval(format_money($user_withdraw_reward_money_success_today,1));

        $user_withdraw_reward_money_auditing_today = WithdrawReward::query()->where('user_id',$user_id)->where('add_time',$today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_reward_money_auditing_today = floatval(format_money($user_withdraw_reward_money_auditing_today,1));

        $max_ad_reward_money = min($user_ad_income_today, $user_withdraw_money_success_today);

        $reward_latest_time = WithdrawReward::query()->where('user_id',$user_id)->where('status','<',3)->orderBy('id','desc')->value('create_time');
        if(!empty($reward_latest_time) && !is_numeric($reward_latest_time)){
            $reward_latest_time = strtotime($reward_latest_time);
        }

        $count_next_number = 0;
        $count_next_reward_time = 0;
        $count_next_reward_time_f = "";
        if(empty($reward_latest_time)){
            $reward_latest_time = 0;
        }else{
            $count_next_number = 7200 - (time() - $reward_latest_time);
            if($count_next_number <= 0){
                $count_next_number = 0;
            }else{
                $count_next_reward_time = $reward_latest_time + 7200;
                $count_next_reward_time_f = date('Y-m-d H:i:s',$count_next_reward_time);
            }
        }

        $user_ad_number_yesterday = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$yesterday)->count();
        $user_ad_income_yesterday = AdvertisementModel::query()->where('user_id',$user_id)->where('can_time',$yesterday)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');
        $user_ad_income_yesterday = floatval(format_money($user_ad_income_yesterday,1));

        $user_withdraw_money_success_yesterday = Withdraw::query()->where('user_id',$user_id)->where('add_time',$yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_success_yesterday = floatval(format_money($user_withdraw_money_success_yesterday,1));

        $user_withdraw_money_auditing_yesterday = Withdraw::query()->where('user_id',$user_id)->where('add_time',$yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        $user_withdraw_money_auditing_yesterday = floatval(format_money($user_withdraw_money_auditing_yesterday,1));

        $user_withdraw_reward_money_success_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
        $user_withdraw_reward_money_auditing_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        
        $user_withdraw_reward_money_success_yesterday = floatval(format_money($user_withdraw_reward_money_success_yesterday,1));
        $user_withdraw_reward_money_auditing_yesterday = floatval(format_money($user_withdraw_reward_money_auditing_yesterday,1));


        $max_ad_reward_money_yesterday = min($user_ad_income_yesterday, $user_withdraw_money_success_yesterday);

        if($user_ad_number_yesterday <= $reward_ad_num){
            $remain_reward_money_yesterday = 0;
        }else{
            $remain_reward_money_yesterday = $max_ad_reward_money_yesterday - $user_withdraw_reward_money_success_yesterday - $user_withdraw_reward_money_auditing_yesterday;
            $remain_reward_money_yesterday = floatval(format_money($remain_reward_money_yesterday,1));
        }

        $remain_reward_money_today = $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        $remain_reward_money_today = floatval(format_money($remain_reward_money_today,1));

        $remain_reward_money_current = $remain_reward_money_yesterday + $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;

        $remain_reward_money_current = floatval(format_money($remain_reward_money_current,1));


        $user_remind_withdraw_money = $user_ad_income_today - $user_withdraw_money_success_today - $user_withdraw_money_auditing_today;
        $user_remind_withdraw_money = floatval(format_money($user_remind_withdraw_money,1));
        if($user_remind_withdraw_money <= 0){
            $user_remind_withdraw_money = 0;
        }

        
        return [
            'reward_open' => 1,
            'is_system_tg' => 1,
            'reward_ad_num' => $reward_ad_num,
            'user_ad_number_today' => $user_ad_number_today,
            'user_ad_income_today' => $user_ad_income_today,
            'user_withdraw_money_success_today' => $user_withdraw_money_success_today,
            'user_withdraw_money_auditing_today' => $user_withdraw_money_auditing_today,
            'user_withdraw_reward_money_success_today' => $user_withdraw_reward_money_success_today,
            'user_withdraw_reward_money_auditing_today' => $user_withdraw_reward_money_auditing_today,
            'max_ad_reward_money' => $max_ad_reward_money,
            'reward_latest_time' => !empty($reward_latest_time) ? date('Y-m-d H:i:s',$reward_latest_time) : '',
            'count_next_number' => $count_next_number,
            'count_next_reward_time' => $count_next_reward_time,
            'count_next_reward_time_f' => $count_next_reward_time_f,
            'user_ad_number_yesterday' => $user_ad_number_yesterday ?? 0,
            'user_ad_income_yesterday' => $user_ad_income_yesterday ?? 0,
            'user_withdraw_money_success_yesterday' => $user_withdraw_money_success_yesterday ?? 0,
            'user_withdraw_money_auditing_yesterday' => $user_withdraw_money_auditing_yesterday ?? 0,
            'user_withdraw_reward_money_success_yesterday' => $user_withdraw_reward_money_success_yesterday ?? 0,
            'user_withdraw_reward_money_auditing_yesterday' => $user_withdraw_reward_money_auditing_yesterday ?? 0,
            'remain_reward_money_yesterday' => $remain_reward_money_yesterday,
            'remain_reward_money_current' => $remain_reward_money_current>0 ? $remain_reward_money_current : 0,
            'user_remind_withdraw_money' => $user_remind_withdraw_money,
            'user_info' => $user
        ];
        
        
        
        

    }

    public function adReward($user){
        $user_id = $user['id'];

        $ad_reward_config = AppConfig::query()->where('type','withdrawal_reward')->select(['title','value'])->get()->toArray();
        $ad_reward_config = array_column($ad_reward_config, 'value', 'title');

        $reward_open = intval($ad_reward_config['reward_open'] ?? 0);
        if($reward_open != 1){
            $reward_open = 0;
        }

        $site_name = config('ad.system_name');


        $ad_reward_desc = $ad_reward_config['ad_reward_desc'] ?? '';
        if(!empty($ad_reward_desc)){
            // 先按换行符分割
            $items = array_filter(explode("\r\n", $ad_reward_desc), function($item) {
                return !empty(trim($item));
            });
            
            // 如果只有一个元素，且包含"数字+顿号"格式，则按该格式分割
            if(count($items) === 1 && preg_match('/\d+、/', $items[0])) {
                $items = preg_split('/\d+、/', $items[0], -1, PREG_SPLIT_NO_EMPTY);
                $items = array_map('trim', $items);
            }
            
            $ad_reward_desc = array_values(array_filter($items));
        }

        $ad_reward_close_desc = $ad_reward_config['ad_reward_close_desc'] ?? '';
        if(empty($ad_reward_close_desc)){
            $ad_reward_close_desc = "";
        }         

        $reward_ad_num = $ad_reward_config['reward_ad_num'] ?? 0;
        $reward_ad_num = intval($reward_ad_num);
        if($reward_ad_num <= 0){
            $reward_ad_num = 30;
        }

        $ad_reward_down_desc = $ad_reward_config['ad_reward_down_desc'] ?? '';
        if(empty($ad_reward_down_desc)){
            $ad_reward_down_desc = "";
        }
        $ad_reward_down_desc = str_ireplace('{num}',$reward_ad_num,$ad_reward_down_desc);

        $ad_reward_success_desc = $ad_reward_config['ad_reward_success_desc'] ?? '';
        if(empty($ad_reward_success_desc)){
            $ad_reward_success_desc = "";
        }


        if ($reward_open == 0) {
            return [
                'reward_open' => 0,
                'is_system_tg' => 0,
                'reward_ad_num' => $reward_ad_num,
                'ad_reward_desc' => $ad_reward_desc,
                'ad_reward_close_desc' => $ad_reward_close_desc,
                'ad_reward_down_desc' => $ad_reward_down_desc,
                'ad_reward_success_desc' => $ad_reward_success_desc,
                'user_ad_number_today' => 0,
                'user_ad_income_today' => 0,
                'user_withdraw_money_success_today' => 0,
                'user_withdraw_money_auditing_today' => 0,
                'user_withdraw_reward_money_success_today' => 0,
                'user_withdraw_reward_money_auditing_today' => 0,
                'max_ad_reward_money' => 0,
                'reward_latest_time' => 0,
                'count_next_number' => 0,
                'user_ad_number_yesterday' => 0,
                'user_ad_income_yesterday' => 0,
                'user_withdraw_money_success_yesterday' => 0,
                'remain_reward_money_yesterday' => 0,
                'remain_reward_money_current' => 0,
                'latest_cal_time' => 0,
                'site_name' => $site_name,
                'user_info' => $user
            ];
        }

        $is_system_tg = 0;
        $system_tg_code = config('ad.system_tg_code');
        $system_tg_code_arr = explode('_', $system_tg_code);
        if(in_array($user['pid'], $system_tg_code_arr)){
            $is_system_tg = 1;
        }

        if ($is_system_tg == 0) {
            return [
                'reward_open' => 1,
                'is_system_tg' => 0,
                'reward_ad_num' => $reward_ad_num,
                'ad_reward_desc' => $ad_reward_desc,
                'ad_reward_close_desc' => $ad_reward_close_desc,
                'ad_reward_down_desc' => $ad_reward_down_desc,
                'ad_reward_success_desc' => $ad_reward_success_desc,
                'user_ad_number_today' => 0,
                'user_ad_income_today' => 0,
                'user_withdraw_money_success_today' => 0,
                'user_withdraw_money_auditing_today' => 0,
                'user_withdraw_reward_money_success_today' => 0,
                'user_withdraw_reward_money_auditing_today' => 0,
                'max_ad_reward_money' => 0,
                'reward_latest_time' => 0,
                'count_next_number' => 0,
                'user_ad_number_yesterday' => 0,
                'user_ad_income_yesterday' => 0,
                'user_withdraw_money_success_yesterday' => 0,
                'remain_reward_money_yesterday' => 0,
                'remain_reward_money_current' => 0,
                'latest_cal_time' => 0,
                'site_name' => $site_name,
                'user_info' => $user
            ];
        }

        
        

        $today = date('Y-m-d');

        $now = Carbon::now();
        $yesterday = $now->subDay()->format('Y-m-d');

        $user_ad_number_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->count();

        
        $user_ad_income_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');                
        $user_ad_income_today = floatval(format_money($user_ad_income_today,1));
        
        $user_withdraw_money_success_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
        $user_withdraw_money_auditing_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 

        $user_withdraw_money_success_today = floatval(format_money($user_withdraw_money_success_today,1));
        $user_withdraw_money_auditing_today = floatval(format_money($user_withdraw_money_auditing_today,1));

        
        $user_withdraw_reward_money_success_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
        $user_withdraw_reward_money_auditing_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_reward_money_success_today = floatval(format_money($user_withdraw_reward_money_success_today,1));
        $user_withdraw_reward_money_auditing_today = floatval(format_money($user_withdraw_reward_money_auditing_today,1));

        #实时的最大可提现金额 允许用户直接操作提现的数额
        $max_ad_reward_money = min($user_ad_income_today, $user_withdraw_money_success_today);
        if($user_ad_number_today < $reward_ad_num){
            //没看够要求的广告数量
            $max_ad_reward_money = 0;
        }

        /* var_dump("今日广告数量:".$user_ad_number_today);
        var_dump("今日广告收益:".$user_ad_income_today);
        var_dump("今日提现成功金额:".$user_withdraw_money_success_today);
        var_dump("今日提现审核中金额:".$user_withdraw_money_auditing_today);
        var_dump("今日奖励提现成功金额:".$user_withdraw_reward_money_success_today);
        var_dump("今日奖励提现审核中金额:".$user_withdraw_reward_money_auditing_today);
        var_dump("今日最大可提现金额:".$max_ad_reward_money); */
        
        #最近一次申请领取红包的时间
        $reward_latest_time = WithdrawReward::query()->where('user_id', $user_id)->where('status','<',3)->orderBy('id', 'desc')->value('create_time');
        if(!empty($reward_latest_time) && !is_numeric($reward_latest_time)){
            $reward_latest_time = strtotime($reward_latest_time);
        }
        $count_next_number = 0; #7200秒后可以再次领取红包
        $count_next_reward_time = 0; #7200秒后可以再次领取红包
        $count_next_reward_time_f = "";
        if(empty($reward_latest_time)){
            $reward_latest_time = 0;
        }else{
            $count_next_number = 7200 - (time() - $reward_latest_time);
            if($count_next_number <= 0){
                $count_next_number = 0;
            }else{
                $count_next_reward_time = $reward_latest_time + 7200;
                $count_next_reward_time_f = date('Y-m-d H:i:s',$count_next_reward_time);
            }
        }

        //var_dump("7200秒后可以再次领取红包:".$count_next_number);

        //var_dump("昨日日期:".$yesterday);
        #计算昨日剩余可提现金额
        $user_ad_number_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->count();

        $user_ad_income_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');                
        $user_ad_income_yesterday = floatval(format_money($user_ad_income_yesterday,1));

        $user_withdraw_money_success_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
        $user_withdraw_money_auditing_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_money_success_yesterday = floatval(format_money($user_withdraw_money_success_yesterday,1));
        $user_withdraw_money_auditing_yesterday = floatval(format_money($user_withdraw_money_auditing_yesterday,1));

        $user_withdraw_reward_money_success_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
        $user_withdraw_reward_money_auditing_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
        
        $user_withdraw_reward_money_success_yesterday = floatval(format_money($user_withdraw_reward_money_success_yesterday,1));
        $user_withdraw_reward_money_auditing_yesterday = floatval(format_money($user_withdraw_reward_money_auditing_yesterday,1));


        $max_ad_reward_money_yesterday = min($user_ad_income_yesterday, $user_withdraw_money_success_yesterday);



        //var_dump("昨日广告数量:".$user_ad_number_yesterday);
        if($user_ad_number_yesterday <= $reward_ad_num){
            #没看够要求的广告数量
            $remain_reward_money_yesterday = 0;
            //var_dump("昨日剩余可提现金额[广告数量不达标]:".$remain_reward_money_yesterday);
        }else{
            /* var_dump("昨日广告收益:".$user_ad_income_yesterday);
            var_dump("昨日提现成功金额:".$user_withdraw_money_success_yesterday);
            var_dump("昨日奖励提现成功金额:".$user_withdraw_reward_money_success_yesterday);
            var_dump("昨日奖励提现审核中金额:".$user_withdraw_reward_money_auditing_yesterday);
            var_dump("昨日最大可提现金额:".$max_ad_reward_money_yesterday); */

            $remain_reward_money_yesterday = $max_ad_reward_money_yesterday - $user_withdraw_reward_money_success_yesterday - $user_withdraw_reward_money_auditing_yesterday;
            $remain_reward_money_yesterday = floatval(format_money($remain_reward_money_yesterday,1));
        }
        //var_dump("昨日广告奖励金额:".$remain_reward_money_yesterday);
        
        $remain_reward_money_today = $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        $remain_reward_money_today = floatval(format_money($remain_reward_money_today,1));

        $remain_reward_money_current = $remain_reward_money_yesterday + $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        //var_dump("当前可获取广告奖励金额:".$remain_reward_money_current);
        $remain_reward_money_current = floatval(format_money($remain_reward_money_current,1));


        $user_remind_withdraw_money = $user_ad_income_today - $user_withdraw_money_success_today - $user_withdraw_money_auditing_today;
        $user_remind_withdraw_money = floatval(format_money($user_remind_withdraw_money,1));
        if($user_remind_withdraw_money <= 0){
            $user_remind_withdraw_money = 0;
        }
        
        $latest_cal_time = time();
        $user_ad_info = UserAdReward::query()->where('user_id',$user_id)->first();
        if($user_ad_info){
            UserAdReward::query()->where('user_id',$user_id)
                                ->update([
                                    'latest_cal_time'   => $latest_cal_time,
                                    'add_time'          => date("Y-m-d H:i:s"),
                                    'update_time'       => time()
                                ]);
        }else{
            UserAdReward::query()->insert([
                'user_id'           => $user_id,
                'ad_reward_num'     => 0,
                'latest_cal_time'   => $latest_cal_time,
                'add_time'          => date("Y-m-d H:i:s"),
                'update_time'       => time(),
                'create_time'       => time()
            ]);
        }

        return [
            'reward_open' => 1,
            'is_system_tg' => 1,
            'reward_ad_num' => $reward_ad_num,
            'ad_reward_desc' => $ad_reward_desc,
            'ad_reward_close_desc' => $ad_reward_close_desc,
            'ad_reward_down_desc' => $ad_reward_down_desc,
            'ad_reward_success_desc' => $ad_reward_success_desc,
            'user_ad_number_today' => $user_ad_number_today,
            'user_ad_income_today' => $user_ad_income_today,
            'user_withdraw_money_success_today' => $user_withdraw_money_success_today,
            'user_withdraw_money_auditing_today' => $user_withdraw_money_auditing_today,
            'user_withdraw_reward_money_success_today' => $user_withdraw_reward_money_success_today,
            'user_withdraw_reward_money_auditing_today' => $user_withdraw_reward_money_auditing_today,
            'max_ad_reward_money' => $max_ad_reward_money,
            'reward_latest_time' => !empty($reward_latest_time) ? date('Y-m-d H:i:s',$reward_latest_time) : '',
            'count_next_number' => $count_next_number,
            'count_next_reward_time' => $count_next_reward_time,
            'count_next_reward_time_f' => $count_next_reward_time_f,
            'user_ad_number_yesterday' => $user_ad_number_yesterday ?? 0,
            'user_ad_income_yesterday' => $user_ad_income_yesterday ?? 0,
            'user_withdraw_money_success_yesterday' => $user_withdraw_money_success_yesterday ?? 0,
            'user_withdraw_money_auditing_yesterday' => $user_withdraw_money_auditing_yesterday ?? 0,
            'user_withdraw_reward_money_success_yesterday' => $user_withdraw_reward_money_success_yesterday ?? 0,
            'user_withdraw_reward_money_auditing_yesterday' => $user_withdraw_reward_money_auditing_yesterday ?? 0,
            'remain_reward_money_yesterday' => $remain_reward_money_yesterday,
            'remain_reward_money_current' => $remain_reward_money_current>0 ? $remain_reward_money_current : 0,
            'user_remind_withdraw_money' => $user_remind_withdraw_money,
            'latest_cal_time' => $latest_cal_time,
            'site_name' => $site_name,
            'user_info' => $user
        ];

    }

    public function adRewardAdd($data){
        $user_id = $data['user_id'];
        
        if(empty($data['weixin_image'])){
            throw new MyException('请先上传收款码');
        }
        // 添加并发控制
        $key = "ad_reward_add_limit:{$user_id}";
        if (!Cache::add($key, now(), 6)) {
            throw new MyException('广告奖励提交过于频繁，请稍候再试');
        }
        
        $pid = $data['pid'];
        $ad_reward_config = AppConfig::query()->where('type','withdrawal_reward')->select(['title','value'])->get()->toArray();
        $ad_reward_config = array_column($ad_reward_config, 'value', 'title');

        $reward_open = intval($ad_reward_config['reward_open'] ?? 0);
        if($reward_open != 1){
            $reward_open = 0;
        }

        if($reward_open == 0){
            throw new MyException('广告奖励活动已结束');
        }

        $is_system_tg = 0;
        $system_tg_code = config('ad.system_tg_code');
        $system_tg_code_arr = explode('_', $system_tg_code);
        if(in_array($pid, $system_tg_code_arr)){
            $is_system_tg = 1;
        }

        if($is_system_tg == 0){
            throw new MyException('您不符合参加广告奖励活动的资格');
        }

        $reward_ad_num = $ad_reward_config['reward_ad_num'] ?? 0;
        $reward_ad_num = intval($reward_ad_num);
        if($reward_ad_num <= 0){
            $reward_ad_num = 30;
        }

        $latest_cal_time = $data['latest_cal_time'] ?? 0;
        if($latest_cal_time <= 0){
            throw new MyException('您提交的广告奖励数据不合法');
        }

        if(date('Y-m-d',$latest_cal_time) != date('Y-m-d')){
            throw new MyException('当前日期已经更改,请重新进入该页面再次提交');
        }

        $user_ad_info = UserAdReward::query()->where('user_id',$user_id)->first();
        if(!$user_ad_info || $user_ad_info->latest_cal_time == 0){
            throw new MyException('请先浏览广告奖励页面');
        }

        if($user_ad_info->latest_cal_time != $latest_cal_time){
            throw new MyException('您提交的广告奖励数据不合法');
        }

        $reward_latest_time = WithdrawReward::query()->where('user_id', $user_id)->where('status','<',3)->orderBy('id', 'desc')->value('create_time');
        if(!empty($reward_latest_time) && !is_numeric($reward_latest_time)){
            $reward_latest_time = strtotime($reward_latest_time);
        }
        
        if(!empty($reward_latest_time)){
            $count_next_number = 7200 - (time() - $reward_latest_time);
            if($count_next_number > 0){
                throw new MyException('距离上次领取广告奖励时间不足2小时');
            }
        }

        $today = date('Y-m-d');
        $now = Carbon::now();
        $yesterday = $now->subDay()->format('Y-m-d');

        $user_ad_number_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->count();

        
        $user_ad_income_today = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $today)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');                
        $user_ad_income_today = floatval(format_money($user_ad_income_today,1));
        
        $user_withdraw_money_success_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
        $user_withdraw_money_auditing_today = Withdraw::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 

        $user_withdraw_money_success_today = floatval(format_money($user_withdraw_money_success_today,1));
        $user_withdraw_money_auditing_today = floatval(format_money($user_withdraw_money_auditing_today,1));

        
        $user_withdraw_reward_money_success_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
        $user_withdraw_reward_money_auditing_today = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $today)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

        $user_withdraw_reward_money_success_today = floatval(format_money($user_withdraw_reward_money_success_today,1));
        $user_withdraw_reward_money_auditing_today = floatval(format_money($user_withdraw_reward_money_auditing_today,1));

        #实时的最大可提现金额 允许用户直接操作提现的数额
        $max_ad_reward_money = min($user_ad_income_today, $user_withdraw_money_success_today);
        if($user_ad_number_today < $reward_ad_num){
            //没看够要求的广告数量
            $max_ad_reward_money = 0;
        }

        //var_dump("昨日日期:".$yesterday);
        #计算昨日剩余可提现金额
        $user_ad_number_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->count();
        //var_dump("昨日广告数量:".$user_ad_number_yesterday);
        if($user_ad_number_yesterday <= $reward_ad_num){
            #没看够要求的广告数量
            $remain_reward_money_yesterday = 0;
            //var_dump("昨日剩余可提现金额[广告数量不达标]:".$remain_reward_money_yesterday);
        }else{
            $user_ad_income_yesterday = AdvertisementModel::query()->where('user_id', $user_id)->where('can_time', $yesterday)->selectRaw('COALESCE(SUM(income_user), 0) as total_income')->value('total_income');                
            $user_ad_income_yesterday = floatval(format_money($user_ad_income_yesterday,1));

            $user_withdraw_money_success_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
            //$user_withdraw_money_auditing_yesterday = Withdraw::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');

            $user_withdraw_money_success_yesterday = floatval(format_money($user_withdraw_money_success_yesterday,1));
            //$user_withdraw_money_auditing_yesterday = floatval(format_money($user_withdraw_money_auditing_yesterday,1));

            $user_withdraw_reward_money_success_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',2)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money'); 
            $user_withdraw_reward_money_auditing_yesterday = WithdrawReward::query()->where('user_id', $user_id)->where('add_time', $yesterday)->where('status',1)->selectRaw('COALESCE(SUM(real_money), 0) as total_money')->value('total_money');
            
            $user_withdraw_reward_money_success_yesterday = floatval(format_money($user_withdraw_reward_money_success_yesterday,1));
            $user_withdraw_reward_money_auditing_yesterday = floatval(format_money($user_withdraw_reward_money_auditing_yesterday,1));

            $max_ad_reward_money_yesterday = min($user_ad_income_yesterday, $user_withdraw_money_success_yesterday);
            
            $remain_reward_money_yesterday = $max_ad_reward_money_yesterday - $user_withdraw_reward_money_success_yesterday - $user_withdraw_reward_money_auditing_yesterday;
            $remain_reward_money_yesterday = floatval(format_money($remain_reward_money_yesterday,1));

        }

        $remain_reward_money_today = $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        $remain_reward_money_today = floatval(format_money($remain_reward_money_today,1));

        $remain_reward_money_current = $remain_reward_money_yesterday + $max_ad_reward_money - $user_withdraw_reward_money_success_today - $user_withdraw_reward_money_auditing_today;
        //var_dump("当前可获取广告奖励金额:".$remain_reward_money_current);
        $remain_reward_money_current = floatval(format_money($remain_reward_money_current,1));

        $user_submit_ad_reward_money = $data['user_submit_ad_reward_money'] ?? 0;
        $user_submit_ad_reward_money = floatval(format_money($user_submit_ad_reward_money,1));
        if($user_submit_ad_reward_money <= 0){
            throw new MyException('您提交的广告奖励数据不合法');
        }

        if($user_submit_ad_reward_money != $remain_reward_money_current){
            throw new MyException('广告奖励数据已发生变化，请重新进入该页面再次提交');
        }

        DB::beginTransaction();
        try{
            $user_ad_info->ad_reward_num = $user_ad_info->ad_reward_num + $user_submit_ad_reward_money;
            $user_ad_info->save();
            UserAdReward::query()->where('user_id',$user_id)
                                    ->update([
                                        'latest_cal_time'   => 0, //重置计算时间    
                                        'add_time'          => date("Y-m-d H:i:s"),
                                        'update_time'       => time()
                                    ]);

            WithdrawReward::query()->insert([
                'user_id'           => $user_id,
                'real_money'        => $user_submit_ad_reward_money,
                'add_time'          => date("Y-m-d H:i:s"),
                'status'            => 1,
                'reply'             => '',
                'type'              => 1,
                'add_time'          => date("Y-m-d"),
                'update_time'       => time(),
                'create_time'       => time(),
                'delete_time'       => 0
            ]);
            DB::commit();
            return ['status'=>1,'msg'=>'提交成功'];
        }catch(\Exception $e){
            DB::rollBack();
            throw new MyException('广告奖励数据提交失败');
        }
    }


    public function adRewardList($data,$page,$page_size){
        $user_id = $data['user_id'];
        $status = $data['status'] ?? 0;
        $list = WithdrawReward::query()->where('user_id',$user_id)
                            ->when((int)$status != 0,function($query) use ($status){
                                return $query->where('status',$status);
                            })
                            ->orderBy('id','desc')
                            ->paginate($page_size,['*'],'page',$page);
        $list = $list->toArray();
        $list['data'] = array_map(function($item){
            $item['real_money'] = format_money($item['real_money'],1);
            if($item['status'] == 1){
                $item['status_text'] = '审核中';
            }else if($item['status'] == 2){
                $item['status_text'] = '已到账';
            }else{
                $item['status_text'] = '已驳回';
            }
            $item['show_time'] = $item['status'] == 2 ? $item['adopt_time'] : $item['create_time'];
            return $item;
        },$list['data']);
        return $list;
    }

}

