export interface DOMFinderConfig {
  shadowRoot: {
    selector: string;     // shadowRoot容器选择器
    attribute?: string;   // 可选的属性验证
  };
  selectors: {
    primary: string[];    // 主选择器数组
    fallback: string[];   // 备选选择器数组
    context: string[];    // 上下文选择器数组
    controls?: {         // 控件选择器
      radioGroup?: string;  // 单选组选择器
      pagination?: string;  // 分页选择器
      paginationElements?: {  // 分页元素选择器
        number?: string;      // 页码选择器
        button?: string;      // 按钮选择器
        buttonDefault?: string;      // 按钮选择器
        link?: string;        // 链接选择器
        ellipsis?: string;    // 省略号选择器
      };
    };
  };
  validation: {
    attributes: string[]; // 用于验证的属性
    structure: string[]; // DOM结构验证规则
  };
}

export interface DOMFinderResult {
  element: Element | null;
  success: boolean;
  error?: string;
  strategy?: string;
}
