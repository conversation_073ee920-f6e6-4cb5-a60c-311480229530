import putFetch from './putFetch';
interface FetchApiProxy {
  [key: string]: (
    body: any,
    callback: (response: any) => any,
    headers?: HeadersInit,
    config?: any
  ) => void;
}

export function createFetchProxy(): FetchApiProxy {
  return new Proxy({} as FetchApiProxy, {
    get(target, property: string) {
      if (!(property in target)) {
        putFetch(target, property, "", {});
      }
      return target[property];
    }
  });
}
