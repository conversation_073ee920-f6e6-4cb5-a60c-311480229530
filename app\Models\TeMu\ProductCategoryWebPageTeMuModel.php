<?php
declare(strict_types=1);

namespace App\Models\TeMu;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ProductCategoryWebPageTeMuModel extends BaseModel
{
    protected $table = 'product_category_webpage_temu';
    
    protected $fillable = [
        'name',
        'name_en',
        'parent_id',
        'is_leaf',
        'level',
        'path',
        'path_name',
        'sort_order',
        'status',
        'relation_status',//关联状态 0未关联 1已关联 2无需关联
    ];

    // 作用域：按状态筛选
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    // 作用域：按父级ID筛选
    public function scopeByParentId($query, int $parentId)
    {
        return $query->where('parent_id', $parentId);
    }

    // 作用域：按层级筛选
    public function scopeByLevel($query, int $level)
    {
        return $query->where('level', $level);
    }

    // 获取子分类
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    // 获取父分类
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }
}
