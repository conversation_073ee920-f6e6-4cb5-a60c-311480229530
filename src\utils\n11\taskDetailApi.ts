import config from '@/config'

// 导入Chrome扩展消息发送函数
declare const chrome: any;

/**
 * 通过background页面发送请求
 */
function sendRequestViaBackground(url: string, options: any): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      url: url,
      method: options.method || 'GET',
      pramas: options.body ? JSON.parse(options.body) : {},
      headers: options.headers || {},
      auth: options.auth || false,
      encrypto: options.encrypto || false,
      timeout: 15000
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      
      if (response && response[0]) {
        const result = response[0];
        if (result.status >= 200 && result.status < 300) {
          resolve({ success: true, data: result.data });
        } else {
          reject(new Error(`请求失败: ${result.status}`));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
}

// 任务详情类型定义
export interface TaskDetail {
  id: number
  task_id: number
  user_id: number
  stock_code: string
  goods_name: string
  status: number
}

// 重新上传参数类型
export interface RetryUploadParams {
  taskDetailId: number
  userId: number
  storeId: number
  goodsData: any
  uploadParams: any
}

/**
 * 根据stockCode查找任务详情
 * @param stockCode 库存代码
 * @returns 任务详情
 */
export async function findTaskDetailByStockCode(stockCode: string): Promise<TaskDetail | null> {
  try {
    const options = {
      method: 'GET',
      auth: true,
      encrypto: true
    }

    const url = `${config.apiTaskDetailUrl}/find-by-stock-code?stock_code=${encodeURIComponent(stockCode)}`
    const response = await sendRequestViaBackground(url, options)

    if (response.success && response.data) {
      return response.data
    } else {
      return null
    }
  } catch (error) {
    console.error('根据stockCode查找任务详情失败:', error)
    throw error
  }
}

/**
 * 获取重新上传参数
 * @param taskDetailId 任务详情ID
 * @returns 重新上传参数
 */
export async function getRetryUploadParams(taskDetailId: number): Promise<RetryUploadParams> {
  try {
    const options = {
      method: 'GET',
      auth: true,
      encrypto: true
    }

    const url = `${config.apiTaskRetryUploadParamsUrl}?task_detail_id=${taskDetailId}`
    const response = await sendRequestViaBackground(url, options)

    if (response.success && response.data) {
      return response.data
    } else {
      throw new Error('获取重新上传参数失败')
    }
  } catch (error) {
    console.error('获取重新上传参数失败:', error)
    throw error
  }
}

/**
 * 批量重新上传失败任务
 * @param taskDetailIds 任务详情ID数组
 * @returns 重新上传结果
 */
export async function batchRetryUpload(taskDetailIds: number[]): Promise<any> {
  try {
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ task_detail_ids: taskDetailIds }),
      auth: true,
      encrypto: true
    }

    const response = await sendRequestViaBackground(
      config.apiTaskBatchRetryUploadUrl,
      options
    )

    if (response.success && response.data) {
      return response.data
    } else {
      throw new Error('批量重新上传失败')
    }
  } catch (error) {
    console.error('批量重新上传失败:', error)
    throw error
  }
} 