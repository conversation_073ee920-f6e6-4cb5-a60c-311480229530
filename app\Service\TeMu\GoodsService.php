<?php
namespace App\Service\TeMu;

use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\UserGoodsDirectoryModel;
use App\Models\User\GoodsInstructionImagesModel;
use Illuminate\Support\Facades\DB;


class GoodsService extends BaseService{

    public function getGoodsType($host){
        $host = strtolower($host);
        $host_parts = explode('.', $host);
        if (count($host_parts) > 1) {
            $main_domain = implode('.', array_slice($host_parts, -2));
        } else {
            $main_domain = $host;
        }
        $goodsType = [
            'temu.com' => 1,
        ];
        if(isset($goodsType[$main_domain])){
            return $goodsType[$main_domain];
        }

        return 0;
    }    
    
    public function getGoodsList(){
        
    }

    public function saveGoodsData(array $data)
    {
        if(empty($data['goodsName'])){
            throw new MyException("商品名称不能为空");
        }

        if(empty($data['source_url'])){
            throw new MyException("商品来源链接不能为空");
        }

        $url = parse_url($data['source_url']);
        if(empty($url['host'])){
            throw new MyException("商品来源链接格式不正确");
        }

        $type = $this->getGoodsType($url['host']);

        if(empty($data['optIdNameMap']) || !is_array($data['optIdNameMap']) || count($data['optIdNameMap']) < 2){
            throw new MyException("未获取到商品详情页分类数据");
        }

        $front_cat_id_1 = array_keys($data['optIdNameMap'])[0];
        $front_cat_id_2 = array_keys($data['optIdNameMap'])[1];
        $front_cat_desc = $data['optNameString'] ?? '';

        $mall_id = $data['mallId'] ?? 0;
        $goods_id = $data['goodsId'] ?? 0;
        $goods_name = $data['goodsName'] ?? '';
        $goods_detail = '';

        if(isset($data['goodsDetail']) && !empty($data['goodsDetail'])){
            $goods_detail =array_column($data['goodsDetail'], 'url');
            $goods_detail = json_encode($goods_detail,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
        }

        $goods_video = '';

        if($data['video']){
            $goods_video = $data['video']['videoUrl'] ?? '';
        }

        $goods_pic = '';
        $goods_pic_urls = [];
        if (isset($data['pics']) && is_array($data['pics']) && count($data['pics']) > 0) {
            $goods_pic_urls = array_column($data['pics'], 'url');
            $goods_pic = json_encode($goods_pic_urls,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES); 
        }

        $goods_instruction_images = '';
        if(isset($data['instructionImages']) && is_array($data['instructionImages']) && count($data['instructionImages']) > 0){
            $goods_instruction_images = json_encode($data['instructionImages'],JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
        }

        $goods_property = '';
        if(isset($data['goodsProperty']) && is_array($data['goodsProperty'])){
            $properties = $data['goodsProperty'];
            $count = count($properties);
            for ($i = 0; $i < $count; $i++) {
                if ($count > 1 && $i === $count - 2) {
                    continue;
                }
                
                $property = $properties[$i];
                
                $values_string = '';
                if (isset($property['values'])) {
                    if (is_array($property['values']) && count($property['values']) > 1) {
                        $values_string = implode(',', $property['values']);
                    } elseif (isset($property['values'][0])) {
                        $values_string = (string) $property['values'][0];
                    }
                }
                
                if (!empty($values_string)) {
                     $goods_property .= $property['key'] . ':' . $values_string . ',';
                }
            }
            $goods_property = rtrim($goods_property, ',');
        }

        $goods_sku_num = 0;
        if(isset($data['sku']) && is_array($data['sku'])){
            $goods_sku_num = count($data['sku']);
        }

        $goods_sold_quantity = $data['goodsSoldQuantity'] ?? 0;
        $goods_score = $data['goodsScore'] ?? 0;

        $goods_data = [
            'type' => $type,
            'user_id' => $data['user_id'],
            'directory_id' => $data['directory_id'] ?? 0, // 新增目录ID支持
            'source_url' => $data['source_url'],
            'cat_id' => $data['catId'],
            'front_cat_id_1' => $front_cat_id_1,
            'front_cat_id_2' => $front_cat_id_2,
            'front_cat_desc' => $front_cat_desc,
            'mall_id' => $mall_id,
            'goods_id' => $goods_id,
            'goods_name' => $goods_name,
            'goods_detail' => $goods_detail,
            'goods_video' => $goods_video,
            'goods_pic' => $goods_pic,
            'goods_sku' => '',
            'goods_sku_num' => $goods_sku_num,
            'goods_property' => $goods_property,
            'goods_sold_quantity' => $goods_sold_quantity,
            'goods_score' => $goods_score,
            'img_local_status' => 0,
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        DB::beginTransaction();
        try{
            // 修改商品存在性检查，增加directory_id条件
            // 每个用户不同的directory_id可以存储相同的商品goods_id
            $goodsModel = GoodsModel::where([
                'user_id' => $data['user_id'], 
                'goods_id' => $goods_id, 
                'type' => $type,
                'directory_id' => $data['directory_id'] ?? 0
            ])->first();

            $oldDirectoryId = null; // 记录旧的目录ID，用于更新商品数量
            $isNewGoods = false; // 是否为新商品

            if ($goodsModel) {
                $oldDirectoryId = $goodsModel->directory_id;
                // 使用原生查询更新，避免模型的类型转换影响JSON格式
                DB::table('user_goods')
                    ->where('id', $goodsModel->id)
                    ->update($goods_data);
                $user_goods_id = $goodsModel->id;
            } else {
                $goods_data['created_at'] = date('Y-m-d H:i:s');
                $user_goods_id = GoodsModel::insertGetId($goods_data);
                $isNewGoods = true;
            }

            if($user_goods_id <= 0){
                if (!$user_goods_id) {
                    throw new MyException("商品保存或更新失败");
                }
            }

            if (isset($data['sku']) && is_array($data['sku'])) {
                foreach($data['sku'] as $sku){
                    $price_str = str_replace('.', '', $sku['price']);
                    $price_float = (float)str_replace(',', '.', $price_str);
                    $sku['currency'] = $sku['currency'] ?? '';
                    $sku_data = [
                        'user_goods_id' => $user_goods_id,
                        'sku_id' => $sku['skuId'] ?? 0,
                        'goods_id' => $sku['goodsId'] ?? 0,
                        'thumb_url' => !empty($sku['thumbUrl']) ? trim($sku['thumbUrl']) : '',
                        'currentcy' => !empty($sku['currency']) ? trim($sku['currency']) : '',
                        'price' => $price_float,
                        'spec_key_values' => !empty($sku['specKeyValues']) ? trim($sku['specKeyValues']) : '',
                        'spec_values' => !empty($sku['specValues']) ? trim($sku['specValues']) : '',
                        'skc_gallery' => !empty($sku['skcGallery']) ? json_encode($sku['skcGallery'], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES) : '',
                        'is_skc_gallery' => $sku['isSkcGallery'] ?? 0,
                        'url' => !empty($sku['url']) ? trim($sku['url']) : '',
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];

                    $goodsSkuModel = GoodsSkuModel::where(['goods_id' => $sku_data['goods_id'], 'sku_id' => $sku_data['sku_id']])->first();

                    if ($goodsSkuModel) {
                        // 使用原生查询更新，避免模型的类型转换影响JSON格式
                        DB::table('user_goods_sku')
                            ->where('id', $goodsSkuModel->id)
                            ->update($sku_data);
                    } else {
                        $sku_data['created_at'] = date('Y-m-d H:i:s');
                        GoodsSkuModel::insert($sku_data);
                    }
                }
            }

            if($goods_instruction_images){
                $goodsInstructionImagesModel = GoodsInstructionImagesModel::where(['user_goods_id' => $user_goods_id])->first();
                if($goodsInstructionImagesModel){
                    $goodsInstructionImagesModel->urls = $goods_instruction_images;
                    $goodsInstructionImagesModel->save();
                }else{
                    $goodsInstructionImagesModel = new GoodsInstructionImagesModel();
                    $goodsInstructionImagesModel->user_goods_id = $user_goods_id;
                    $goodsInstructionImagesModel->goods_id = $goods_id;
                    $goodsInstructionImagesModel->urls = $goods_instruction_images;
                    $goodsInstructionImagesModel->save();
                }
            }

            // 更新目录商品数量统计
            $this->updateDirectoryGoodsCount($data['user_id'], $data['directory_id'] ?? 0, $oldDirectoryId, $isNewGoods);

            DB::commit();
        }catch(\Exception $e){
            DB::rollBack();
            throw new MyException("商品保存失败".$e->getMessage());
        }

        return ['info_id' => $user_goods_id, 'goods_id' => $goods_id, 'goods_name' => $goods_name,'goods_sku_num' => $goods_sku_num];
    }

    /**
     * 更新目录商品数量统计
     * 使用统计计算的方法更新值，不使用increment/decrement
     * 
     * @param int $userId 用户ID
     * @param int $newDirectoryId 新目录ID
     * @param int|null $oldDirectoryId 旧目录ID（更新商品时）
     * @param bool $isNewGoods 是否为新商品
     */
    private function updateDirectoryGoodsCount(int $userId, int $newDirectoryId, ?int $oldDirectoryId, bool $isNewGoods): void
    {
        // 需要更新的目录ID集合
        $directoryIdsToUpdate = [];
        
        if ($isNewGoods) {
            // 新商品，只需要更新新目录
            if ($newDirectoryId > 0) {
                $directoryIdsToUpdate[] = $newDirectoryId;
            }
        } else {
            // 更新商品，可能需要更新新旧两个目录
            if ($oldDirectoryId !== $newDirectoryId) {
                // 目录发生变化
                if ($oldDirectoryId > 0) {
                    $directoryIdsToUpdate[] = $oldDirectoryId;
                }
                if ($newDirectoryId > 0) {
                    $directoryIdsToUpdate[] = $newDirectoryId;
                }
            }
            // 如果目录没有变化，不需要更新统计
        }

        // 批量更新目录商品数量
        foreach (array_unique($directoryIdsToUpdate) as $directoryId) {
            $this->recalculateDirectoryGoodsCount($userId, $directoryId);
        }
    }

    /**
     * 重新计算指定目录的商品数量
     * 
     * @param int $userId 用户ID
     * @param int $directoryId 目录ID
     */
    private function recalculateDirectoryGoodsCount(int $userId, int $directoryId): void
    {
        // 统计该目录下的商品数量
        $goodsCount = GoodsModel::where('user_id', $userId)
            ->where('directory_id', $directoryId)
            ->where('status', 1) // 只统计有效商品
            ->count();

        // 更新目录的商品数量
        UserGoodsDirectoryModel::where('user_id', $userId)
            ->where('id', $directoryId)
            ->update(['goods_count' => $goodsCount]);
    }
}

