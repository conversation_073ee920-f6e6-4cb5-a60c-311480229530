// 特殊顶级域名列表
const specialTLDs = new Set([
  'co.uk',
  'com.cn',
  'net.cn',
  'org.cn',
  'gov.cn',
  'com.hk',
  'co.jp',
  'com.tw'
]);

// 定义消息类型
interface ClearCookiesRequest {
  action: 'clearCookies';
  hostname: string;
}

interface ClearCookiesResponse {
  success: boolean;
  count?: number;
  error?: string;
}

// 获取主域名
function getMainDomain(hostname: string): string {
  const parts = hostname.split('.');
  if (parts.length <= 2) return hostname;

  // 检查是否是特殊顶级域名
  const lastTwoParts = parts.slice(-2).join('.');
  if (specialTLDs.has(lastTwoParts)) {
    // 如果是特殊顶级域名，取最后三段
    return parts.slice(-3).join('.');
  }

  // 否则取最后两段
  return parts.slice(-2).join('.');
}

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((
  request: ClearCookiesRequest,
  sender: chrome.runtime.MessageSender,
  sendResponse: (response: ClearCookiesResponse) => void
) => {
  if (request.action === 'clearCookies') {
    clearCookies(request.hostname)
      .then(count => sendResponse({ success: true, count }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // 保持消息通道开启，等待异步响应
  }
});

// 清除指定域名下的指定cookie
async function clearSpecificCookie(domain: string, cookieName: string): Promise<void> {
  try {
    const cookies = await chrome.cookies.getAll({ domain });
    const targetCookie = cookies.find((cookie: chrome.cookies.Cookie) => cookie.name === cookieName);

    if (targetCookie) {
      await chrome.cookies.remove({
        url: `https://${targetCookie.domain}${targetCookie.path}`,
        name: targetCookie.name
      });
      console.log(`已清除域名 ${domain} 下的 ${cookieName} cookie`);
    }else{
      console.log(`未找到域名 ${domain} 下的 ${cookieName} cookie`);
    }
  } catch (error) {
    console.error(`清除指定cookie失败:`, error);
    throw error;
  }
}

// 清除指定域名的cookie
async function clearCookies(hostname: string): Promise<number> {
  try {
    const domain = getMainDomain(hostname);
    console.log('清除域名:',domain);
    // 如果是housengine.com域名,先清除sessionid cookie
    if (domain === 'housengine.com') {
      console.log('清除housengine.com域名下的sessionid cookie');
      await clearSpecificCookie(domain, 'sessionid');
    }

    const cookies = await chrome.cookies.getAll({ domain }) as chrome.cookies.Cookie[];

    for (const cookie of cookies) {
      await chrome.cookies.remove({
        url: `https://${cookie.domain}${cookie.path}`,
        name: cookie.name
      });
    }

    console.log(`已清除域名 ${domain} 下的 ${cookies.length} 个cookie`);
    return cookies.length;
  } catch (error) {
    console.error('清除cookie失败:', error);
    throw error;
  }
}
