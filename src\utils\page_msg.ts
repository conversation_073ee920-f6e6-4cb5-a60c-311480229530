import { ElNotification } from 'element-plus';
//页面通信函数
export const sendMessageToContentScript = (message:any, callback:any) => {
    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs:any) {
        console.log("获取到的TABS ");
        console.log(tabs);
        chrome.tabs.sendMessage(tabs[0].id as number, message, function (response:any) {
            if (callback) callback(response);
        });
    });
}

export const sendMessageToContentScripByTabId = (tabId:any,message:any, callback:any) => {
    chrome.tabs.sendMessage(tabId, message, function (response:any) {
        if (callback) callback(response);
    });
}

export const getTabUrlById = (tabId: number): Promise<string | null> => {
  return new Promise((resolve, reject) => {
      chrome.tabs.get(tabId, (tab) => {
          if (chrome.runtime.lastError) {
              console.error('获取标签页信息失败:', chrome.runtime.lastError.message);
              reject(null); // 处理错误情况
              return;
          }
          const tabUrl = tab.url;
          console.log('获取到的标签页 URL:', tabUrl);
          resolve(tabUrl); // 返回获取到的 URL
      });
  });
}

export const updateTabUrl = (tabId: number, newUrl: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    chrome.tabs.update(tabId, { url: newUrl }, (tab) => {
      if (chrome.runtime.lastError) {
        console.error('更新标签页 URL 失败:', chrome.runtime.lastError.message);
        reject(new Error(chrome.runtime.lastError.message));
      } else {
        console.log('标签页 URL 已更新:', newUrl);
        resolve();
      }
    });
  });
};



export const getCurrentTabUrl = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      console.log("获取到的TABS ");
      console.log(tabs);
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
      } else if (tabs.length > 0 && tabs[0].url) {
        resolve(tabs[0].url);
      } else {
        reject(new Error('无法获取当前标签页的URL'));
      }
    });
  });
};


export const contentShowNotice = (app_name:any,msg:any,currentTabId:any,type:any) => {
    switch(type){
        case 1:
          //获取评论
          ElNotification({
            title: app_name+'提示',
            message: msg,
            type: 'success'
          });
          setTimeout(() => {
            chrome.runtime.sendMessage({funType: 'pageDomOpt',tabId:currentTabId,type:101},{},(res:any)=>{
            });
          }, 2000);
          break;
        case 101:
          ElNotification({
            title: app_name+'提示',
            message: msg,
            type: 'success'
          });
          setTimeout(() => {
            chrome.runtime.sendMessage({funType: 'pageDomOpt',tabId:currentTabId,type:102},{},(res:any)=>{
            });
          }, 4000);
          break;
      }
}
