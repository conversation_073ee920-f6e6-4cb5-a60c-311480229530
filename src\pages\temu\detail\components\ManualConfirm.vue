<template>
  <div class="manual-confirm">
    <div class="confirm-message">
      已获取到该商品信息
      <br>
      <span class="directory-info">将存储到目录：{{ directoryName || '未知目录' }}</span>
    </div>
    <el-button type="primary" @click="$emit('confirm')" :loading="loading">采集入库</el-button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean;
  directoryName?: string;
}

defineProps<Props>();
defineEmits<{
  confirm: [];
}>();
</script>

<style scoped>
.manual-confirm {
  position: fixed;
  top: 80px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 10001;
  backdrop-filter: blur(5px);
  text-align: center;
}

.confirm-message {
  margin-bottom: 12px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.directory-info {
  color: #409eff;
  font-weight: 500;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .manual-confirm {
    top: 10px;
    right: 10px;
    left: 10px;
    padding: 12px;
  }
}
</style> 