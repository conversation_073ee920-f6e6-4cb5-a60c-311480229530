const fs = require('fs').promises; // Use promise-based API
const path = require('path');

const mode = process.env.NODE_ENV ?? 'development';
const isProduction = mode === 'production';
console.log('cleanFile   smode=============',mode)
console.log('cleanFiles  isProduction=====',isProduction)
console.log('cleanFiles  BTYPE=====',process.env.BTYPE)

const outputDir = isProduction ? 'tsa_build' : 'tsa';

console.log('-------cleanFiles  outputDir--------',outputDir);

const dirPath = path.join(__dirname, '..', outputDir);

console.log('-------cleanFiles  dirPath--------',dirPath);

async function cleanDirectory(directory) {
    try {
        const entries = await fs.readdir(directory, { withFileTypes: true });

        for (const entry of entries) {
            const entryPath = path.join(directory, entry.name);

            if (entry.isDirectory()) {
                // Recursively clean subdirectories
                await cleanDirectory(entryPath);
            } else if (entry.isFile()) {
                // Check and delete files
                if (entry.name.endsWith('.js.map') || entry.name.endsWith('.LICENSE.txt')) {
                    try {
                        await fs.unlink(entryPath);
                        console.log(`已删除文件: ${entryPath}`);
                    } catch (err) {
                        console.error(`删除文件失败 ${entryPath}:`, err);
                    }
                }
            }
        }
    } catch (err) {
        console.error(`读取目录失败 ${directory}:`, err);
    }
}
cleanDirectory(dirPath).catch(console.error);
