<?php

namespace App\Service\System;

use App\Models\IndexIco\IndexIco;
use App\Models\System\Setting;
use App\Service\BaseService;
use Illuminate\Support\Str;

class SettingService extends BaseService
{
    protected Setting $settingModel;

    public function __construct(Setting $setting)
    {
        $this->settingModel = $setting;
        parent::__construct();
    }

    public function list()
    {
        $result = $this->settingModel::query()->pluck('value','key');
        $list = [];
        $tmp = [];
        foreach ($result as $k=>$v){
            $tmp['key'] = $k;
            if(stristr($k,'_img')){
                $tmp['value'] = Str::startsWith($v,request()->getSchemeAndHttpHost())?uploadFilePath($v):$v;
            }else{
                $tmp['value'] = $v;
            }
            $list[] = $tmp;
        }
        return $list;
    }

    public function getValue(string $key='')
    {
        $value = '';
        $info = $this->settingModel::query()->where('key',$key)->first();
        if($info){
            $value = $info->value;
        }
        return $value;
    }

    public function update(array $data=[])
    {
        if($data){
            foreach($data as $k=>$v){
                $this->settingModel::query()->updateOrCreate(['key'=>$k],[
                    'value' =>$v,
                    'type'  => 'setting'
                ]);
            }

            foreach($data as $k=>$v){
                if(Str::endsWith($k,'_img')){
                   $v =  uploadFilePath($v,1);
                   $this->settingModel::query()->where('key',$k)->update(['value'=>$v]);
                }
            }
        }
        return '';
    }


    public function indexIcoSearchList()
    {
        $platform = config('jk.platform');
        array_unshift($platform,['name'=>'全部平台','values'=>0]);
        $check = [
            [
                'name'   => '全部状态',
                'values' => 0
            ],
            [
                'name'   => '正常模式',
                'values' => 1
            ],
            [
                'name'   => '审核模式',
                'values' => 2
            ]
        ];

        $status = [
            [
                'name'   => '正常',
                'values' => 1
            ],
            [
                'name'   => '禁用',
                'values' => 0
            ]
        ];

        return [
            'platform' => $platform,
            'check'    => $check,
            'status'   => $status
        ];
    }

    /*
     * Ico列表
     * platform  0全部  1app 2微信小程序 3抖音小程序
     * */
    public function indexIco(){
        $data = request()->all();
        $platform = $data['platform'] ?? '';
        $list = IndexIco::query()->when(is_numeric($platform) && in_array($platform,[0,1,2]),function ($query) use ($platform) {
                $query->where('platform', $platform);
            })
            ->when(isset($data['check']) && in_array($data['check'],[1,2]),function ($query) use ($data) {
                $query->where('check', (int)$data['check']-1);
            })
            ->orderBy('id','desc')
            ->paginate($this->per_page);

        $list->getCollection()->transform(function ($item) {
            if (!empty($item->image)) {
                $item->image = uploadFilePath($item->image);
            }
            return $item;
        });

        return $list;
    }

    public function indexIcoUpdate()
    {
        $data = request()->all();
        $field = $data;
        $data = (new IndexIco())->schemaFieldsFromArray($data);
        if(!empty($data['image'])){
            $data['image'] = uploadFilePath($data['image'],1);
        }
        if(!isset($data['order'])){
            $data['order'] = 1;
        }else{
            $data['order'] = (int)$data['order'];
        }
        if(!isset($data['status'])){
            $data['status'] = 1;
        }else{
            $data['status'] = (int)$data['status'];
        }
        if(!isset($data['platform'])){
            $data['platform'] = 0;
        }else{
            $data['platform'] = (int)$data['platform'];
        }
        if(!isset($data['check'])){
            $data['check'] = 0;
        }else{
            $data['check'] = (int)$data['check'];
        }
        if(isset($field['id']) && (int)$field['id']>0){
            $id = $field['id'];
            unset($data['id']);
            IndexIco::query()->where('id',$id)->update($data);
            return '';
        }else{
            $data['adddate'] = date('Y-m-d H:i:s');
            IndexIco::query()->create($data);
        }
    }

}
