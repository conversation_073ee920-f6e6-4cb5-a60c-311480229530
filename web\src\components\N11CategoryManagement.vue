<template>
  <div class="n11-category-management">


    <div class="page-header">
      <h2>N11商品分类</h2>
    </div>

    <!-- 检索条件 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="分类名称">
          <el-input 
            v-model="searchForm.name" 
            placeholder="请输入分类名称" 
            clearable 
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            检索
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- N11分类表格 -->
    <div class="table-section">
      <div class="table-container">
        <el-table 
          :data="n11CategoryList" 
          v-loading="loading"
          stripe
          border
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :default-expand-all="false"
          ref="tableRef"
          style="width: 100%"
        >
          <el-table-column label="ID" prop="id" width="120" align="right" />
          <el-table-column label="分类名称" prop="name" min-width="300" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="merged-category-name-cell">
                <!-- 中文名称和路径 -->
                <div class="category-name-row">
                  <span class="category-name" :class="{ [`level-${row.level}`]: row.level }">
                    {{ row.name }}
                    <template v-if="row.path_name">
                      <span class="path-display">【{{ row.path_name.replace(/,/g, '->') }}】</span>
                    </template>
                  </span>
                </div>
                <div class="category-name-row secondary">
                  <span class="category-name-tl">
                    {{ row.name_tl }}
                    <template v-if="row.path_name_tl">
                      <span class="path-display">【{{ row.path_name_tl.replace(/,/g, '->') }}】</span>
                    </template>
                  </span>
                  <el-button
                    type="text"
                    size="small"
                    @click="copyToClipboard(row.path_name_tl || row.name_tl)"
                    title="复制完整路径"
                    class="copy-btn"
                  >
                    <el-icon><DocumentCopy /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="层级" prop="level" width="80" />
          <el-table-column label="末级" width="80">
            <template #default="{ row }">
              <el-tag :type="row.is_leaf ? 'success' : 'info'" size="small">
                {{ row.is_leaf ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="更新时间" prop="updated_at" width="180" />
          <el-table-column label="操作" width="120" fixed="right" v-if="userInfo?.is_admin === 1">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleEditCategory(row)"
                :loading="editingCategoryId === row.id"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- N11分类编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑N11分类"
      width="600px"
      :close-on-click-modal="false"
      :z-index="3000"
      append-to-body
      class="edit-category-dialog"
    >
      <div v-if="editingCategory" class="edit-category-form">
        <!-- 分类基本信息展示 -->
        <div class="category-info-section">
          <h4>分类信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">分类ID:</span>
              <span class="value">{{ editingCategory.id }}</span>
            </div>
            <div class="info-item">
              <span class="label">父级ID:</span>
              <span class="value">{{ editingCategory.parent_id }}</span>
            </div>
            <div class="info-item">
              <span class="label">层级:</span>
              <span class="value">第{{ editingCategory.level }}级</span>
            </div>
            <div class="info-item">
              <span class="label">是否叶子:</span>
              <el-tag :type="editingCategory.is_leaf ? 'success' : 'info'" size="small">
                {{ editingCategory.is_leaf ? '是' : '否' }}
              </el-tag>
            </div>
            <div class="info-item full-width">
              <span class="label">路径:</span>
              <span class="value">{{ editingCategory.path }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">当前路径名称:</span>
              <div class="value path-names">
                <div class="path-name-primary">{{ displayPathName }}</div>
                <div v-if="displayPathNameTl" class="path-name-secondary">{{ displayPathNameTl }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑表单 -->
        <div class="edit-form-section">
          <h4>编辑分类名称</h4>
          <el-form :model="editForm" :rules="editFormRules" ref="editFormRef" label-width="120px">
            <el-form-item label="分类名称" prop="name">
              <el-input 
                v-model="editForm.name" 
                placeholder="请输入分类名称"
                maxlength="200"
                show-word-limit
                clearable
              />
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                修改后将自动更新相关的路径名称
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 影响范围预览 -->
        <div v-if="editingCategory && editForm.name !== editingCategory.name" class="impact-preview-section">
          <h4>影响范围预览</h4>
          <div class="impact-info">
            <div class="impact-item">
              <span class="impact-label">当前分类:</span>
              <span class="impact-value">
                {{ editingCategory.name }} → {{ editForm.name }}
              </span>
            </div>
            <div class="impact-item">
              <span class="impact-label">路径名称:</span>
              <div class="impact-value">
                <div class="path-change">
                  <div class="path-before">{{ displayPathName }}</div>
                  <div class="path-arrow">→</div>
                  <div class="path-after">{{ getPreviewPathName() }}</div>
                </div>
              </div>
            </div>
            <div v-if="!editingCategory.is_leaf" class="impact-item">
              <span class="impact-label">影响子分类:</span>
              <span class="impact-value warning">
                <el-icon><Warning /></el-icon>
                将同时更新所有子分类的路径名称
              </span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false" :disabled="editLoading">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmEdit" 
            :loading="editLoading"
            :disabled="!editForm.name || editForm.name === editingCategory?.name"
          >
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ElTable, ElForm } from 'element-plus'
import { Search, InfoFilled, Warning, DocumentCopy } from '@element-plus/icons-vue'
import { 
  getUserInfo as fetchUserInfo,
  getN11CategoryList,
  getN11CategoryDetail,
  updateN11Category,
  type ThirdPartyCategory,
  type UserInfo
} from '../utils/thirdPartyApi'

// 响应式数据
const loading = ref(false)
const userInfo = ref<UserInfo | null>(null)
const n11CategoryList = ref<ThirdPartyCategory[]>([])
const tableRef = ref<InstanceType<typeof ElTable>>()

// 编辑功能相关数据
const editDialogVisible = ref(false)
const editingCategory = ref<ThirdPartyCategory | null>(null)
const editingCategoryId = ref<number | null>(null)
const editLoading = ref(false)
const editFormRef = ref<InstanceType<typeof ElForm>>()

// 编辑表单数据
const editForm = reactive({
  name: ''
})

// 编辑表单验证规则
const editFormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 200, message: '分类名称长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

// 搜索表单数据
const searchForm = reactive({
  name: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 计算属性 - 格式化显示的路径名称
const displayPathName = computed(() => {
  if (!editingCategory.value?.path_name) return ''
  return editingCategory.value.path_name.replace(/,/g, '->')
})

// 计算属性 - 格式化显示的翻译路径名称
const displayPathNameTl = computed(() => {
  if (!editingCategory.value?.path_name_tl) return ''
  return editingCategory.value.path_name_tl.replace(/,/g, '->')
})

// 预览路径名称
const getPreviewPathName = () => {
  if (!editingCategory.value || !editForm.name) return ''
  
  const pathParts = editingCategory.value.path_name.split(',')
  const pathIds = editingCategory.value.path.split(',')
  
  // 找到当前分类在path中的位置
  const currentIndex = pathIds.findIndex(id => parseInt(id) === editingCategory.value!.id)
  if (currentIndex !== -1) {
    // 创建新的路径名称数组，替换对应位置的名称
    const newPathParts = [...pathParts]
    newPathParts[currentIndex] = editForm.name
    return newPathParts.join('->') // 返回格式化后的显示结果
  }
  
  return displayPathName.value
}

// 获取用户信息
const getUserInfo = async () => {
  try {
    const response = await fetchUserInfo()
    userInfo.value = response
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 获取N11分类列表
const getN11CategoryListData = async () => {
  loading.value = true
  try {
    const response = await getN11CategoryList({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...(searchForm.name && { name: searchForm.name })
    })
    
    n11CategoryList.value = response.list || []
    pagination.total = response.pagination?.total || 0
  } catch (error) {
    console.error('获取N11分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 检索
const handleSearch = () => {
  pagination.currentPage = 1
  getN11CategoryListData()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getN11CategoryListData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  getN11CategoryListData()
}

// 编辑分类
const handleEditCategory = async (category: ThirdPartyCategory) => {
  try {
    editingCategoryId.value = category.id
    
    // 获取分类详情
    const categoryDetail = await getN11CategoryDetail(category.id)
    editingCategory.value = categoryDetail
    editForm.name = categoryDetail.name
    
    editDialogVisible.value = true
  } catch (error) {
    console.error('获取分类详情失败:', error)
    ElMessage.error('获取分类详情失败')
  } finally {
    editingCategoryId.value = null
  }
}

// 确认编辑
const handleConfirmEdit = async () => {
  if (!editFormRef.value || !editingCategory.value) return
  
  try {
    // 表单验证
    await editFormRef.value.validate()
    
    editLoading.value = true
    
    // 调用更新接口
    await updateN11Category(editingCategory.value.id, {
      name: editForm.name
    })
    
    ElMessage.success('分类更新成功')
    editDialogVisible.value = false
    
    // 重新加载分类列表
    await getN11CategoryListData()
    
  } catch (error) {
    console.error('更新分类失败:', error)
    ElMessage.error('更新分类失败')
  } finally {
    editLoading.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  if (!text) {
    ElMessage.warning('没有可复制的内容')
    return
  }

  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}

// 组件挂载时初始化
onMounted(() => {
  getUserInfo()
  getN11CategoryListData()
})

// 定义组件名称
defineOptions({
  name: 'N11CategoryManagement'
})
</script>

<script lang="ts">
export default {};
</script>

<style scoped>
.n11-category-management {
  padding: 20px 0;
}

.search-section {
  background: #fff;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  width: 100%;
}

/* 确保表格不会超出容器 */
.table-section :deep(.el-table) {
  width: 100% !important;
  table-layout: fixed;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 分类名称样式 */
.category-name.level-1 {
  font-weight: bold;
  color: #409eff;
}

.category-name.level-2 {
  color: #67c23a;
}

.category-name.level-3 {
  color: #e6a23c;
}

.category-name.level-4 {
  color: #f56c6c;
}

.path-display {
  color: #999;
  margin-left: 10px;
  font-size: 12px;
}

/* 合并后的分类名称单元格样式 */
.merged-category-name-cell {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 4px 0;
}

.category-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 20px;
}

.category-name-row.secondary {
  border-top: 1px solid #f0f0f0;
  padding-top: 6px;
}

.category-name-tl {
  color: #67c23a;
  font-size: 13px;
  font-style: italic;
}

.category-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-btn {
  margin-left: 0;
  padding: 2px 4px;
  opacity: 0.6;
  transition: opacity 0.3s;
  flex-shrink: 0;
  font-size: 12px;
}

.copy-btn:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

/* 编辑对话框样式 */
.edit-category-dialog {
  z-index: 3000 !important;
}

.edit-category-form {
  max-height: 70vh;
  overflow-y: auto;
}

.category-info-section,
.edit-form-section,
.impact-preview-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
}

.category-info-section {
  background-color: #f8f9fa;
}

.edit-form-section {
  background-color: #fff;
}

.impact-preview-section {
  background-color: #fff9e6;
  border-color: #ffd666;
}

.category-info-section h4,
.edit-form-section h4,
.impact-preview-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
  word-break: break-all;
}

.path-names {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.path-name-primary {
  color: #303133;
  font-weight: 500;
}

.path-name-secondary {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.path-change {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.path-before {
  color: #909399;
  text-decoration: line-through;
}

.path-arrow {
  color: #409eff;
  font-weight: bold;
}

.path-after {
  color: #67c23a;
  font-weight: 500;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.impact-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.impact-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.impact-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.impact-value {
  color: #303133;
  flex: 1;
}

.impact-value.warning {
  color: #e6a23c;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .impact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .impact-label {
    min-width: auto;
  }
  
  .path-change {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .path-arrow {
    align-self: center;
  }
}
</style> 