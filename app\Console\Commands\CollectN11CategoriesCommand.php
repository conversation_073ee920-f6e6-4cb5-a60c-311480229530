<?php

namespace App\Console\Commands;

use App\Service\N11\ProductCategoryService;
use Illuminate\Console\Command;

class CollectN11CategoriesCommand extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'n11:collect-categories
                            {--translator=mymemory : 指定翻译服务，可选值：mymemory(默认), google}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '从JSON文件采集N11商品分类并保存到数据库';

    /**
     * 商品分类服务
     *
     * @var ProductCategoryService
     */
    protected $productCategoryService;

    /**
     * 创建命令实例
     *
     * @param ProductCategoryService $productCategoryService
     * @return void
     */
    public function __construct(ProductCategoryService $productCategoryService)
    {
        parent::__construct();
        $this->productCategoryService = $productCategoryService;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始从JSON文件采集N11商品分类...');
        
        // 获取翻译服务选项
        $translator = $this->option('translator');
        $this->info('使用翻译服务: ' . $translator);
        
        // 检查翻译选项是否有效
        if (!in_array($translator, ['mymemory', 'google'])) {
            $this->error('无效的翻译服务选项: ' . $translator . '，应为 mymemory 或 google');
            return 1;
        }

        // 传递命令行参数给服务方法
        $result = $this->productCategoryService->collectCategories(true, $this, $translator);

        if ($result['status'] == 1) {
            $this->info('采集成功: ' . $result['message']);
            $this->info('共采集了 ' . $result['count'] . ' 个分类');
            return 0; // 成功返回码
        } else {
            $this->error('采集失败: ' . $result['message']);
            return 1; // 失败返回码
        }
    }
} 