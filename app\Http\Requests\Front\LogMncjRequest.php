<?php

namespace App\Http\Requests\Front;

use App\Http\Requests\BaseRequest;

class LogMncjRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'model' => 'required',
            'subject' => 'required',
            'correct' => 'required|integer',
            'error' => 'required|integer',
            'score' => 'required',
            'total' => 'required',
            'total_score' => 'required',
            'timecost' => 'required',
            'timetotal' => 'required'
        ];
    }
}
