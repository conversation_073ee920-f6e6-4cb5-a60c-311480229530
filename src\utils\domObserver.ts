/**
 * DOM观察器工具 - 用于监听页面DOM变化并执行回调函数
 */

type ObserverOptions = {
  targetSelector?: string;  // 要观察的目标元素选择器，默认为document.body
  textPattern?: string | RegExp;  // 要匹配的文本模式
  attributePattern?: string;  // 要匹配的属性名
  timeout?: number;  // 超时时间(毫秒)，默认3000ms
  maxRetries?: number;  // 最大重试次数，用于回调函数
  initialDelay?: number;  // 初始延迟时间(毫秒)，用于回调函数
  onSuccess: (element?: Element) => void;  // 成功时的回调
  onTimeout?: () => void;  // 超时时的回调
  stopObserverOnSuccess?: boolean;  // 成功后是否停止观察，默认true
};

/**
 * 创建并启动DOM观察器
 * @param options 观察器配置选项
 * @returns 观察器实例和清理函数
 */
export const createDomObserver = (options: ObserverOptions) => {
  const {
    targetSelector = 'body',
    textPattern,
    attributePattern,
    timeout = 30000,
    maxRetries = 10,
    initialDelay = 300,
    onSuccess,
    onTimeout,
    stopObserverOnSuccess = true
  } = options;

  // 跟踪是否已处理数据
  let dataProcessed = false;
  let timeoutId: number | null = null;
  let observer: MutationObserver | null = null;

  // 处理数据的函数(支持重试机制)
  const processData = async (retryCount = 0, delay = initialDelay) => {
    if (dataProcessed) {
      console.log('数据已处理，跳过重复处理');
      return;
    }

    if (retryCount >= maxRetries) {
      console.error(`达到最大重试次数(${maxRetries})，处理失败`);
      return;
    }

    try {
      onSuccess();
      dataProcessed = true;
      if (stopObserverOnSuccess && observer) {
        observer.disconnect();
      }
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    } catch (error) {
      console.log(`尝试 ${retryCount + 1}/${maxRetries} 失败，${delay}ms后重试...`, error);
      // 递增延迟时间
      const nextDelay = Math.min(delay * 1.2, initialDelay * 2);
      setTimeout(() => processData(retryCount + 1, nextDelay), delay);
    }
  };

  // 检查内容是否匹配条件
  const checkContent = (node: Node): boolean => {
    if (textPattern) {
      const content = node.textContent || '';
      if (textPattern instanceof RegExp) {
        return textPattern.test(content);
      }
      return content.includes(textPattern);
    }
    
    if (attributePattern && node instanceof Element) {
      return node.hasAttribute(attributePattern);
    }
    
    return false;
  };

  // 递归检查节点及其子节点
  const checkNode = (node: Node): boolean => {
    if (checkContent(node)) {
      return true;
    }

    if (node.childNodes && node.childNodes.length > 0) {
      for (let i = 0; i < node.childNodes.length; i++) {
        if (checkNode(node.childNodes[i])) {
          return true;
        }
      }
    }
    
    return false;
  };

  // 创建观察器
  observer = new MutationObserver((mutations, obs) => {
    let matchFound = false;
    let matchedElement: Element | undefined;

    // 检查已有内容
    const target = document.querySelector(targetSelector);
    if (target && checkNode(target)) {
      matchFound = true;
      matchedElement = target as Element;
    }

    // 检查变化的内容
    if (!matchFound) {
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          for (const node of Array.from(mutation.addedNodes)) {
            if (checkNode(node)) {
              matchFound = true;
              if (node instanceof Element) {
                matchedElement = node;
              }
              break;
            }
          }
        } else if (mutation.type === 'characterData') {
          if (checkContent(mutation.target)) {
            matchFound = true;
            break;
          }
        }
        
        if (matchFound) break;
      }
    }

    if (matchFound) {
      console.log('观察器检测到匹配内容');
      processData(0, initialDelay);
    }
  });

  // 开始观察
  const target = document.querySelector(targetSelector) || document.body;
  observer.observe(target, {
    childList: true,
    subtree: true,
    characterData: true,
    attributes: attributePattern ? true : false
  });

  // 设置超时保障
  timeoutId = window.setTimeout(() => {
    if (!dataProcessed) {
      console.log('超时保障机制触发');
      if (onTimeout) {
        onTimeout();
      } else {
        processData();
      }
    }
    
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  }, timeout);

  // 返回清理函数
  const cleanup = () => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return {
    observer,
    cleanup
  };
}; 