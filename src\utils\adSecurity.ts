/**
 * 加密工具类，与PHP后端的AdSecurity类对应
 * 用于加密API请求数据
 */

import CryptoJS from 'crypto-js';

/**
 * 加密方法
 * @param data 要加密的数据
 * @param appKey 加密密钥
 * @returns 包含加密后的数据和签名
 */
export function encrypt(data: Record<string, any>, appKey: string): {
  encrypted_data: string;
  sign: string;
} | {
  error: string;
  original_data: Record<string, any>;
} {
  try {
    // 添加时间戳，防止重放攻击
    data.timestamp = Math.floor(Date.now() / 1000);
    
    // 添加随机签名字段
    data.system_sign = generateRandomString(30);

    // 生成签名 - 只使用system_sign字段
    const sign = generateSignWithSystemSign(data.system_sign, appKey);

    // 将数据转为JSON
    const jsonData = JSON.stringify(data);

    // 使用AES加密数据
    const encryptedData = aesEncrypt(jsonData, appKey);

    return {
      encrypted_data: encryptedData,
      sign: sign
    };
  } catch (error) {
    console.error('加密过程中出现错误:', error);
    // 返回原始数据，让系统可以回退到未加密的方式
    return {
      error: '加密失败',
      original_data: data
    };
  }
}

/**
 * 对象按键名排序
 * @param obj 要排序的对象
 * @returns 排序后的对象
 */
function sortObjectKeys(obj: Record<string, any>): Record<string, any> {
  const sortedObj: Record<string, any> = {};
  Object.keys(obj).sort().forEach(key => {
    sortedObj[key] = obj[key];
  });
  return sortedObj;
}

/**
 * 使用system_sign生成签名
 * @param systemSign system_sign字段的值
 * @param appKey 密钥
 * @returns 签名
 */
function generateSignWithSystemSign(systemSign: string, appKey: string): string {
  // 使用HMAC-SHA256和system_sign生成签名
  return CryptoJS.HmacSHA256(systemSign, appKey).toString();
}

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
function generateRandomString(length: number): string {
  const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

/**
 * 生成签名
 * @param data 数据
 * @param appKey 密钥
 * @returns 签名
 */
function generateSign(data: Record<string, any>, appKey: string): string {
  // 对数据进行排序
  const sortedData = sortObjectKeys(data);

  // 将数据转为查询字符串
  const queryString = objectToQueryString(sortedData);
  // 使用HMAC-SHA256生成签名
  const sign = CryptoJS.HmacSHA256(queryString, appKey).toString();

  return sign;
}

/**
 * 将对象转为查询字符串，确保与PHP端行为一致
 * @param obj 要转换的对象
 * @returns 查询字符串
 */
function objectToQueryString(obj: Record<string, any>): string {
  const parts: string[] = [];

  // 确保按照键名排序处理
  Object.keys(obj).sort().forEach(key => {
    const value = obj[key];
    const encodedKey = rawUrlEncode(key);

    // 处理数组值
    if (Array.isArray(value)) {
      // 对于数组值，将其转换为JSON字符串，不转义斜杠
      const jsonString = JSON.stringify(value).replace(/\\\//g, '/');
      const encodedValue = rawUrlEncode(jsonString);
      parts.push(encodedKey + '=' + encodedValue);
    } else if (value === null) {
      // 处理null值
      parts.push(encodedKey + '=');
    } else if (typeof value === 'object') {
      // 处理对象值，不转义斜杠
      const jsonString = JSON.stringify(value).replace(/\\\//g, '/');
      const encodedValue = rawUrlEncode(jsonString);
      parts.push(encodedKey + '=' + encodedValue);
    } else {
      // 其他值，直接转换为字符串
      const encodedValue = rawUrlEncode(String(value));
      parts.push(encodedKey + '=' + encodedValue);
    }
  });

  return parts.join('&');
}

/**
 * 实现与PHP的rawurlencode相同的编码行为
 * @param str 要编码的字符串
 * @returns 编码后的字符串
 */
function rawUrlEncode(str: string): string {
  // 使用encodeURIComponent，但做一些调整以匹配PHP的rawurlencode
  return encodeURIComponent(str)
    .replace(/!/g, '%21')
    .replace(/'/g, '%27')
    .replace(/\(/g, '%28')
    .replace(/\)/g, '%29')
    .replace(/\*/g, '%2A');
}

/**
 * 生成自定义随机IV
 * 这是一个备选方案，用于在CryptoJS.lib.WordArray.random无法使用时生成随机IV
 * @param length 需要生成的字节数
 * @returns WordArray对象
 */
function generateCustomIV(length: number): CryptoJS.lib.WordArray {
  // 使用时间戳和Math.random组合生成伪随机数
  const hexChars: string[] = [];
  const timestamp = Date.now().toString();

  for (let i = 0; i < length * 2; i++) {
    // 使用时间戳的数字、Math.random和循环索引组合生成随机值
    const randomValue = Math.floor((Math.random() * 16) +
                   (parseInt(timestamp[i % timestamp.length] || '0') * 3) +
                   (i * 7)) % 16;
    hexChars.push(randomValue.toString(16));
  }

  // 将十六进制字符串转换为WordArray
  return CryptoJS.enc.Hex.parse(hexChars.join(''));
}

/**
 * AES加密
 * @param data 要加密的数据
 * @param key 密钥
 * @returns 加密后的数据（Base64编码）
 */
function aesEncrypt(data: string, key: string): string {
  // 生成密钥
  const keyHash = CryptoJS.SHA256(key);
  const truncatedKey = CryptoJS.lib.WordArray.create(keyHash.words.slice(0, 8)); // 取前32字节

  let iv: CryptoJS.lib.WordArray;

  try {
    // 尝试使用CryptoJS的随机数生成器
    iv = CryptoJS.lib.WordArray.random(16);
  } catch (error) {
    // 如果失败，使用自定义的随机数生成方法
    console.warn('使用备选随机数生成方法');
    iv = generateCustomIV(16);
  }

  // 加密
  const encrypted = CryptoJS.AES.encrypt(data, truncatedKey, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  // 将IV和加密后的数据拼接在一起，并进行Base64编码
  const ivAndEncrypted = iv.concat(encrypted.ciphertext);
  return CryptoJS.enc.Base64.stringify(ivAndEncrypted);
}

/**
 * AES解密
 * @param data 加密后的数据（WordArray格式）
 * @param key 密钥
 * @returns 解密后的数据，失败返回false
 */
function aesDecrypt(data: CryptoJS.lib.WordArray, key: string): string | false {
  try {
    // 生成密钥 - 使用SHA-256哈希密钥，取前32字节
    const keyHash = CryptoJS.SHA256(key);
    // 注意：CryptoJS中一个word是4字节，所以8个word等于32字节
    const truncatedKey = CryptoJS.lib.WordArray.create(keyHash.words.slice(0, 8));

    // 提取IV（前16字节）
    if (data.sigBytes < 16) {
      console.error('数据长度不足，无法提取IV');
      return false; // 数据长度不足，无法提取IV
    }

    // 从数据中提取IV（前16字节）和加密内容
    // 注意：CryptoJS中一个word是4字节，所以4个word等于16字节
    const words = [...data.words]; // 创建副本以避免修改原始数据
    const iv = CryptoJS.lib.WordArray.create(words.slice(0, 4));

    // 计算加密内容的字节数
    const encryptedSigBytes = data.sigBytes - 16;

    // 提取加密内容
    const encrypted = CryptoJS.lib.WordArray.create(
      words.slice(4),
      encryptedSigBytes
    );
    // 尝试解密
    const decrypted = CryptoJS.AES.decrypt(
      { ciphertext: encrypted } as any,
      truncatedKey,
      {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    );

    // 转换为UTF-8字符串
    const result = decrypted.toString(CryptoJS.enc.Utf8);

    // 检查结果是否为空
    if (!result) {
      console.error('解密结果为空');
      return false;
    }

    return result;
  } catch (error) {
    console.error('AES解密失败:', error);
    return false;
  }
}

/**
 * 解密前端加密的数据
 *
 * @param params 包含 encrypted_data 和 sign 的对象
 * @param appKey 加密密钥
 * @param expireTime 过期时间（秒）
 * @returns 解密后的数据，失败返回false
 */
export function decryptFromClient(
  params: { encrypted_data: string; sign: string },
  appKey: string,
  expireTime: number = 3600
): Record<string, any> | false {
  try {
    // 检查必要参数
    if (!params.encrypted_data || !params.sign) {
      console.error('缺少必要参数');
      return false;
    }

    const encryptedData = params.encrypted_data;
    const sign = params.sign;
    // Base64解码
    const decodedData = CryptoJS.enc.Base64.parse(encryptedData);
    // AES解密
    const jsonData = aesDecrypt(decodedData, appKey);
    if (jsonData === false) {
      console.error('AES解密失败');
      return false;
    }
    // 尝试JSON解码
    let data;
    try {
      data = JSON.parse(jsonData);
    } catch (jsonError) {
      return false;
    }

    if (!data || typeof data !== 'object') {
      return false;
    }

    // 验证时间戳
    const currentTime = Math.floor(Date.now() / 1000);
    if (!data.timestamp) {
      return false;
    }
    if ((currentTime - data.timestamp) > expireTime) {
      console.error('请求数据已失效');
      return false;
    }

    // 验证system_sign字段存在
    if (!data.system_sign) {
      return false;
    }
    
    // 验证签名 - 只使用system_sign字段
    const calculatedSign = generateSignWithSystemSign(data.system_sign, appKey);
    if (calculatedSign !== sign) {
      return false;
    }

    return data;
  } catch (error) {
    console.error('解密过程中出现错误:');
    return false;
  }
}

export default {
  encrypt,
  decryptFromClient
};