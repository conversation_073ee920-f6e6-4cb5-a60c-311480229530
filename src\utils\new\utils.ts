export const getSyncStorageData = (keys:any) => {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.get(keys, function(result:any) {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(result);
        }
      });
    });
  }

/**
 * 将数据保存到Chrome同步存储中
 * @param data 要保存的数据对象
 * @returns Promise 保存操作的结果
 */
export const setSyncStorageData = (data: { [key: string]: any }): Promise<void> => {
  return new Promise((resolve, reject) => {
    chrome.storage.sync.set(data, () => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve();
      }
    });
  });
};

export const getLocalStorageData = (keys:any) => {
  return new Promise((resolve, reject) => {
    chrome.storage.local.get(keys, function(result:any) {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(result);
      }
    });
  });
}

export const setLocalStorageData = (data:any) => {
    return new Promise((resolve, reject) => {
      chrome.storage.local.set(data, function() {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve({});
        }
      });
    });
  }

export const getNowTimestamp = () => {
    return Math.round(new Date().getTime() / 1000);
}

export const getDiffTime = (timestamp:string|number) => {
    timestamp = parseInt(timestamp.toString());
    return getNowTimestamp() - timestamp;
}

export const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}



export const getCurrentFormattedTime = (format: string = 'MM-DD HH:mm:ss'): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  const formatMap: { [key: string]: string } = {
    'YYYY': year.toString(),
    'MM': month,
    'DD': day,
    'HH': hours,
    'mm': minutes,
    'ss': seconds
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => formatMap[match]);
}

export const getFormatTime = (timestamp: string | number, format: string = 'YYYY-MM-DD HH:mm:ss') => {
  timestamp = parseInt(timestamp.toString());
  const date = new Date(timestamp * 1000);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  const formatMap: { [key: string]: string } = {
    'YYYY': year.toString(),
    'MM': month,
    'DD': day,
    'HH': hours,
    'mm': minutes,
    'ss': seconds
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => formatMap[match]);
}


/*
把下面的日期格式转换成  年月日 时分秒
Fri Jul 19 2024 00:00:00 GMT+0800 (中国标准时间)
*/
export const formatIosDate = (date: any, type:number=1): string => {

  let format = 'YYYY-MM-DD';
  if(type==2){
    format = 'YYYY-MM-DD HH:mm:ss';
  }

  if (typeof date === 'string') {
    if(format=='YYYY-MM-DD'){
      if (!date.includes(" ")) {
      } else {
        return date.split(' ')[0];
      }
    }else if(format=='YYYY-MM-DD HH:mm:ss'){
      return date;
    }
    return date;
  } else if (date instanceof Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero indexed
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    if (format === 'YYYY-MM-DD') {
      return `${year}-${month}-${day}`;
    } else if (format === 'YYYY-MM-DD HH:mm:ss') {
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
  return '';
}

export const convertToTimestamp = (dateString: string): number => {
  const date = new Date(dateString.includes(' ') ? dateString : dateString + ' 00:00:00');
  return Math.floor(date.getTime() / 1000);
}

export const convertToTimestampMs = (dateString: string): number => {
  const date = new Date(dateString.includes(' ') ? dateString : dateString + ' 00:00:00');
  return date.getTime();
}

export const formatDateToRelative = (dateStr: string) => {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const twoDaysAgo = new Date(today);
  twoDaysAgo.setDate(today.getDate() - 2);

  const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const yesterdayOnly = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
  const twoDaysAgoOnly = new Date(twoDaysAgo.getFullYear(), twoDaysAgo.getMonth(), twoDaysAgo.getDate());

  const timeStr = dateStr.split(' ')[1].split(':').slice(0, 2).join(':');

  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const shortDate = `${month}-${day}`;

  if (dateOnly.getTime() === todayOnly.getTime()) {
    return `今天 ${timeStr}`;
  } else if (dateOnly.getTime() === yesterdayOnly.getTime()) {
    return `昨天 ${timeStr}`;
  } else if (dateOnly.getTime() === twoDaysAgoOnly.getTime()) {
    return `前天 ${timeStr}`;
  }
  return `${shortDate} ${timeStr}`;
};

export const isOver60Days = (startTime: string): boolean => {
  if (!startTime) return true;

  const startDate = new Date(startTime.split(' ')[0]);
  const currentDate = new Date(new Date().toISOString().split('T')[0]);
  const diffTime = currentDate.getTime() - startDate.getTime();
  const diffDays = diffTime / (1000 * 60 * 60 * 24);

  return diffDays >= 61;
};

/**
 * 生成唯一ID，格式为"时间戳十六进制-8位随机十六进制数"
 * @returns {string} 唯一ID
 */
export const generateUniqueId = (): string => {
  return `${getTimestampHex()}-${generateRandomHex()}`;
};

function getTimestampHex(): string {
  const timestamp = Math.floor(Date.now() / 1000);
  return timestamp.toString(16);
}

function generateRandomHex(): string {
  let hex = '';
  for (let i = 0; i < 8; i++) {
    hex += Math.floor(Math.random() * 16).toString(16);
  }
  return hex;
}





