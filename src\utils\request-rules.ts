interface UpdateCookieRuleParams {
  cookie: string;
  domain: string;
  ruleId?: number;
}

export const updateRequestCookieRule = async ({
  cookie,
  domain,
  ruleId = 1
}: UpdateCookieRuleParams) => {
  try {
    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: [ruleId],
      addRules: [{
        id: ruleId,
        priority: 1,
        action: {
          type: 'modifyHeaders',
          requestHeaders: [{
            header: 'Cookie',
            operation: 'set',
            value: cookie
          }]
        },
        condition: {
          domains: [domain],
          resourceTypes: [
            'xmlhttprequest',
            'main_frame',
            'sub_frame',
            'script',
            'other']
        }
      }]
    });
    return true;
  } catch (error) {
    console.error('更新请求规则失败:', error);
    return false;
  }
};

// 清除规则的方法
export const clearRequestRules = async (ruleId: number = 1) => {
  try {
    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: [ruleId]
    });
    return true;
  } catch (error) {
    console.error('清除请求规则失败:', error);
    return false;
  }
};
