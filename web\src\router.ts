import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { checkLoginExpired } from './utils/loginState'

// 导入组件
import Dashboard from './components/Dashboard.vue'
import LoginExpired from './components/LoginExpired.vue'
import StoreManagement from './components/StoreManagement.vue'
import GoodsManagement from './components/GoodsManagement.vue'
import GoodsDirectoryManagement from './components/GoodsDirectoryManagement.vue'
import GoodsListByDirectory from './components/GoodsListByDirectory.vue'
import CategoryManagement from './components/CategoryManagement.vue'
import N11CategoryManagement from './components/N11CategoryManagement.vue'
import TaskManagement from './components/TaskManagement.vue'

// 临时组件
const AccountSettings = { template: '<div class="temp-component"><h2>账户设置</h2><p>账户设置功能正在开发中...</p></div>' }
const HelpCenter = { template: '<div class="temp-component"><h2>帮助中心</h2><p>帮助中心功能正在开发中...</p></div>' }

const routes: RouteRecordRaw[] = [
  { path: '/', redirect: '/dashboard' },
  { path: '/dashboard', component: Dashboard },
  { 
    path: '/products', 
    component: GoodsManagement,
    children: [
      {
        path: '',
        name: 'GoodsDirectoryList',
        component: GoodsDirectoryManagement
      },
      {
        path: 'directory/:directoryId',
        name: 'GoodsListByDirectory',
        component: GoodsListByDirectory
      }
    ]
  },
  { path: '/store', component: StoreManagement },
  { path: '/category', component: CategoryManagement },
  { path: '/n11-category', component: N11CategoryManagement },
  { path: '/tasks', component: TaskManagement },
  { path: '/account', component: AccountSettings },
  { path: '/help', component: HelpCenter },
  { path: '/login-expired', component: LoginExpired },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 全局前置守卫：检查登录状态
router.beforeEach((to, from, next) => {
  // 如果登录已失效且不是前往登录失效页面，则重定向到登录失效页面
  if (checkLoginExpired() && to.path !== '/login-expired') {
    next('/login-expired')
  } else {
    next()
  }
})

// 添加全局样式 
const style = document.createElement('style')
style.textContent = `
  .temp-component {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .temp-component h2 {
    margin-top: 0;
    color: #333;
    font-size: 1.5rem;
    margin-bottom: 15px;
  }
  .temp-component p {
    color: #666;
    font-size: 1rem;
  }
`
document.head.appendChild(style)

export default router
