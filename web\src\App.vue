<template>
  <!-- 如果登录失效，只显示路由视图（登录失效页面） -->
  <div v-if="isLoginExpired">
    <router-view></router-view>
  </div>
  
  <!-- 正常状态下显示完整界面 -->
  <div v-else>
    <div class="header">
      <h1>跨境蜂 - 用户中心</h1>
      <div class="user-info" v-if="userInfo.isLogin">
        <span class="username">欢迎，<span>{{ userInfo.phone || '用户' }}</span></span>
        <span v-if="userInfo.isVip" class="vip-badge">
          <span class="vip-icon">👑</span> VIP会员
        </span>
        <span v-else class="non-vip-badge">普通会员</span>
        <span v-if="userInfo.isAdmin" class="admin-badge">
          <span class="admin-icon">🔧</span> 管理员
        </span>
      </div>
      <div class="user-info" v-else>
        <span class="login-prompt">请先登录</span>
      </div>
    </div>

    <div class="container">
      <div class="main-content">
        <div class="sidebar">
          <div
            v-for="(item, index) in filteredMenuItems"
            :key="index"
            :class="['menu-item', { active: currentRoute === item.route }]"
            @click="navigateTo(item.route)"
          >
            {{ item.name }}
          </div>
        </div>

        <div class="content-area">
          <router-view></router-view>
        </div>
      </div>

      <div class="footer">
        © {{ new Date().getFullYear() }} 跨境蜂 版权所有
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { isLoginExpired, checkLoginExpired } from './utils/loginState'
import { userInfo, fetchAndUpdateUserInfo, handleUserLogout, onUserInfoChange } from './utils/userStore'

const router = useRouter()
const route = useRoute()

const menuItems = [
  { name: '控制面板', route: '/dashboard' },
  { name: '店铺管理', route: '/store' },
  { name: '商品分类', route: '/category', adminOnly: true },
  { name: 'N11分类', route: '/n11-category' , adminOnly: true },
  { name: '商品管理', route: '/products' },
  { name: '任务列表', route: '/tasks' },
]

// 过滤菜单项，根据用户权限显示
const filteredMenuItems = computed(() => {
  console.log('过滤菜单项，当前用户信息:', {
    isLogin: userInfo.isLogin,
    isAdmin: userInfo.isAdmin,
    phone: userInfo.phone
  })
  
  const filtered = menuItems.filter(item => {
    // 如果菜单项需要管理员权限，检查用户是否为管理员
    if (item.adminOnly) {
      const hasAccess = userInfo.isAdmin === true
      console.log(`菜单项 "${item.name}" 需要管理员权限，用户是否有权限:`, hasAccess)
      return hasAccess
    }
    // 其他菜单项都显示
    return true
  })
  
  console.log('过滤后的菜单项:', filtered.map(item => item.name))
  return filtered
})

const currentRoute = computed(() => route.path)

function navigateTo(path: string) {
  // 检查登录状态，如果已失效则跳转到登录失效页面
  if (checkLoginExpired()) {
    router.push('/login-expired')
    return
  }
  router.push(path)
}

// 设置消息监听器
const setupMessageListeners = () => {
  // 监听来自popup的退出登录消息
  const messageListener = (message: any) => {
    if (message.type === 'USER_LOGOUT') {
      console.log('收到退出登录消息:', message)
      handleUserLogout()
    }
  }
  
  // 监听chrome runtime消息
  if (chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener(messageListener)
  }
  
  // 监听storage变化
  const storageListener = (changes: any, namespace: any) => {
    if (namespace === 'sync' && changes.is_login) {
      const newValue = changes.is_login.newValue
      if (newValue === false && userInfo.isLogin === true) {
        console.log('检测到storage中登录状态变化，处理退出登录')
        handleUserLogout()
      }
    }
  }
  
  if (chrome.storage && chrome.storage.onChanged) {
    chrome.storage.onChanged.addListener(storageListener)
  }
  
  return () => {
    // 清理监听器
    if (chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.removeListener(messageListener)
    }
    if (chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.removeListener(storageListener)
    }
  }
}

// 监听登录失效状态变化
watch(isLoginExpired, (expired) => {
  if (expired && route.path !== '/login-expired') {
    router.push('/login-expired')
  }
})

// 清理函数
let cleanupListeners: (() => void) | null = null

onMounted(async () => {
  // 检查登录失效状态
  if (checkLoginExpired()) {
    router.push('/login-expired')
    return
  }

  // 设置消息监听器
  cleanupListeners = setupMessageListeners()

  // 加载用户信息（只在App.vue中调用API）
  await fetchAndUpdateUserInfo()

  // 设置页面标题
  if (chrome.runtime && chrome.runtime.getManifest) {
    const manifest = chrome.runtime.getManifest()
    document.title = `${manifest.name} - 用户中心 v${manifest.version}`
  }
})

onUnmounted(() => {
  // 清理监听器
  if (cleanupListeners) {
    cleanupListeners()
  }
})
</script>

<style>
/* 全局样式修复 */
html, body {
  overflow-x: hidden; /* 防止水平滚动条 */
}

.el-table {
  width: 100% !important;
  max-width: 100%;
}

.el-table__body, 
.el-table__header {
  width: 100% !important;
}
</style>

<style scoped>
.header {
  background-color: #eef4ff;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  font-size: 0.9rem;
  color: #555;
}

.username span {
  font-weight: bold;
  color: #333;
}

.vip-badge {
  background-color: #f8d56c;
  color: #8b6d0c;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 4px;
}

.vip-icon {
  font-size: 0.9rem;
}

.non-vip-badge {
  background-color: #e0e0e0;
  color: #666;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.login-prompt {
  color: #999;
  font-size: 0.9rem;
}

.container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}

.main-content {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: calc(100vh - 180px);
}

.sidebar {
  width: 200px;
  background-color: #f5f7fa;
  padding: 20px 0;
  flex-shrink: 0;
}

.menu-item {
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s;
  color: #555;
}

.menu-item:hover {
  background-color: #e8f0fe;
  color: #1890ff;
}

.menu-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-right: 3px solid #1890ff;
}

.content-area {
  flex: 1;
  padding: 20px;
  overflow-x: hidden;
  min-width: 0;
}

.footer {
  text-align: center;
  padding: 15px;
  color: #999;
  font-size: 0.8rem;
  margin-top: 20px;
}

.admin-badge {
  background-color: #67c23a;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 4px;
}

.admin-icon {
  font-size: 0.9rem;
}
</style>