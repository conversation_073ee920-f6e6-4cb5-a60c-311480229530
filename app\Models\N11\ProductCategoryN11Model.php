<?php

namespace App\Models\N11;

use App\Models\BaseModel;

class ProductCategoryN11Model extends BaseModel
{
    protected $table = 'product_category_n11';
    
    protected $fillable = [
        'name',
        'name_en', 
        'name_tl',
        'parent_id',
        'is_leaf',
        'level',
        'path',
        'path_name',
        'path_name_tl',
        'sort_order',
        'status',
        'relation_status'
    ];

    protected $casts = [
        'parent_id' => 'integer',
        'is_leaf' => 'boolean',
        'level' => 'integer',
        'sort_order' => 'integer',
        'status' => 'integer',
        'relation_status' => 'integer'
    ];

    /**
     * 获取父分类
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * 获取所有子分类（递归）
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：叶子节点
     */
    public function scopeLeaf($query)
    {
        return $query->where('is_leaf', 1);
    }

    /**
     * 作用域：指定层级
     */
    public function scopeLevel($query, $level)
    {
        return $query->where('level', $level);
    }
}