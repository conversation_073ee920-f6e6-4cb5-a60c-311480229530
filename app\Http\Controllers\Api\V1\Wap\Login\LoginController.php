<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\Login;

use App\Http\Controllers\Api\Controller;
use App\Service\User\Auth\LoginService;

class LoginController extends Controller
{
    protected LoginService $loginService;
    public function __construct(LoginService $loginService)
    {
        $this->loginService = $loginService;
        parent::__construct();
    }

    public function errorLog()
    {
        return $this->success($this->loginService->errorLog());
    }

    public function loginToken()
    {
        return $this->success($this->loginService->loginToken());
    }

    /**
     * @throws \App\Exceptions\MyException
     */
    public function loginDouyin(): \Illuminate\Http\JsonResponse
    {
        return $this->success($this->loginService->loginDouyin());
    }

    /**
     * @throws \App\Exceptions\MyException
     */
    public function bindAccountDouyin(): \Illuminate\Http\JsonResponse
    {
        return $this->success($this->loginService->bindAccountDouyin());
    }

    public function createAccountDouyin(): \Illuminate\Http\JsonResponse
    {
        return $this->success($this->loginService->createAccountDouyin());
    }

    public function getPhoneWx()
    {
        $user_id = ($this->request->user)['id'];
        return $this->success($this->loginService->getPhoneWx(['user_id'=>$user_id]));
    }

    public function getPhoneDy()
    {
        $user_id = ($this->request->user)['id'];
        return $this->success($this->loginService->getPhoneDy(['user_id'=>$user_id]));
    }




    public function loginWx()
    {
        return $this->success($this->loginService->loginWx());
    }

    /**
     * @throws \App\Exceptions\MyException
     */
    public function bindAccountWx(): \Illuminate\Http\JsonResponse
    {
        return $this->success($this->loginService->bindAccountWx());
    }

    public function createAccountWx(): \Illuminate\Http\JsonResponse
    {
        return $this->success($this->loginService->createAccountWx());
    }

    public function loginWxApp()
    {
        return $this->success($this->loginService->loginWxApp());
    }


    public function loginWxAppOnlyAuthorize(string $code='')
    {
        return $this->success($this->loginService->loginWxAppOnlyAuthorize($code));
    }

    public function bindAccountWxApp(): \Illuminate\Http\JsonResponse
    {
        return $this->success($this->loginService->bindAccountWxApp());
    }

    public function createAccountWxApp(): \Illuminate\Http\JsonResponse
    {
        return $this->success($this->loginService->createAccountWxApp());
    }

    public function debugDouyin(int $id=0)
    {
        return $this->success($this->loginService->debugDouyin($id));
    }

    public function accountInvite()
    {
        return $this->success($this->loginService->accountInvite());
    }


}
