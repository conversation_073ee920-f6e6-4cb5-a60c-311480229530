// https://github.com/mzabriskie/axios
import axios, { AxiosResponse } from 'axios'
import fetchAdapter from '@vespaiach/axios-fetch-adapter';
import { decryptFromClient } from '@/utils/adSecurity';
import config from '@/config';

const http = axios.create({
    adapter: fetchAdapter
})
// 允许携带cookie
http.defaults.withCredentials = true
// 拦截request,设置全局请求为ajax请求

http.interceptors.request.use((config) => {
  //@ts-ignore
  config.headers['X-Requested-With'] = 'XMLHttpRequest'
  return config
})
// 拦截响应response，并做一些错误处理
http.interceptors.response.use((response) => {
    // 成功响应处理
    // 检查响应数据是否符合加密数据的格式
    if (response.data &&
        typeof response.data === 'object' &&
        'code' in response.data &&
        'status' in response.data &&
        'data' in response.data &&
        response.data.data &&
        typeof response.data.data === 'object' &&
        'encrypted_data' in response.data.data &&
        'sign' in response.data.data) {

        try {
            // 使用已导入的解密方法和配置
            // 解密数据
            const decryptedData = decryptFromClient(
                {
                    encrypted_data: response.data.data.encrypted_data,
                    sign: response.data.data.sign
                },
                config.ay
            );

            // 如果解密成功，替换原始响应中的data字段
            if (decryptedData !== false) {
                console.log("--------------解密成功后的数据-------------");
                response.data.data = decryptedData.data;
                console.log(response.data.data);
            } else {
                console.error('解密响应数据失败');
            }
        } catch (error) {
            console.error('解密响应数据过程中出现异常:', error);
        }
    }

    return response;
}, (err) => {
    // 这里是返回状态码不为200时候的错误处理
    if (err && err.response) {
        switch (err.response.status) {
            case 400:
                err.message = '请求错误'
                break

            case 401:
                err.message = '未授权，请登录'
                break

            case 403:
                err.message = '拒绝访问'
                break

            case 404:
                err.message = `请求地址出错: ${err.response.config.url}`
                break

            case 408:
                err.message = '请求超时'
                break

            case 500:
                err.message = `服务器内部错误: ${err.response.data.error?.message || ''}`
                break

            case 501:
                err.message = '服务未实现'
                break

            case 502:
                err.message = '网关错误'
                break

            case 503:
                err.message = '服务不可用'
                break

            case 504:
                err.message = '网关超时'
                break

            case 505:
                err.message = 'HTTP版本不受支持'
                break

            default:
        }
    }
    return Promise.reject(err)
})
export default http
