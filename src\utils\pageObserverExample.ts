/**
 * 页面观察器使用示例 - 提供各种常见场景的应用示例
 */
import { createDomObserver } from './domObserver';

/**
 * 示例1: 观察特定文本内容的出现
 * 用于等待页面中某个特定文本的出现后执行操作
 */
export const observeSpecificText = (textToWatch: string, callback: () => void) => {
  return createDomObserver({
    textPattern: textToWatch,
    onSuccess: callback
  });
};

/**
 * 示例2: 观察特定属性的元素
 * 用于等待具有特定属性的元素出现后执行操作
 */
export const observeElementWithAttribute = (attributeName: string, callback: (element?: Element) => void) => {
  return createDomObserver({
    attributePattern: attributeName,
    onSuccess: callback
  });
};

/**
 * 示例3: 观察特定选择器元素的变化
 * 用于观察页面上特定元素的内容变化
 */
export const observeElementBySelector = (selector: string, textToWatch: string, callback: () => void) => {
  return createDomObserver({
    targetSelector: selector,
    textPattern: textToWatch,
    onSuccess: callback
  });
};

/**
 * 示例4: 使用正则表达式观察复杂模式
 * 用于匹配更复杂的内容模式
 */
export const observeWithRegex = (pattern: RegExp, callback: () => void) => {
  return createDomObserver({
    textPattern: pattern,
    onSuccess: callback
  });
};

/**
 * 示例5: 带有自定义超时处理的观察器
 * 用于需要特定超时处理的场景
 */
export const observeWithCustomTimeout = (
  textToWatch: string, 
  onSuccessCallback: () => void,
  onTimeoutCallback: () => void,
  timeoutMs = 5000
) => {
  return createDomObserver({
    textPattern: textToWatch,
    timeout: timeoutMs,
    onSuccess: onSuccessCallback,
    onTimeout: onTimeoutCallback
  });
};

/**
 * 示例6: JSON数据提取观察器
 * 适用于需要从页面中提取JSON数据的场景
 */
export const observeAndExtractJson = <T>(
  jsonPattern: string | RegExp,
  dataProcessor: (jsonText: string) => T,
  onDataExtracted: (data: T) => void,
  onError?: (error: Error) => void
) => {
  return createDomObserver({
    textPattern: jsonPattern,
    onSuccess: () => {
      try {
        const htmlContent = document.documentElement.outerHTML;
        const match = typeof jsonPattern === 'string' 
          ? htmlContent.match(new RegExp(jsonPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '(.+?)(;|<)'))
          : htmlContent.match(jsonPattern);
        
        if (match && match[1]) {
          const jsonText = match[1];
          const processedData = dataProcessor(jsonText);
          onDataExtracted(processedData);
        } else {
          throw new Error('无法从页面提取JSON数据');
        }
      } catch (error) {
        console.error('提取JSON数据时出错:', error);
        if (onError && error instanceof Error) {
          onError(error);
        }
      }
    }
  });
}; 