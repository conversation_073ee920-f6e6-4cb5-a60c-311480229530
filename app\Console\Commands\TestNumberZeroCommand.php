<?php
namespace App\Console\Commands;

use App\Utils\BankUtil;
use App\Models\Card\Card;
use Illuminate\Support\Str;
use App\Jobs\SSEDataDealJob;
use App\Jobs\GoodsDataDealJob;
use Illuminate\Console\Command;
use App\Service\User\UserService;
use App\Service\User\Auth\LoginService;
use Illuminate\Support\Facades\DB;
use App\Service\Index\IndexService;
use App\Utils\Tools;
use App\Utils\GoodsNameTrait;

class TestNumberZeroCommand extends Command
{
    use GoodsNameTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    public function handle(UserService $userService,LoginService $loginService)
    {
        $goodsName = '【30 adet Şövalye 5 Ana/Simli】 30 parça gümüş renkli sofra takımı, 6 kişi için u<PERSON>, y<PERSON><PERSON><PERSON> kaliteli paslanmaz çelik ve ayna cilalı sofra takımı, çatal, bıçak, kaşık dahil, ev, mutfak, restoran için uygun, dayanıklı ve korozyona karşı dirençli, bulaşık makinesinde yıkanabilir';
        $goodsName = $this->truncateGoodsName($goodsName);
        echo $goodsName;
    }



}
