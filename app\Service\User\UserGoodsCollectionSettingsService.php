<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\UserGoodsCollectionSettingsModel;
use App\Models\User\UserGoodsDirectoryModel;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Validator;

class UserGoodsCollectionSettingsService extends BaseService
{
    /**
     * 获取用户采集设置
     */
    public function getUserCollectionSettings(int $userId): array
    {
        $settings = UserGoodsCollectionSettingsModel::findByUserId($userId);
        
        if (!$settings) {
            // 用户未设置过，返回默认值
            return [
                'has_settings' => false,
                'default_directory_id' => 0,
                'default_directory_name' => '',
                'collection_mode' => UserGoodsCollectionSettingsModel::COLLECTION_MODE_AUTO,
                'collection_mode_name' => '自动采集',
                'no_remind_until' => null,
                'is_in_no_remind_period' => false,
                'is_default_directory_valid' => false,
                'need_setup' => true
            ];
        }

        // 检查默认目录是否有效
        $isDirectoryValid = $settings->isDefaultDirectoryValid();
        $directoryName = '';
        
        if ($isDirectoryValid && $settings->defaultDirectory) {
            $directoryName = $settings->defaultDirectory->name;
        }

        return [
            'has_settings' => true,
            'default_directory_id' => $settings->default_directory_id,
            'default_directory_name' => $directoryName,
            'collection_mode' => $settings->collection_mode,
            'collection_mode_name' => $settings->getCollectionModeName(),
            'no_remind_until' => $settings->no_remind_until ? date('Y-m-d H:i:s', strtotime($settings->no_remind_until)) : null,
            'is_in_no_remind_period' => $settings->isInNoRemindPeriod(),
            'is_default_directory_valid' => $isDirectoryValid,
            'need_setup' => !$isDirectoryValid || $settings->default_directory_id == 0
        ];
    }

    /**
     * 保存用户采集设置
     */
    public function saveUserCollectionSettings(int $userId, array $data): array
    {
        // 验证数据
        $this->validateSettingsData($data, $userId);

        $settings = UserGoodsCollectionSettingsModel::findByUserId($userId);
        
        $settingsData = [
            'user_id' => $userId,
            'default_directory_id' => $data['default_directory_id'],
            'collection_mode' => $data['collection_mode'],
        ];

        // 处理24小时免提醒
        if (isset($data['no_remind_24h']) && $data['no_remind_24h']) {
            $settingsData['no_remind_until'] = now()->addHours(24);
        } else {
            // 如果没有勾选24小时免提醒，保留原有的no_remind_until值
            if ($settings && $settings->no_remind_until) {
                // 检查原有时间是否已到期
                if (now()->lt($settings->no_remind_until)) {
                    // 未到期，保留原值
                    $settingsData['no_remind_until'] = $settings->no_remind_until;
                } else {
                    // 已到期，清空
                    $settingsData['no_remind_until'] = null;
                }
            } else {
                $settingsData['no_remind_until'] = null;
            }
        }

        if ($settings) {
            // 更新现有设置
            $settings->update($settingsData);
        } else {
            // 创建新设置
            $settings = UserGoodsCollectionSettingsModel::create($settingsData);
        }

        return [
            'message' => '设置保存成功',
            'settings' => $this->getUserCollectionSettings($userId)
        ];
    }

    /**
     * 获取用户可用的目录列表
     */
    public function getUserAvailableDirectories(int $userId): array
    {
        $directories = UserGoodsDirectoryModel::where('user_id', $userId)
            ->where('status', 1)
            ->orderBy('sort_order', 'desc')
            ->orderBy('id', 'desc')
            ->select(['id', 'name', 'description', 'goods_count'])
            ->limit(100)
            ->get()
            ->toArray();

        return [
            'list' => $directories
        ];
    }

    /**
     * 验证设置数据
     */
    private function validateSettingsData(array $data, int $userId): void
    {
        $rules = [
            'default_directory_id' => 'required|integer|min:1',
            'collection_mode' => 'required|integer|in:1,2,3',
            'no_remind_24h' => 'sometimes|boolean',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new MyException($validator->errors()->first());
        }

        // 验证目录是否属于当前用户且有效
        $directoryExists = UserGoodsDirectoryModel::where('id', $data['default_directory_id'])
            ->where('user_id', $userId)
            ->where('status', 1)
            ->exists();

        if (!$directoryExists) {
            throw new MyException('选择的目录不存在或已被禁用');
        }
    }

    /**
     * 检查用户是否需要设置采集参数
     */
    public function checkUserNeedSetup(int $userId): array
    {
        $settings = $this->getUserCollectionSettings($userId);
        
        $needSetup = false;
        $reason = '';

        if (!$settings['has_settings']) {
            $needSetup = true;
            $reason = '首次使用，需要完成采集设置';
        } elseif (!$settings['is_default_directory_valid']) {
            $needSetup = true;
            $reason = '默认目录不存在或已被禁用，需要重新设置';
        } elseif ($settings['is_in_no_remind_period']) {
            $needSetup = false;
            $reason = '在24小时免提醒期内';
        } else {
            // 新增逻辑：如果用户已设置且目录有效，但采集模式是自动或手动，且没有设置免提醒时间
            // 这种情况下不需要显示设置弹窗，但在商品采集时会触发临时目录选择
            $needSetup = false;
            $reason = '设置已完成';
        }

        return [
            'need_setup' => $needSetup,
            'reason' => $reason,
            'settings' => $settings
        ];
    }
}