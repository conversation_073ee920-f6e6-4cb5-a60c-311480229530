import { DOMFinderConfig } from '@/types/domFinder';

export const defaultConfig: DOMFinderConfig = {
  shadowRoot: {
    selector: 'wujie-app[class="wujie_iframe"]',
    attribute: 'class'  // 可选的属性验证
  },
  selectors: {
    primary: [
      '.ant-table-fixed-right .ant-table-tbody tr[data-row-key]',
      '.ant-table-fixed-right tr.ant-table-row'
    ],
    fallback: [
      '.ant-table-tbody tr[data-row-key]',
      '.ant-table tr.ant-table-row'
    ],
    context: [
      '.ant-table-fixed-right',
      '.ant-table-body-inner table'
    ],
    controls: {
      radioGroup: '.weui-desktop-radio-group.radio-group',
      pagination: '.weui-desktop-pagination',
      paginationElements: {
        number: 'weui-desktop-pagination__num',
        button: 'weui-desktop-btn',
        buttonDefault: 'weui-desktop-btn_default',
        link: 'weui-desktop-link',
        ellipsis: 'weui-desktop-pagination__ellipsis'
      }
    }
  },
  validation: {
    attributes: ['data-row-key'],
    structure: [
      'tr',
      'tr[class*="ant-table"]'
    ]
  }
};

// 支持从远程更新配置
export async function fetchConfig(url: string): Promise<DOMFinderConfig> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('配置获取失败');
    }
    const config = await response.json();
    return config;
  } catch (error) {
    console.error('获取配置失败，使用默认配置:', error);
    return defaultConfig;
  }
}
