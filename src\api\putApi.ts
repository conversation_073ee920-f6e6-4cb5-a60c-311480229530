import { AxiosRequestConfig } from 'axios';
import http from './axios'
import { modifyRequestHeaders, removeRequestRule } from '@/utils/requestRuleManager';
import { requestIdManager } from '@/utils/requestIdManager';
import { processRequestParams, processRequestHeaders } from '@/utils/requestMiddleware';
export default function (api: { [x: string]: (body: any, callback: (arg0: any) => any, headers?: {}) => void }, name: string | number, initialUrl: string, {
  initialMethod = 'get',
} = {}) {
    api[name] = async (body: any, callback: (arg0: any) => any, headers = {}, config = {}) => {
        console.log('config--body----------',body);
        const finalUrl = config.url || initialUrl;
        const finalMethod = config.method || initialMethod;
        const finalRequestType = config.requestType || '';
        const auth = config.auth || false;
        const encrypto = config.encrypto ?? undefined;
        const timeout = config.timeout || 15000;
        const { url: _, method: __, requestType: ___, auth: ____, encrypto: _____, timeout: ______, ...restConfig } = config;
        config = restConfig;
        if(finalRequestType != 'arraybuffer'){
          Object.assign(http.defaults.headers.common, headers)
        }else{
          Object.assign(http.defaults.headers.common, headers)
        }

        let requestId = '';
        let urlPattern = '';
        if(finalRequestType != 'arraybuffer' && headers && Object.keys(headers).length > 0){
          requestId = requestIdManager.generateId().toString();
          urlPattern = finalUrl;
        }

        //如果headers['content-type']包含'application/json'
        if (headers['content-type'] && headers['content-type'].includes('application/json')) {
          http.defaults.headers.common['Content-Type'] = 'application/json';
        }
        if (headers['accept'] && headers['accept'].includes('application/json')) {
          http.defaults.headers.common['accept'] = 'application/json, text/plain, */*';
        }
        if(finalRequestType == 'arraybuffer'){
          const keysToRemove = ['Appid', 'Priority', 'Referer', 'Reqparamstrans', 'Type', 'Url', 'X-Apppid', 'X-Secsdk-Csrf-Token', 'X-Sub-Web-Id'];
          keysToRemove.forEach(key => delete http.defaults.headers.common[key]);
        }else{
          if (headers && Object.keys(headers).length > 0 && requestId && urlPattern) {
            // 在发送请求前设置Cookie规则
            await modifyRequestHeaders({
              requestId,
              headers,
              urlPattern
            });
          }
        }

      try {
        // 处理请求头，添加认证信息
        const processedHeaders = await processRequestHeaders(headers, { auth, encrypto, url: finalUrl });

        const axiosConfig: AxiosRequestConfig = {
          url: finalUrl,
          method: finalMethod,
          withCredentials: true,
          timeout: timeout,
          headers: {
            ...processedHeaders,
            Cookie: processedHeaders.Cookie || http.defaults.headers.common.Cookie //这里设置其实是无效的
          },
          ...restConfig
        };


        if (finalMethod === 'get') {
          // 对于GET请求，处理查询参数
          axiosConfig.params = encrypto !== undefined
            ? (encrypto ? processRequestParams(body, { auth, encrypto, url: finalUrl }) : body)
            : processRequestParams(body, { auth, url: finalUrl });
        } else if (finalRequestType === 'arraybuffer') {
          // 对于arraybuffer请求，不进行加密处理
          axiosConfig.data = body;
          axiosConfig.headers = {
            ...axiosConfig.headers,
            'Content-Type': 'application/x-protobuf'
          };
          axiosConfig.responseType = 'arraybuffer';
        } else {
          // 对于其他请求，处理请求体
          console.log('axiosConfig.data----------',axiosConfig.data);
          console.log('encrypto----------',encrypto);
          console.log('auth----------',auth);
          console.log('finalUrl----------',finalUrl);
          axiosConfig.data = encrypto !== undefined
            ? (encrypto ? processRequestParams(body, { auth, encrypto, url: finalUrl }) : body)
            : processRequestParams(body, { auth, url: finalUrl });
        }

        // 记录处理后的请求信息
        try {
          const response = await http(axiosConfig);
          callback(response);
        } catch (error: any) {
          console.error(`Error in ${name} API call:`, error);
          // 如果有错误响应,返回错误响应
          if (error.response) {
            callback({
              status: error.response.status,
              data: error.response.data,
              headers: error.response.headers
            });
          } else {
            // 如果没有错误响应,返回一个通用错误
            callback({
              status: 500,
              data: {
                code: -1,
                msg: error.message || '请求失败',
                data: null
              }
            });
          }
        }
      } finally {
        // 移除Cookie规则
        if(finalRequestType != 'arraybuffer' && headers && Object.keys(headers).length > 0 && requestId && urlPattern){
          await removeRequestRule(requestId);
          requestIdManager.releaseId(Number(requestId));
        }
      }
    }
}

