<?php
namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\UserAccountModel;
use App\Exceptions\MyException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class StoreService extends BaseService
{
    /**
     * 获取店铺列表（分页）
     */
    public function getStoreList(int $userId, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $accountType = $params['account_type'] ?? null;
        $status = $params['status'] ?? null;
        $accountName = $params['account_name'] ?? null;
        $brand = $params['brand'] ?? null;
        $integratorName = $params['integrator_name'] ?? null;

        // 构建查询
        $query = UserAccountModel::byUserId($userId);

        // 按账户类型筛选
        if ($accountType && in_array($accountType, [1, 2])) {
            $query->byAccountType($accountType);
        }

        if($accountName){
            $query->where('account_name', 'like', "%{$accountName}%");
        }

        if($brand){
            $query->where('brand', 'like', "%{$brand}%");
        }

        if(is_numeric($status) && in_array($status, [0, 1, 2])){
            $query->where('status', $status);
        }

        if($integratorName){
            $query->where('integrator_name', 'like', "%{$integratorName}%");
        }

        // 排序
        $query->orderBy('id', 'desc');

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        
        $stores = $query->offset($offset)
                       ->limit($pageSize)
                       ->get()
                       ->map(function($store) {
                           return $this->formatStoreData($store);
                       });

        return [
            'list' => $stores,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 创建店铺
     */
    public function createStore(int $userId, array $data): array
    {
        // 验证数据
        $this->validateStoreData($data, true);

        // 检查同一用户下是否已存在相同的账户ID和类型
        $exists = UserAccountModel::byUserId($userId)
            ->where('account_type', $data['account_type'])
            ->where('account_name', $data['account_name'])
            ->exists();

        if ($exists) {
            throw new MyException('您添加的店铺已存在');
        }

        // 创建店铺
        $storeData = array_merge($data, [
            'user_id' => $userId,
            'cookie_status' => 1,
            'price_rate' => $data['price_rate'] ?? 1.00,
            'price_add' => $data['price_add'] ?? 0.00,
            'price_subtract' => $data['price_subtract'] ?? 0.00,
        ]);

        $store = UserAccountModel::create($storeData);

        return [
            'id' => $store->id,
            'message' => '店铺创建成功'
        ];
    }

    /**
     * 更新店铺
     */
    public function updateStore(int $userId, array $data): array
    {
        // 验证数据
        $this->validateStoreData($data, false);

        if (!isset($data['id'])) {
            throw new MyException('店铺ID不能为空');
        }

        // 查找店铺并验证所有权
        $store = UserAccountModel::find($data['id']);
        
        if (!$store) {
            throw new MyException('店铺不存在');
        }

        if (!$store->belongsToUser($userId)) {
            throw new MyException('无权限操作此店铺');
        }
        if(!empty($data['account_name'])){
            $data['account_name'] = trim($data['account_name']);
        }
        // 如果更新了account_type或account_name，检查是否重复
        if (isset($data['account_type']) || isset($data['account_name'])) {
            $accountType = $data['account_type'] ?? $store->account_type;
            $accountName = $data['account_name'] ?? $store->account_name;
            
            $exists = UserAccountModel::byUserId($userId)
                ->where('account_type', $accountType)
                ->where('account_name', $accountName)
                ->where('id', '!=', $store->id)
                ->exists();

            if ($exists) {
                throw new MyException('该店铺名称已存在');
            }
        }

        // 更新店铺
        $updateData = $data;
        unset($updateData['id']);
        $store->update($updateData);

        return [
            'message' => '店铺更新成功'
        ];
    }

    /**
     * 批量更新店铺
     */
    public function batchUpdateStore(int $userId, array $data): array
    {
        $count = UserAccountModel::where('user_id', $userId)->whereIn('id', $data['ids'])->count();
        if($count != count($data['ids'])){
            throw new MyException('参数错误');
        }
        $ids = $data['ids'];
        unset($data['ids']);
        if(count($data) == 0){
            throw new MyException('请至少选择一项');
        }
        UserAccountModel::where('user_id', $userId)->whereIn('id', $ids)->update($data);
        return [
            'message' => '店铺更新成功'
        ];
    }
    /**
     * 删除店铺
     */
    public function deleteStore(int $userId, int $storeId): array
    {
        throw new MyException('您没有权限进行该操作');
        // 验证店铺ID
        if (!$storeId) {
            throw new MyException('店铺ID不能为空');
        }

        // 查找店铺并验证所有权
        $store = UserAccountModel::find($storeId);
        
        if (!$store) {
            throw new MyException('店铺不存在');
        }

        if (!$store->belongsToUser($userId)) {
            throw new MyException('无权限操作此店铺');
        }

        // 删除店铺
        $store->delete();

        return [
            'message' => '店铺删除成功'
        ];
    }

    /**
     * 获取店铺详情
     */
    public function getStoreDetail(int $userId, int $storeId): array
    {
        // 验证店铺ID
        if (!$storeId) {
            throw new MyException('店铺ID不能为空');
        }

        // 查找店铺并验证所有权
        $store = UserAccountModel::find($storeId);
        
        if (!$store) {
            throw new MyException('店铺不存在');
        }

        if (!$store->belongsToUser($userId)) {
            throw new MyException('无权限查看此店铺');
        }

        return $this->formatStoreDetailData($store);
    }

    /**
     * 验证店铺数据
     */
    private function validateStoreData(array $data, bool $isCreate = false): void
    {
        $rules = [];
        
        if ($isCreate) {
            $rules = [
                'account_type' => ['required', 'integer', Rule::in([1, 2])],
                'account_name' => 'required|string|max:100',
            ];
        } else {
            $rules = [
                'id' => 'required|integer',
                'account_type' => ['sometimes', 'integer', Rule::in([1, 2])],
                'account_name' => 'sometimes|string|max:100',
            ];
        }

        // 通用验证规则
        $commonRules = [
            'account_logo' => 'nullable|string|max:255',
            'account_cookie' => 'nullable|string',
            'brand' => 'nullable|string|max:50',
            'price_rate' => 'nullable|numeric|min:0|max:999.99',
            'price_add' => 'nullable|numeric|min:0|max:999999.99',
            'price_subtract' => 'nullable|numeric|min:0|max:999999.99',
            'app_key' => 'required|string|max:255',
            'app_secret' => 'required|string|max:255',
            'cookie_status' => 'sometimes|boolean',
            'shipment_template' => 'nullable|string|max:255',
            'integrator_name' => 'nullable|string|max:255',
            'quantity' => 'nullable|integer',
            'vat_rate' => 'nullable|integer',
            'preparing_day' => 'nullable|integer',
        ];

        $rules = array_merge($rules, $commonRules);

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new MyException($validator->errors()->first());
        }
    }

    /**
     * 格式化店铺数据（列表用）
     */
    private function formatStoreData($store): array
    {
        return [
            'id' => $store->id,
            'account_type' => $store->account_type,
            'account_type_name' => $store->getAccountTypeName(),
            'account_name' => $store->account_name,
            'brand' => $store->brand,
            'price_rate' => $store->price_rate,
            'price_add' => $store->price_add,
            'price_subtract' => $store->price_subtract,
            'app_key' => $store->app_key,
            'app_secret' => $store->app_secret,
            'status' => $store->status,
            'created_at' => $store->created_at,
            'updated_at' => $store->updated_at,
            'shipment_template' => $store->shipment_template,
            'integrator_name' => $store->integrator_name,
            'quantity' => $store->quantity,
            'vat_rate' => $store->vat_rate,
            'preparing_day' => $store->preparing_day,
        ];
    }

    /**
     * 格式化店铺详情数据
     */
    private function formatStoreDetailData($store): array
    {
        return [
            'id' => $store->id,
            'user_id' => $store->user_id,
            'account_type' => $store->account_type,
            'account_type_name' => $store->getAccountTypeName(),
            'account_name' => $store->account_name,
            'brand' => $store->brand,
            'price_rate' => $store->price_rate,
            'price_add' => $store->price_add,
            'price_subtract' => $store->price_subtract,
            'app_key' => $store->app_key,
            'app_secret' => $store->app_secret,
            'status' => $store->status,
            'created_at' => $store->created_at,
            'updated_at' => $store->updated_at,
            'shipment_template' => $store->shipment_template,
            'integrator_name' => $store->integrator_name,
            'quantity' => $store->quantity,
            'vat_rate' => $store->vat_rate,
            'preparing_day' => $store->preparing_day,
        ];
    }
} 