<?php
declare(strict_types=1);

namespace App\Service\User;

use App\Service\BaseService;
use App\Exceptions\MyException;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\GoodsInstructionImagesModel;
use App\Models\User\UserGoodsDirectoryModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\File;

class ImageProcessService extends BaseService
{
    // 每次处理的图片数量
    const BATCH_SIZE = 5;
    
    // 图片下载超时时间（秒）
    const DOWNLOAD_TIMEOUT = 30;
    
    // 重试次数
    const RETRY_TIMES = 2;

    /**
     * 处理商品图片本地化
     * 
     * @param int $userId 用户ID
     * @param int $goodsId 商品ID
     * @param int $processStep 处理步骤
     * @return array
     */
    public function processGoodsImages(int $userId, int $goodsId, int $processStep): array
    {
        // 获取商品信息
        $goods = GoodsModel::where('user_id', $userId)
            ->where('id', $goodsId)
            ->where('status', 1)
            ->first();

        if (!$goods) {
            throw new MyException("商品不存在或已删除");
        }

        // 如果已经处理完成，直接返回状态
        if ($goods->img_local_status == 1) {
            return $this->getProcessResult($goods, true);
        }

        DB::beginTransaction();
        try {
            // 获取所有需要处理的图片和视频信息
            $mediaInfo = $this->getMediaInfo($goods);
            
            // 第一次请求时返回统计信息
            if ($processStep == 1) {
                $result = $this->getInitialProcessInfo($mediaInfo, $goods);
                DB::commit();
                return $result;
            }

            // 处理图片和视频
            $processResult = $this->processMediaFiles($goods, $mediaInfo, $processStep);
            
            DB::commit();
            return $processResult;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取商品的所有媒体文件信息
     * 
     * @param GoodsModel $goods
     * @return array
     */
    private function getMediaInfo(GoodsModel $goods): array
    {
        $mediaInfo = [
            'images' => [],
            'videos' => [],
            'total_images' => 0,
            'total_videos' => 0,
            'processed_images' => 0,
            'processed_videos' => 0
        ];

        // 处理商品主图片
        if (!empty($goods->goods_pic)) {
            $goodsPics = json_decode($goods->goods_pic, true);
            if (is_array($goodsPics)) {
                foreach ($goodsPics as $pic) {
                    if (is_string($pic) && !empty($pic)) {
                        $mediaInfo['images'][] = [
                            'url' => $pic,
                            'type' => 'goods_pic',
                            'field' => 'goods_pic'
                        ];
                    }
                }
            }
        }

        // 处理商品视频
        if (!empty($goods->goods_video) && is_string($goods->goods_video)) {
            $mediaInfo['videos'][] = [
                'url' => $goods->goods_video,
                'type' => 'goods_video',
                'field' => 'goods_video'
            ];
        }

        // 处理商品介绍图片
        $instructionImages = GoodsInstructionImagesModel::where('user_goods_id', $goods->id)->first();
        if ($instructionImages && !empty($instructionImages->urls)) {
            $urls = json_decode($instructionImages->urls, true);
            if (is_array($urls)) {
                foreach ($urls as $url) {
                    if (is_string($url) && !empty($url)) {
                        $mediaInfo['images'][] = [
                            'url' => $url,
                            'type' => 'instruction_images',
                            'field' => 'urls',
                            'model_id' => $instructionImages->id
                        ];
                    }
                }
            }
        }

        // 处理SKU图片
        $skus = GoodsSkuModel::where('user_goods_id', $goods->id)->get();
        foreach ($skus as $sku) {
            // 处理缩略图
            if (!empty($sku->thumb_url) && is_string($sku->thumb_url)) {
                $mediaInfo['images'][] = [
                    'url' => $sku->thumb_url,
                    'type' => 'sku_thumb',
                    'field' => 'thumb_url',
                    'sku_id' => $sku->id
                ];
            }

            // 处理SKC图片集
            if (!empty($sku->skc_gallery)) {
                $skcGallery = json_decode($sku->skc_gallery, true);
                if (is_array($skcGallery)) {
                    foreach ($skcGallery as $item) {
                        if (isset($item['url']) && is_string($item['url']) && !empty($item['url'])) {
                            $mediaInfo['images'][] = [
                                'url' => $item['url'],
                                'type' => 'sku_skc_gallery',
                                'field' => 'skc_gallery',
                                'sku_id' => $sku->id,
                                'gallery_item' => $item
                            ];
                        }
                    }
                }
            }
        }

        // 统计数量
        $mediaInfo['total_images'] = count($mediaInfo['images']);
        $mediaInfo['total_videos'] = count($mediaInfo['videos']);

        // 统计已处理数量
        foreach ($mediaInfo['images'] as $image) {
            if (!$this->isRemoteUrl($image['url'])) {
                $mediaInfo['processed_images']++;
            }
        }

        foreach ($mediaInfo['videos'] as $video) {
            if (!$this->isRemoteUrl($video['url'])) {
                $mediaInfo['processed_videos']++;
            }
        }

        return $mediaInfo;
    }

    /**
     * 获取初始处理信息
     * 
     * @param array $mediaInfo
     * @param GoodsModel $goods
     * @return array
     */
    private function getInitialProcessInfo(array $mediaInfo, GoodsModel $goods): array
    {
        $directoryName = '未分类';
        if ($goods->directory_id > 0) {
            $directory = UserGoodsDirectoryModel::find($goods->directory_id);
            if ($directory) {
                $directoryName = $directory->name;
            }
        }

        return [
            'goods_id' => $goods->id,
            'img_local_status' => $goods->img_local_status,
            'total_images' => $mediaInfo['total_images'],
            'processed_images' => $mediaInfo['processed_images'],
            'total_videos' => $mediaInfo['total_videos'],
            'processed_videos' => $mediaInfo['processed_videos'],
            'estimated_steps' => $this->calculateEstimatedSteps($mediaInfo),
            'directory_name' => $directoryName
        ];
    }

    /**
     * 计算预估处理步骤数
     * 
     * @param array $mediaInfo
     * @return int
     */
    private function calculateEstimatedSteps(array $mediaInfo): int
    {
        $remainingImages = $mediaInfo['total_images'] - $mediaInfo['processed_images'];
        $remainingVideos = $mediaInfo['total_videos'] - $mediaInfo['processed_videos'];
        
        $imageSteps = ceil($remainingImages / self::BATCH_SIZE);
        $videoSteps = $remainingVideos > 0 ? 1 : 0;
        
        return (int) max(1, $imageSteps + $videoSteps);
    }

    /**
     * 处理媒体文件
     * 
     * @param GoodsModel $goods
     * @param array $mediaInfo
     * @param int $processStep
     * @return array
     */
    private function processMediaFiles(GoodsModel $goods, array $mediaInfo, int $processStep): array
    {
        $processedCount = 0;
        $allImagesProcessed = true;
        $allVideosProcessed = true;

        // 先处理图片
        $imagesToProcess = [];
        foreach ($mediaInfo['images'] as $image) {
            if ($this->isRemoteUrl($image['url'])) {
                $imagesToProcess[] = $image;
                $allImagesProcessed = false;
                if (count($imagesToProcess) >= self::BATCH_SIZE) {
                    break;
                }
            }
        }

        // 如果还有图片需要处理
        if (!empty($imagesToProcess)) {
            $processedCount = $this->processImages($goods, $imagesToProcess);
        } else {
            // 图片都处理完了，处理视频
            $videosToProcess = [];
            foreach ($mediaInfo['videos'] as $video) {
                if ($this->isRemoteUrl($video['url'])) {
                    $videosToProcess[] = $video;
                    $allVideosProcessed = false;
                    break; // 一次只处理一个视频
                }
            }

            if (!empty($videosToProcess)) {
                $processedCount = $this->processVideos($goods, $videosToProcess);
            }
        }

        // 重新获取媒体信息以更新统计
        $updatedMediaInfo = $this->getMediaInfo($goods);
        
        // 检查是否全部处理完成
        $isCompleted = ($updatedMediaInfo['processed_images'] >= $updatedMediaInfo['total_images']) && 
                      ($updatedMediaInfo['processed_videos'] >= $updatedMediaInfo['total_videos']);

        if ($isCompleted && $goods->img_local_status != 1) {
            $goods->img_local_status = 1;
            $goods->save();
        }

        $directoryName = '未分类';
        if ($goods->directory_id > 0) {
            $directory = UserGoodsDirectoryModel::find($goods->directory_id);
            if ($directory) {
                $directoryName = $directory->name;
            }
        }

        return [
            'goods_id' => $goods->id,
            'img_local_status' => $isCompleted ? 1 : 0,
            'total_images' => $updatedMediaInfo['total_images'],
            'processed_images' => $updatedMediaInfo['processed_images'],
            'total_videos' => $updatedMediaInfo['total_videos'],
            'processed_videos' => $updatedMediaInfo['processed_videos'],
            'current_batch_processed' => $processedCount,
            'is_completed' => $isCompleted,
            'directory_name' => $directoryName
        ];
    }

    /**
     * 处理图片
     * 
     * @param GoodsModel $goods
     * @param array $images
     * @return int
     */
    private function processImages(GoodsModel $goods, array $images): int
    {
        $processedCount = 0;
        $updates = [];

        foreach ($images as $image) {
            try {
                $localPath = $this->downloadImage($goods->user_id, $goods->goods_id, $image);
                if ($localPath) {
                    $updates[] = [
                        'image' => $image,
                        'local_path' => $localPath
                    ];
                    $processedCount++;
                }
            } catch (\Exception $e) {
                // 下载失败的图片标记为需要移除
                $updates[] = [
                    'image' => $image,
                    'local_path' => null // null表示移除该图片
                ];
            }
        }

        // 批量更新数据库
        $this->updateImageUrls($goods, $updates);

        return $processedCount;
    }

    /**
     * 处理视频
     * 
     * @param GoodsModel $goods
     * @param array $videos
     * @return int
     */
    private function processVideos(GoodsModel $goods, array $videos): int
    {
        $processedCount = 0;

        foreach ($videos as $video) {
            try {
                $localPath = $this->downloadVideo($goods->user_id, $goods->goods_id, $video);
                if ($localPath) {
                    // 更新视频URL
                    $goods->goods_video = $localPath;
                    $goods->save();
                    $processedCount++;
                }
            } catch (\Exception $e) {
                // 视频下载失败，清空视频字段
                $goods->goods_video = '';
                $goods->save();
            }
        }

        return $processedCount;
    }

    /**
     * 下载图片
     * 
     * @param int $userId
     * @param int $goodsId
     * @param array $image
     * @return string|null
     */
    private function downloadImage(int $userId, int $goodsId, array $image): ?string
    {
        $url = $image['url'];
        $type = $image['type'];

        // 添加图片处理参数
        $downloadUrl = $url . '?imageView2/2/w/602/q/90/format/webp';
        
        // 解析文件名，保持原始扩展名
        $fileName = $this->extractFileName($url);
        if (!$fileName) {
            return null;
        }

        // 确定存储目录
        $subDir = $this->getImageSubDirectory($type);
        $relativePath = "attachment/{$userId}/{$goodsId}/{$subDir}/{$fileName}";
        $fullPath = public_path($relativePath);

        // 检查文件是否已存在
        if (file_exists($fullPath)) {
            return $relativePath;
        }

        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new \Exception("无法创建目录: {$dir}");
            }
        }

        // 下载文件，使用带处理参数的URL
        $success = false;
        for ($i = 0; $i < self::RETRY_TIMES; $i++) {
            try {
                $response = Http::timeout(self::DOWNLOAD_TIMEOUT)
                    ->withoutVerifying() // 跳过SSL证书验证
                    ->get($downloadUrl);
                if ($response->successful()) {
                    file_put_contents($fullPath, $response->body());
                    $success = true;
                    break;
                }
            } catch (\Exception $e) {
                if ($i == self::RETRY_TIMES - 1) {
                    throw $e;
                }
                sleep(1); // 重试前等待1秒
            }
        }

        return $success ? $relativePath : null;
    }

    /**
     * 下载视频
     * 
     * @param int $userId
     * @param int $goodsId
     * @param array $video
     * @return string|null
     */
    private function downloadVideo(int $userId, int $goodsId, array $video): ?string
    {
        $url = $video['url'];
        
        // 解析文件名
        $fileName = $this->extractFileName($url);
        if (!$fileName) {
            return null;
        }

        // 确定存储目录
        $relativePath = "attachment/{$userId}/{$goodsId}/goods_video/{$fileName}";
        $fullPath = public_path($relativePath);

        // 检查文件是否已存在
        if (file_exists($fullPath)) {
            return $relativePath;
        }

        // 创建目录
        $dir = dirname($fullPath);
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new \Exception("无法创建目录: {$dir}");
            }
        }

        // 下载文件（视频文件较大，增加超时时间）
        $success = false;
        for ($i = 0; $i < self::RETRY_TIMES; $i++) {
            try {
                $response = Http::timeout(120)
                    ->withoutVerifying() // 跳过SSL证书验证
                    ->get($url); // 视频下载超时时间2分钟
                if ($response->successful()) {
                    file_put_contents($fullPath, $response->body());
                    $success = true;
                    break;
                }
            } catch (\Exception $e) {
                if ($i == self::RETRY_TIMES - 1) {
                    throw $e;
                }
                sleep(2); // 重试前等待2秒
            }
        }

        return $success ? $relativePath : null;
    }

    /**
     * 更新图片URL
     * 
     * @param GoodsModel $goods
     * @param array $updates
     */
    private function updateImageUrls(GoodsModel $goods, array $updates): void
    {
        $goodsPicUpdates = [];
        $instructionUpdates = [];
        $skuUpdates = [];

        foreach ($updates as $update) {
            $image = $update['image'];
            $localPath = $update['local_path'];

            switch ($image['type']) {
                case 'goods_pic':
                    $goodsPicUpdates[] = [
                        'old_url' => $image['url'],
                        'new_url' => $localPath
                    ];
                    break;

                case 'instruction_images':
                    $instructionUpdates[] = [
                        'model_id' => $image['model_id'],
                        'old_url' => $image['url'],
                        'new_url' => $localPath
                    ];
                    break;

                case 'sku_thumb':
                    $skuUpdates[] = [
                        'sku_id' => $image['sku_id'],
                        'field' => 'thumb_url',
                        'old_url' => $image['url'],
                        'new_url' => $localPath
                    ];
                    break;

                case 'sku_skc_gallery':
                    $skuUpdates[] = [
                        'sku_id' => $image['sku_id'],
                        'field' => 'skc_gallery',
                        'old_url' => $image['url'],
                        'new_url' => $localPath,
                        'gallery_item' => $image['gallery_item']
                    ];
                    break;
            }
        }

        // 更新商品主图
        if (!empty($goodsPicUpdates)) {
            $this->updateGoodsPic($goods, $goodsPicUpdates);
        }

        // 更新介绍图片
        if (!empty($instructionUpdates)) {
            $this->updateInstructionImages($instructionUpdates);
        }

        // 更新SKU图片
        if (!empty($skuUpdates)) {
            $this->updateSkuImages($skuUpdates);
        }
    }

    /**
     * 更新商品主图
     * 
     * @param GoodsModel $goods
     * @param array $updates
     */
    private function updateGoodsPic(GoodsModel $goods, array $updates): void
    {
        $goodsPics = json_decode($goods->goods_pic, true);
        if (!is_array($goodsPics)) {
            return;
        }

        foreach ($updates as $update) {
            $key = array_search($update['old_url'], $goodsPics);
            if ($key !== false) {
                if ($update['new_url'] === null) {
                    // 移除失败的图片
                    unset($goodsPics[$key]);
                } else {
                    // 更新URL
                    $goodsPics[$key] = $update['new_url'];
                }
            }
        }

        $goodsPics = array_values($goodsPics);
        
        // 如果所有图片都失败了，存储空字符串而不是空数组
        if (empty($goodsPics)) {
            $goods->goods_pic = '';
        } else {
            $goods->goods_pic = json_encode($goodsPics, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
        
        $goods->save();
    }

    /**
     * 更新介绍图片
     * 
     * @param array $updates
     */
    private function updateInstructionImages(array $updates): void
    {
        $groupedUpdates = [];
        foreach ($updates as $update) {
            $groupedUpdates[$update['model_id']][] = $update;
        }

        foreach ($groupedUpdates as $modelId => $modelUpdates) {
            $model = GoodsInstructionImagesModel::find($modelId);
            if (!$model) {
                continue;
            }

            $urls = json_decode($model->urls, true);
            if (!is_array($urls)) {
                continue;
            }

            foreach ($modelUpdates as $update) {
                $key = array_search($update['old_url'], $urls);
                if ($key !== false) {
                    if ($update['new_url'] === null) {
                        // 移除失败的图片
                        unset($urls[$key]);
                    } else {
                        // 更新URL
                        $urls[$key] = $update['new_url'];
                    }
                }
            }

            $urls = array_values($urls);
            
            // 如果所有图片都失败了，存储空字符串而不是空数组
            if (empty($urls)) {
                $model->urls = '';
            } else {
                $model->urls = json_encode($urls, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }
            
            $model->save();
        }
    }

    /**
     * 更新SKU图片
     * 
     * @param array $updates
     */
    private function updateSkuImages(array $updates): void
    {
        $groupedUpdates = [];
        foreach ($updates as $update) {
            $groupedUpdates[$update['sku_id']][] = $update;
        }

        foreach ($groupedUpdates as $skuId => $skuUpdates) {
            $sku = GoodsSkuModel::find($skuId);
            if (!$sku) {
                continue;
            }

            foreach ($skuUpdates as $update) {
                if ($update['field'] == 'thumb_url') {
                    if ($update['new_url'] === null) {
                        $sku->thumb_url = '';
                    } else {
                        $sku->thumb_url = $update['new_url'];
                    }
                } elseif ($update['field'] == 'skc_gallery') {
                    $skcGallery = json_decode($sku->skc_gallery, true);
                    if (is_array($skcGallery)) {
                        foreach ($skcGallery as &$item) {
                            if (isset($item['url']) && $item['url'] == $update['old_url']) {
                                if ($update['new_url'] === null) {
                                    // 移除失败的图片项
                                    $item = null;
                                } else {
                                    $item['url'] = $update['new_url'];
                                }
                                break;
                            }
                        }
                        // 移除null项
                        $skcGallery = array_filter($skcGallery);
                        $skcGallery = array_values($skcGallery);
                        
                        // 如果所有图片都失败了，存储空字符串而不是空数组
                        if (empty($skcGallery)) {
                            $sku->skc_gallery = '';
                        } else {
                            $sku->skc_gallery = json_encode($skcGallery, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                        }
                    }
                }
            }

            $sku->save();
        }
    }

    /**
     * 判断是否为远程URL
     * 
     * @param string $url
     * @return bool
     */
    private function isRemoteUrl(string $url): bool
    {
        return strpos($url, 'https://') === 0 || strpos($url, 'http://') === 0;
    }

    /**
     * 提取文件名
     * 
     * @param string $url
     * @return string|null
     */
    private function extractFileName(string $url): ?string
    {
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['path'])) {
            return null;
        }

        $pathInfo = pathinfo($parsedUrl['path']);
        if (!isset($pathInfo['filename'])) {
            return null;
        }

        $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
        return $pathInfo['filename'] . $extension;
    }

    /**
     * 获取图片子目录
     * 
     * @param string $type
     * @return string
     */
    private function getImageSubDirectory(string $type): string
    {
        switch ($type) {
            case 'goods_pic':
                return 'goods_pic';
            case 'instruction_images':
                return 'goods_instruction_images';
            case 'sku_thumb':
            case 'sku_skc_gallery':
                return 'sku';
            default:
                return 'other';
        }
    }

    /**
     * 获取处理结果
     * 
     * @param GoodsModel $goods
     * @param bool $isCompleted
     * @return array
     */
    private function getProcessResult(GoodsModel $goods, bool $isCompleted = false): array
    {
        $mediaInfo = $this->getMediaInfo($goods);
        
        $directoryName = '未分类';
        if ($goods->directory_id > 0) {
            $directory = UserGoodsDirectoryModel::find($goods->directory_id);
            if ($directory) {
                $directoryName = $directory->name;
            }
        }
        
        return [
            'goods_id' => $goods->id,
            'img_local_status' => $goods->img_local_status,
            'total_images' => $mediaInfo['total_images'],
            'processed_images' => $mediaInfo['processed_images'],
            'total_videos' => $mediaInfo['total_videos'],
            'processed_videos' => $mediaInfo['processed_videos'],
            'is_completed' => $isCompleted,
            'directory_name' => $directoryName
        ];
    }
} 