<?php

namespace App\Http\Controllers\Api\V1\Wap\System;

use App\Http\Controllers\Api\Controller;
use App\Service\System\ConfigService;
use App\Service\User\UserService;
use JetBrains\PhpStorm\ArrayShape;

class ConfigController extends Controller
{
    protected ConfigService $configService;
    protected UserService $userService;

    public function __construct(ConfigService $configService,UserService $userService)
    {
        $this->configService = $configService;
        $this->userService = $userService;
        parent::__construct();
    }

    public function adImgUser()
    {
        $config = $this->configService->config();//所有配置
        $adImgUser = [];
        if(isset($this->request->user)){
            $user_id  = ($this->request->user)['id'];
            $adImgUser = $this->userService->adImg($user_id);//用户广告图
        }
        return [
            'adimg' => ($config['index_adimg'] ?? '') ? uploadFilePath($config['index_adimg']) : '',
            'adurl' => $config['index_adurl'] ?? '',
            'adimg_agent' => $adImgUser['adimg'] ?? '',
            'adurl_agent' => $adImgUser['adurl'] ?? '',
            'app_down' => config('jk.app_down_url')
        ];
    }

}
