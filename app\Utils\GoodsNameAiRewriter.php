<?php

declare(strict_types=1);

namespace App\Utils;

use App\Models\User\GoodsModel;
use App\Models\User\UserAccountModel;
use App\Models\User\UserTaskDetailGoodsNameModel;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GoodsNameAiRewriter
{
    private GoodsModel $goodsModel;
    private UserAccountModel $userAccountModel;
    private UserTaskDetailGoodsNameModel $userTaskDetailGoodsNameModel;

    public function __construct(
        GoodsModel $goodsModel,
        UserAccountModel $userAccountModel,
        UserTaskDetailGoodsNameModel $userTaskDetailGoodsNameModel
    ) {
        $this->goodsModel = $goodsModel;
        $this->userAccountModel = $userAccountModel;
        $this->userTaskDetailGoodsNameModel = $userTaskDetailGoodsNameModel;
    }

    /**
     * 通过 AI 增强和品牌整合获取重写后的商品名称
     *
     * @param object $taskDetail 任务详情对象，包含 task_detail_id, user_account_id, user_goods_id, spec_values
     * @return string 带品牌前缀的重写后的商品名称，或回退到原始名称
     */
    public function getRewrittenGoodsName($taskDetail): string
    {
        try {
            $existingRewrite = $this->userTaskDetailGoodsNameModel
                ->where('task_detail_id', $taskDetail->id ?? $taskDetail->task_detail_id)
                ->first();

            if ($existingRewrite && !empty($existingRewrite->goods_name)) {
                return $existingRewrite->goods_name;
            }
            return $this->performAiRewriting($taskDetail);

        } catch (\Exception $e) {
            return $this->getOriginalGoodsName($taskDetail);
        }
    }

    /**
     * 执行 AI 重写流程
     *
     * @param object $taskDetail
     * @return string
     */
    private function performAiRewriting($taskDetail): string
    {
        // 获取原始商品名称
        $originalGoodsName = $this->getOriginalGoodsName($taskDetail);
        
        if (empty($originalGoodsName)) {
            Log::warning('Original goods name is empty', [
                'user_goods_id' => $taskDetail->user_goods_id ?? null
            ]);
            return '';
        }
        // 提取 SKU 值
        $skuValues = $this->extractSkuValues($taskDetail->spec_values ?? '');

        // 构建土耳其语提示
        $prompt = $this->constructTurkishPrompt($skuValues, $originalGoodsName);

        // 调用 DeepSeek API
        $rewrittenTitle = $this->callDeepSeekApi($prompt);

        if (empty($rewrittenTitle)) {
            Log::warning('AI rewriting failed, using original name', [
                'task_detail_id' => $taskDetail->id ?? $taskDetail->task_detail_id ?? null,
                'original_name' => $originalGoodsName
            ]);
            return $originalGoodsName;
        }

        // 获取品牌并前置（如果存在）
        $brand = $this->getBrandByUserAccountId($taskDetail->user_account_id);
        $rewrittenTitle = $this->prependBrand($rewrittenTitle, $brand);

        // 将结果存入缓存
        $this->storeRewrittenName($taskDetail, $rewrittenTitle);

        return $rewrittenTitle;
    }

    /**
     * 从 GoodsModel 获取原始商品名称
     *
     * @param object $taskDetail
     * @return string
     */
    private function getOriginalGoodsName($taskDetail): string
    {
        if (empty($taskDetail->user_goods_id)) {
            return '';
        }

        $goods = $this->goodsModel->find($taskDetail->user_goods_id);
        
        return $goods->goods_name ?? '';
    }

    /**
     * 从 spec_values 中提取 SKU 值
     *
     * @param string $specValues
     * @return string
     */
    private function extractSkuValues(string $specValues): string
    {
        if (empty($specValues)) {
            return '';
        }

        try {
            // 首先尝试解码为 JSON
            $decoded = json_decode($specValues, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                // 从解码后的数组中提取值
                $values = [];
                foreach ($decoded as $key => $value) {
                    if (is_array($value)) {
                        $values = array_merge($values, array_values($value));
                    } else {
                        $values[] = $value;
                    }
                }
                return implode(', ', array_filter($values));
            }
            
            // 如果不是 JSON，则按原样返回
            return $specValues;
            
        } catch (\Exception $e) {
            Log::warning('Error extracting SKU values', [
                'spec_values' => $specValues,
                'error' => $e->getMessage()
            ]);
            return $specValues;
        }
    }

    /**
     * 构建用于 AI 重写的土耳其语提示
     *
     * @param string $skuValues
     * @param string $originalGoodsName
     * @return string
     */
    private function constructTurkishPrompt(string $skuValues, string $originalGoodsName): string
    {
        if(!empty($skuValues)){
            $skuValues = "土耳其商品的sku是{$skuValues},";
            $include_sku_values = "标题要包含sku值,";
        }else{
            $skuValues = "";
            $include_sku_values = "";
        }
        
        return "{$skuValues}土耳其语的商品标题是{$originalGoodsName} 重写标题,{$include_sku_values}总长度包含空格不能超过85个字符,标题中不能包含符号|,标题要包含商品的主要信息.直接给出改写后的土耳其语言的标题即可,不需要任何额外的其它信息。";
    }

    /**
     * 调用 DeepSeek API 进行 AI 重写
     *
     * @param string $prompt
     * @return string
     */
    private function callDeepSeekApi(string $prompt): string
    {
        try {
            $apiKey = config('services.deepseek.api_key');
            $apiUrl = config('services.deepseek.api_url', 'https://api.deepseek.com');

            if (empty($apiKey)) {
                Log::error('DeepSeek API key not configured');
                return '';
            }

            $response = Http::timeout(30)
                ->withoutVerifying() // 跳过SSL证书验证
                ->retry(3, 1000)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json',
                ])
                ->post($apiUrl . '/chat/completions', [
                    'model' => 'deepseek-chat',
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'max_tokens' => 150,
                    'temperature' => 0.7
                ]);

            if (!$response->successful()) {
                Log::error('DeepSeek API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return '';
            }

            $data = $response->json();
            
            if (!isset($data['choices'][0]['message']['content'])) {
                Log::error('Invalid DeepSeek API response format', [
                    'response' => $data
                ]);
                return '';
            }

            $rewrittenTitle = trim($data['choices'][0]['message']['content']);
            
            // 验证重写后的标题
            if (strlen($rewrittenTitle) > 85) {
                Log::warning('AI rewritten title exceeds 85 characters, truncating', [
                    'original_length' => strlen($rewrittenTitle),
                    'title' => $rewrittenTitle
                ]);
                $rewrittenTitle = substr($rewrittenTitle, 0, 85);
            }

            // 根据要求移除管道符号
            $rewrittenTitle = str_replace('|', '', $rewrittenTitle);

            return $rewrittenTitle;

        } catch (RequestException $e) {
            Log::error('DeepSeek API request exception', [
                'error' => $e->getMessage(),
                'prompt' => $prompt
            ]);
            return '';
        } catch (\Exception $e) {
            Log::error('Unexpected error in DeepSeek API call', [
                'error' => $e->getMessage(),
                'prompt' => $prompt
            ]);
            return '';
        }
    }

    /**
     * 将重写后的名称存入缓存
     *
     * @param object $taskDetail
     * @param string $rewrittenTitle
     * @return void
     */
    private function storeRewrittenName($taskDetail, string $rewrittenTitle): void
    {
        try {
            $this->userTaskDetailGoodsNameModel->updateOrCreate(
                [
                    'task_detail_id' => $taskDetail->id ?? $taskDetail->task_detail_id,
                ],
                [
                    'user_account_id' => $taskDetail->user_account_id ?? 0,
                    'user_goods_id' => $taskDetail->user_goods_id ?? 0,
                    'user_goods_sku_id' => $taskDetail->user_goods_sku_id ?? 0,
                    'goods_id' => $taskDetail->goods_id ?? 0,
                    'goods_name' => $rewrittenTitle,
                ]
            );

        } catch (\Exception $e) {
            Log::error('Error storing rewritten goods name', [
                'task_detail_id' => $taskDetail->id ?? $taskDetail->task_detail_id ?? null,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 根据用户账户 ID 获取品牌
     *
     * @param int|null $userAccountId
     * @return string
     */
    private function getBrandByUserAccountId(?int $userAccountId): string
    {
        if (empty($userAccountId)) {
            return '';
        }

        try {
            $userAccount = $this->userAccountModel->find($userAccountId);
            return $userAccount->brand ?? '';
        } catch (\Exception $e) {
            Log::warning('Error retrieving brand', [
                'user_account_id' => $userAccountId,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * 如果品牌存在，则将其前置到商品名称
     *
     * @param string $goodsName
     * @param string $brand
     * @return string
     */
    private function prependBrand(string $goodsName, string $brand): string
    {
        if (empty($brand) || empty($goodsName)) {
            return $goodsName;
        }

        // 检查品牌是否已在开头
        if (stripos($goodsName, $brand) === 0) {
            return $goodsName;
        }

        return $brand . ' ' . $goodsName;
    }
}