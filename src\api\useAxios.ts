import protobufRequest from '@/api/req_protobuf';

/* var api = {};
//@ts-ignore
import admin from './files/admin'
Object.assign(api, {
    ...admin
}) */
import config from "@/config/index";
import admin from './files/admin';
const api = admin;
export function axios(request: {
  funName: any;
  pramas: any;
  headers: any;
  method: any;
  url: any;
  responseType?: any;
  requestType?: any;
  auth?: boolean;
  encrypto?: boolean;
  timeout?: number;
}, sender: any, callback = () => { }) {

  let { funName, pramas, headers, method, url, responseType, requestType, auth, encrypto, timeout } = request;


  if (requestType === 'arraybuffer') {
    handleSpecialRequest(url, headers, pramas, callback);
  } else {
    // api[funName]会通过代理自动创建方法
    const config = {
      method,
      url,
      ...(responseType && { responseType }),
      ...(requestType && { requestType }),
      ...(auth !== undefined && { auth }),
      ...(encrypto !== undefined && { encrypto }),
      ...(timeout !== undefined && { timeout })
    };

    api[funName](pramas, (...response) => callback(response), headers, config);
  }
}

async function handleSpecialRequest(url: string, headers: any, pramas: string, callback: (response: any) => void) {
  try {
      const response = await protobufRequest(url, headers, pramas);

    if (response.error_desc && response.error_desc.toLowerCase() === 'ok') {
      callback(response);
    } else {
      console.error(`请求失败,返回值: ${response}`);
      callback(null);
    }
  } catch (error) {
    console.error('请求出错:', error);
    callback(null);
  }
}
export const serve = api
