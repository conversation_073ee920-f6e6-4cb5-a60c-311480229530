/**
 * 请求中间件
 * 用于处理请求的加密和认证
 */

import { encrypt } from './adSecurity';
import config from '@/config';
import { shouldEncryptRequest } from './index';

/**
 * 处理请求参数
 * @param params 请求参数
 * @param options 选项 {auth: boolean, encrypto: boolean}
 * @returns 处理后的参数
 */
export const processRequestParams = (
  params: Record<string, any>,
  options: { auth?: boolean; encrypto?: boolean; url?: string } = {}
): Record<string, any> => {

  console.log("-----------处理请求参数-------------");
  console.log(params);
  // 判断参数是否为空或空对象
  if(!params || (typeof params === 'object' && Object.keys(params).length === 0)) return params;

  let processedParams = { ...params };

  // 判断是否需要加密
  // 1. 如果明确指定了encrypto参数，则使用指定的值
  // 2. 如果未指定encrypto参数但提供了url，则使用shouldEncryptRequest函数判断
  // 3. 如果既未指定encrypto参数也未提供url，则默认不加密
  console.log("-----------处理请求参数 options -------------");
  console.log(options);
  console.log("-----------处理请求参数 options.encrypto -------------");
  console.log(options.encrypto);
  const shouldEncrypt = options.encrypto !== undefined
    ? options.encrypto
    : (options.url ? shouldEncryptRequest(options.url) : false);
  // 如果需要加密
  if (shouldEncrypt) {
    try {
      console.log("-----------加密请求参数-------------");
      console.log(processedParams);
      const encryptionResult = encrypt(processedParams, config.ay);
      console.log("-----------加密结果-------------");
      console.log(encryptionResult);
      // 检查加密是否成功
      if ('encrypted_data' in encryptionResult) {
        // 返回加密后的数据
        return {
          encrypted_data: encryptionResult.encrypted_data,
          sign: encryptionResult.sign
        };
      } else {
        // 加密失败，记录错误并返回原始数据
        console.error('加密失败，使用原始数据:', encryptionResult.error);
        return processedParams;
      }
    } catch (error) {
      console.error('加密过程中出现异常:', error);
      return processedParams;
    }
  }

  return processedParams;
};

/**
 * 处理请求头
 * @param headers 请求头
 * @param options 选项 {auth: boolean, encrypto: boolean}
 * @returns 处理后的请求头
 */
export const processRequestHeaders = async (
  headers: Record<string, any>,
  options: { auth?: boolean; encrypto?: boolean; url?: string } = {}
): Promise<Record<string, any>> => {
  let processedHeaders = { ...headers };

  // 如果需要认证
  if (options.auth) {
    try {
      // 从chrome.storage.local中获取token
      const result = await new Promise<{token?: string}>((resolve) => {
        chrome.storage.local.get(['token'], (storageResult: {token?: string}) => {
          resolve(storageResult);
        });
      });

      if (result.token) {
        processedHeaders['Authorization'] = `Bearer ${result.token}`;
      } else {
        console.warn('未找到认证token');
      }
    } catch (error) {
      console.error('获取认证token失败:', error);
    }
  }

  // 判断是否需要加密
  // 1. 如果明确指定了encrypto参数，则使用指定的值
  // 2. 如果未指定encrypto参数但提供了url，则使用shouldEncryptRequest函数判断
  // 3. 如果既未指定encrypto参数也未提供url，则默认不加密
  const shouldEncrypt = options.encrypto !== undefined
    ? options.encrypto
    : (options.url ? shouldEncryptRequest(options.url) : false);

  // 如果需要加密，添加加密标记头
  if (shouldEncrypt) {
    processedHeaders['X-Encrypted'] = 'true';
  }

  return processedHeaders;
};

/**
 * 增强chrome.runtime.sendMessage请求
 * 处理加密和认证
 * @param request 请求对象
 * @param options 选项 {auth: boolean, encrypto: boolean}
 * @returns 处理后的请求对象
 */
export const enhanceRequest = async (
  request: any,
  options: { auth?: boolean; encrypto?: boolean; url?: string } = {}
): Promise<any> => {
  const enhancedRequest = { ...request };

  // 如果请求中有url但options中没有，则将url添加到options中
  if (enhancedRequest.url && !options.url) {
    options.url = enhancedRequest.url;
  }

  // 判断是否需要加密
  // 1. 如果明确指定了encrypto参数，则使用指定的值
  // 2. 如果未指定encrypto参数但提供了url，则使用shouldEncryptRequest函数判断
  // 3. 如果既未指定encrypto参数也未提供url，则默认不加密
  const shouldEncrypt = options.encrypto !== undefined
    ? options.encrypto
    : (options.url ? shouldEncryptRequest(options.url) : false);

  // 处理请求参数
  if (enhancedRequest.pramas) {
    enhancedRequest.pramas = processRequestParams(enhancedRequest.pramas, {
      ...options,
      encrypto: shouldEncrypt
    });
  }

  // 处理请求头
  if (enhancedRequest.headers) {
    enhancedRequest.headers = await processRequestHeaders(enhancedRequest.headers, {
      ...options,
      encrypto: shouldEncrypt
    });
  }

  return enhancedRequest;
};
