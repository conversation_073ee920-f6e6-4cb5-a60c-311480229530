@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    安装 TSA 代码混淆工具依赖
echo ==========================================
echo.

:: 切换到plugin/gulp目录
cd /d "%~dp0"

echo 📦 正在安装 Gulp 混淆依赖...
echo.

call npm install

if %errorlevel% equ 0 (
    echo.
    echo ✅ 依赖安装完成！
    echo.
    echo 🚀 现在可以使用以下方式运行混淆工具：
    echo.
    echo   方式一：图形界面
    echo     双击运行 obfuscate.bat
    echo.
    echo   方式二：命令行（在项目根目录）
    echo     npm run obfuscate          - 完整混淆
    echo     npm run obfuscate:quick    - 快速混淆（仅JS）
    echo     npm run build:obfuscate    - 构建并混淆
    echo.
    echo   方式三：直接在此目录运行
    echo     npx gulp obfuscate         - 完整混淆
    echo     npx gulp obfuscate-quick   - 快速混淆
    echo     npx gulp restore           - 恢复备份
    echo.
) else (
    echo.
    echo ❌ 依赖安装失败！
    echo 请检查网络连接或尝试使用 cnpm：
    echo   cnpm install
    echo.
)

pause 