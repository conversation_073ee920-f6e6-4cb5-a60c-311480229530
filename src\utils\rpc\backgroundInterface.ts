// Background Script 专用的 RPC 接口
// 避免在 background script 中直接导入包含 DOM API 的模块
// 重构版本：使用静态导入避免Service Worker中的动态导入问题

declare const chrome: any;

// 静态导入页面控制器模块，避免动态导入问题
import { getAllRejectedProductsViaRPC } from './pageController';

// 静态导入DOM操作模块，避免动态导入问题
import * as domOperations from './domOperations';

// 静态导入网络监听模块，避免动态导入问题
import * as networkListener from './networkListener';

/**
 * RPC 操作结果类型
 */
export interface RPCResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * 在 background script 中安全地执行 RPC 操作
 */
export class BackgroundRPCInterface {
  /**
   * 获取所有被拒绝的商品 - 通过页面控制器
   */
  static async getAllRejectedProducts(
    onProgress?: (current: number, total: number) => void
  ): Promise<any[]> {
    try {
      // 检查基础环境
      if (!chrome || !chrome.tabs || !chrome.scripting) {
        throw new Error('Chrome API 环境不完整');
      }

      // 使用静态导入的页面控制器模块
      console.log('Background: 使用静态导入的pageController模块');
      return await getAllRejectedProductsViaRPC(onProgress);
    } catch (error: any) {
      console.error('Background RPC 获取被拒绝商品失败:', error);
      throw new Error(`获取数据失败: ${error.message}`);
    }
  }

  /**
   * 检查是否为 N11 产品列表页面
   */
  static async checkN11ProductListPage(): Promise<boolean> {
    try {
      // 基础环境检查
      if (!chrome || !chrome.tabs || !chrome.scripting) {
        console.warn('Chrome API 环境不完整');
        return false;
      }

      // 获取当前活动标签页
      const tabs = await new Promise<any[]>((resolve, reject) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(result);
          }
        });
      });

      if (!tabs || tabs.length === 0) {
        console.warn('未找到活动标签页');
        return false;
      }

      const tabId = tabs[0].id;
      const url = tabs[0].url;
      
      // 首先通过URL进行基础检查
      if (!url || !url.includes('so.n11.com')) {
        return false;
      }

      // 通过脚本检查页面结构
      try {
        const results = await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            try {
              const url = window.location.href;
              const hasCorrectUrl = url.includes('so.n11.com') && 
                                   (url.includes('product') || url.includes('urun'));
              
              const hasProductList = document.querySelector('.product-list') ||
                                    document.querySelector('[class*="product"]') ||
                                    document.querySelector('.tabManager');
              
              return hasCorrectUrl && !!hasProductList;
            } catch (error) {
              return false;
            }
          }
        });
        
        return !!(results && results[0] && results[0].result);
      } catch (scriptError) {
        console.warn('执行页面检查脚本失败:', scriptError);
        // 如果脚本执行失败，回退到URL检查
        return url.includes('so.n11.com') && (url.includes('product') || url.includes('urun'));
      }
    } catch (error) {
      console.error('Background RPC 检查页面失败:', error);
      return false;
    }
  }

  /**
   * 获取 RPC 状态信息
   */
  static async getRPCStatus(): Promise<any> {
    try {
      const status = {
        supported: false,
        chromeAPIs: {
          scripting: !!(chrome && chrome.scripting && chrome.scripting.executeScript),
          debugger: !!(chrome && chrome.debugger && chrome.debugger.attach),
          tabs: !!(chrome && chrome.tabs && chrome.tabs.query)
        },
        currentPage: {
          isN11: false,
          url: undefined as string | undefined,
          tabId: undefined as number | undefined
        },
        permissions: {
          debugger: false,
          scripting: false,
          activeTab: false
        }
      };

      // 检查权限
      if (chrome && chrome.permissions) {
        try {
          const permissions = await new Promise<any>((resolve, reject) => {
            chrome.permissions.getAll((result: any) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(result);
              }
            });
          });

          status.permissions.debugger = permissions.permissions?.includes('debugger') || false;
          status.permissions.scripting = permissions.permissions?.includes('scripting') || false;
          status.permissions.activeTab = permissions.permissions?.includes('activeTab') || false;
        } catch (permError) {
          console.warn('检查权限失败:', permError);
        }
      }

      // 检查当前页面
      if (chrome && chrome.tabs) {
        try {
          const tabs = await new Promise<any[]>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(result);
              }
            });
          });

          if (tabs && tabs.length > 0) {
            const activeTab = tabs[0];
            status.currentPage.url = activeTab.url;
            status.currentPage.tabId = activeTab.id;
            
            if (activeTab.url) {
              status.currentPage.isN11 = activeTab.url.includes('so.n11.com') && 
                                       (activeTab.url.includes('product') || activeTab.url.includes('urun'));
            }
          }
        } catch (tabError) {
          console.warn('获取当前页面信息失败:', tabError);
        }
      }

      // 综合判断是否支持
      const apiSupported = status.chromeAPIs.scripting && 
                          status.chromeAPIs.debugger && 
                          status.chromeAPIs.tabs;
      
      const permissionsGranted = status.permissions.debugger &&
                                status.permissions.scripting;
      
      status.supported = apiSupported && permissionsGranted;

      return status;
    } catch (error: any) {
      console.error('Background RPC 获取状态失败:', error);
      return {
        supported: false,
        message: '状态检查失败',
        error: error.message
      };
    }
  }

  /**
   * 执行DOM操作
   */
  static async executeDomOperation(operation: string, tabId?: number, params?: any): Promise<RPCResult> {
    try {
      // 使用静态导入的DOM操作模块
      console.log('Background: 使用静态导入的domOperations模块');

      // 获取目标标签页ID
      let targetTabId = tabId;
      if (!targetTabId) {
        const tabs = await new Promise<any[]>((resolve, reject) => {
          chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(result);
            }
          });
        });

        if (!tabs || tabs.length === 0) {
          return { success: false, message: '未找到活动标签页' };
        }

        targetTabId = tabs[0].id;
      }

      // 执行对应的DOM操作
      let result: any;
      switch (operation) {
        case 'setPageSize100':
          result = await domOperations.setPageSize100(targetTabId);
          break;
        case 'clickNextPage':
          result = await domOperations.clickNextPage(targetTabId);
          break;
        case 'getPageStatus':
          result = await domOperations.getPageStatus(targetTabId);
          break;
        case 'isN11ProductListPage':
          result = await domOperations.isN11ProductListPage(targetTabId);
          break;
        case 'waitForElement':
          result = await domOperations.waitForElement(targetTabId, params);
          break;
        case 'scrollToPosition':
          result = await domOperations.scrollToPosition(targetTabId, params);
          break;
        default:
          result = { success: false, message: `不支持的DOM操作: ${operation}` };
      }
      
      return result;
    } catch (error: any) {
      console.error('Background RPC DOM操作失败:', error);
      return { success: false, message: `DOM操作失败: ${error.message}` };
    }
  }

  /**
   * 管理网络监听器
   */
  static async manageNetworkListener(action: string, tabId?: number, config?: any): Promise<RPCResult> {
    try {
      // 使用静态导入的网络监听模块
      console.log('Background: 使用静态导入的networkListener模块');

      // 获取目标标签页ID
      let targetTabId = tabId;
      if (!targetTabId) {
        const tabs = await new Promise<any[]>((resolve, reject) => {
          chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(result);
            }
          });
        });

        if (!tabs || tabs.length === 0) {
          return { success: false, message: '未找到活动标签页' };
        }

        targetTabId = tabs[0].id;
      }

      // 执行对应的网络监听操作
      let result: any;
      switch (action) {
        case 'create':
          const listener = networkListener.createNetworkListener({ tabId: targetTabId, ...config });
          result = await listener.start();
          break;
        default:
          result = { success: false, message: `不支持的网络监听操作: ${action}` };
      }
      
      return result;
    } catch (error: any) {
      console.error('Background RPC 网络监听失败:', error);
      return { success: false, message: `网络监听失败: ${error.message}` };
    }
  }
}

export default BackgroundRPCInterface; 