// API配置文件
export const API_URLS = {
  // 用户相关API
  USER_INFO: 'apiUserInfoUrl',
  TASK_ADD: 'apiTaskAddUrl',
  TASK_LIST: 'apiTaskListUrl',
  TASK_START: 'apiTaskStartUrl',
  TASK_UPDATE: 'apiTaskUpdateUrl',
  TASK_DETAIL: 'apiTaskDetailUrl',
  TASK_DETAIL_LIST: 'apiTaskDetailListUrl',
  TASK_QUERY_RESULTS: 'apiTaskQueryResultsUrl',
  TASK_PENDING_QUERY: 'apiTaskPendingQueryUrl',
  TASK_BATCH_UPDATE: 'apiTaskBatchUpdateUrl',
  TASK_SAVE_UPLOAD_PARAMS: 'apiTaskSaveUploadParamsUrl',
  TASK_RETRY_UPLOAD_PARAMS: 'apiTaskRetryUploadParamsUrl',
  TASK_BATCH_RETRY_UPLOAD: 'apiTaskBatchRetryUploadUrl',
  
  // 分类相关API
  CATEGORY_N11_LIST: 'apiCatN11ListUrl',
  CATEGORY_LINK: 'apiCategoryLinkUrl',
  CATEGORY_RELATION_LIST: 'apiCatRelationListUrl',
  
  // 商品相关API
  GOODS_STATISTICS: 'apiGoodsStatisticsUrl',
  
  // 其他平台API（预留）
  // CATEGORY_AMAZON_LIST: 'apiCatAmazonListUrl',
  // CATEGORY_EBAY_LIST: 'apiCatEbayListUrl',
}

// 平台配置
export const PLATFORMS = {
  N11: {
    id: 1,
    name: 'N11',
    code: 'n11',
    apiUrl: API_URLS.CATEGORY_N11_LIST
  }
  // 可以在这里添加更多平台
}

// 关联类型
export const LINK_TYPES = {
  SYSTEM: 1,
  USER: 2
} as const

export type LinkType = typeof LINK_TYPES[keyof typeof LINK_TYPES]

/**
 * API配置工具
 * 统一管理API地址获取逻辑
 */

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
export const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      console.log('获取API地址响应:', response)
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};