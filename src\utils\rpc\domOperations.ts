// DOM操作工具文件 - 基于Manifest V3的chrome.scripting API
declare const chrome: any;

/**
 * DOM操作结果类型
 */
export interface DomOperationResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * 页面状态信息
 */
export interface PageStatus {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

/**
 * 等待元素出现的配置
 */
export interface WaitElementConfig {
  selector: string;
  timeout?: number;
  checkInterval?: number;
}

/**
 * 在指定标签页中执行脚本
 */
async function executeScriptInTab(tabId: number, func: Function, args: any[] = []): Promise<any> {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: func,
      args: args
    });
    
    if (results && results[0] && results[0].result) {
      return results[0].result;
    }
    
    return null;
  } catch (error) {
    console.error('执行脚本失败:', error);
    throw error;
  }
}

/**
 * 切换每页显示数量为100条
 */
export async function setPageSize100(tabId: number): Promise<DomOperationResult> {
  const scriptFunction = () => {
    return new Promise((resolve) => {
      try {
        console.log('开始设置页面大小为100条');
        
        // 先尝试查找已展开的下拉菜单
        const dropdownContent = document.querySelector('.dropdown-content .simpleSelect-content') ||
                               document.querySelector('.simpleSelect-content') ||
                               document.querySelector('.dropdown-content');
        
        if (dropdownContent) {
          console.log('下拉菜单已展开，直接查找100选项');
          findAndClick100Option(dropdownContent, resolve);
          return;
        }

        console.log('下拉菜单未展开，尝试点击下拉按钮');
        // 查找选择器容器
        const selectContainer = document.querySelector('.simpleSelect') || 
                              document.querySelector('[class*="select"]') ||
                              document.querySelector('.per-page-wrapper');
        
        if (!selectContainer) {
          resolve({ success: false, message: '未找到页面大小选择器容器' });
          return;
        }

        // 查找下拉按钮
        const dropdownButton = selectContainer.querySelector('button') ||
                              selectContainer.querySelector('.dropdown-toggle') ||
                              selectContainer.querySelector('[role="button"]') ||
                              selectContainer.querySelector('.simpleSelect-btn');
        
        if (!dropdownButton) {
          resolve({ success: false, message: '未找到下拉按钮' });
          return;
        }

        console.log('点击下拉按钮');
        (dropdownButton as HTMLElement).click();
        
        // 等待下拉菜单出现
        setTimeout(() => {
          const newDropdownContent = document.querySelector('.dropdown-content .simpleSelect-content') ||
                                   document.querySelector('.simpleSelect-content') ||
                                   document.querySelector('.dropdown-content');
          
          if (!newDropdownContent) {
            resolve({ success: false, message: '点击后仍未找到下拉内容容器' });
            return;
          }
          
          console.log('下拉菜单已展开，查找100选项');
          findAndClick100Option(newDropdownContent, resolve);
        }, 500);

        // 查找并点击100选项的函数
        function findAndClick100Option(container: Element, resolveCallback: Function) {
          const optionButtons = container.querySelectorAll('li button[type="button"]') ||
                               container.querySelectorAll('button');
          
          console.log('找到选项按钮数量:', optionButtons.length);
          
          let option100Button: Element | null = null;
          
          for (const button of Array.from(optionButtons)) {
            const text = button.textContent?.trim();
            console.log('检查按钮文本:', text);
            if (text === '100 Ürün' || text === '100' || text === '100条' || 
                text === '100 items' || (text && text.includes('100'))) {
              option100Button = button;
              console.log('找到100选项按钮:', button);
              break;
            }
          }
          
          if (option100Button) {
            console.log('点击100选项按钮');
            (option100Button as HTMLElement).click();
            
            setTimeout(() => {
              resolveCallback({ 
                success: true, 
                message: '成功设置每页显示100条' 
              });
            }, 1000);
          } else {
            resolveCallback({ 
              success: false, 
              message: '未找到100选项按钮' 
            });
          }
        }
        
      } catch (error: any) {
        console.error('设置页面大小失败:', error);
        resolve({ 
          success: false, 
          message: `设置页面大小失败: ${error.message}` 
        });
      }
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行脚本失败: ${error.message}` };
  }
}

/**
 * 点击下一页按钮
 */
export async function clickNextPage(tabId: number): Promise<DomOperationResult> {
  const scriptFunction = () => {
    return new Promise((resolve) => {
      try {
        // 查找分页容器
        const pagination = document.querySelector('.pagination') ||
                          document.querySelector('[class*="pagination"]') ||
                          document.querySelector('.page-nav');

        if (!pagination) {
          resolve({ success: false, message: '未找到分页容器' });
          return;
        }

        console.log('找到分页容器:', pagination);

        // 根据用户提供的DOM结构，查找正确的下一页按钮
        // 避免点击最后一页按钮（last-page.ff1a1c1f.svg），要点击下一页按钮（next-page.f7588e0b.svg）
        let nextButton: Element | null = null;

        // 方法1：通过img src属性查找下一页按钮
        const nextPageImg = pagination.querySelector('img[src*="next-page"]');
        if (nextPageImg) {
          nextButton = nextPageImg.closest('button') || nextPageImg.parentElement;
          console.log('通过next-page图标找到下一页按钮:', nextButton);
        }

        // 方法2：如果方法1失败，查找包含">"符号的按钮（但排除">>"符号）
        if (!nextButton) {
          const buttons = pagination.querySelectorAll('button');
          for (const button of Array.from(buttons)) {
            const buttonText = button.textContent?.trim();
            const hasNextIcon = button.querySelector('img[src*="next"]') ||
                               button.querySelector('[class*="next"]');
            const hasLastIcon = button.querySelector('img[src*="last"]') ||
                               button.querySelector('[class*="last"]');

            // 确保是下一页按钮，不是最后一页按钮
            if ((buttonText === '>' || hasNextIcon) && !hasLastIcon) {
              nextButton = button;
              console.log('通过按钮内容找到下一页按钮:', nextButton);
              break;
            }
          }
        }

        // 方法3：备用方法 - 查找倒数第二个按钮（通常最后一个是"最后一页"）
        if (!nextButton) {
          const buttons = pagination.querySelectorAll('button');
          if (buttons.length >= 2) {
            // 检查倒数第二个按钮是否是下一页
            const secondLastButton = buttons[buttons.length - 2];
            const hasLastIcon = secondLastButton.querySelector('img[src*="last"]');
            if (!hasLastIcon) {
              nextButton = secondLastButton;
              console.log('通过位置找到下一页按钮（倒数第二个）:', nextButton);
            }
          }
        }

        if (!nextButton) {
          resolve({ success: false, message: '未找到下一页按钮' });
          return;
        }

        // 检查按钮是否可点击
        const buttonElement = nextButton as HTMLButtonElement;
        if (buttonElement.disabled || nextButton.classList.contains('disabled')) {
          resolve({ success: false, message: '下一页按钮已禁用，可能已到最后一页' });
          return;
        }

        console.log('准备点击下一页按钮:', nextButton);

        // 滚动到按钮可见区域
        nextButton.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 等待滚动完成后点击
        setTimeout(() => {
          (nextButton as HTMLElement).click();
          console.log('已点击下一页按钮');
          resolve({ success: true, message: '成功点击下一页按钮' });
        }, 500);

      } catch (error: any) {
        resolve({ success: false, message: `点击下一页失败: ${error.message}` });
      }
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行脚本失败: ${error.message}` };
  }
}

/**
 * 点击指定页码
 */
export async function clickPageNumber(tabId: number, pageNumber: number): Promise<DomOperationResult> {
  const scriptFunction = (targetPage: number) => {
    return new Promise((resolve) => {
      try {
        const pagination = document.querySelector('.pagination') ||
                          document.querySelector('[class*="pagination"]');
        
        if (!pagination) {
          resolve({ success: false, message: '未找到分页容器' });
          return;
        }

        // 查找目标页码按钮
        const pageButtons = pagination.querySelectorAll('button, a');
        let targetButton: Element | null = null;
        
        for (const button of Array.from(pageButtons)) {
          const text = button.textContent?.trim();
          if (text === targetPage.toString()) {
            targetButton = button;
            break;
          }
        }
        
        if (!targetButton) {
          resolve({ success: false, message: `未找到页码 ${targetPage} 的按钮` });
          return;
        }

        const buttonElement = targetButton as HTMLButtonElement;
        if (buttonElement.disabled || targetButton.classList.contains('disabled') || 
            targetButton.classList.contains('active')) {
          resolve({ success: false, message: `页码 ${targetPage} 按钮不可点击` });
          return;
        }

        targetButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        setTimeout(() => {
          (targetButton as HTMLElement).click();
          resolve({ success: true, message: `成功点击页码 ${targetPage}` });
        }, 500);
        
      } catch (error: any) {
        resolve({ success: false, message: `点击页码失败: ${error.message}` });
      }
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction, [pageNumber]);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行脚本失败: ${error.message}` };
  }
}

/**
 * 获取页面状态信息
 */
export async function getPageStatus(tabId: number): Promise<PageStatus | null> {
  const scriptFunction = () => {
    try {
      // 查找分页信息
      const pagination = document.querySelector('.pagination') ||
                        document.querySelector('[class*="pagination"]');

      if (!pagination) {
        console.log('未找到分页容器');
        return null;
      }

      console.log('找到分页容器:', pagination);

      // 获取当前页码
      const activeButton = pagination.querySelector('.active') ||
                          pagination.querySelector('[aria-current="page"]') ||
                          pagination.querySelector('.current') ||
                          pagination.querySelector('button.active');

      const currentPage = activeButton ? parseInt(activeButton.textContent?.trim() || '1') : 1;
      console.log('当前页码:', currentPage, '激活按钮:', activeButton);

      // 重要：总页数应该从API响应中获取，而不是从DOM中推断
      // 这里先设置一个默认值，实际的总页数会在网络监听器中从API响应获取
      let totalPages = 1;

      // 尝试从DOM获取可见的页码按钮作为参考（但不作为最终值）
      const pageButtons = pagination.querySelectorAll('button, a');
      let maxVisiblePage = 1;

      for (const button of Array.from(pageButtons)) {
        const text = button.textContent?.trim();
        if (text && /^\d+$/.test(text)) {
          const pageNum = parseInt(text);
          if (pageNum > maxVisiblePage) {
            maxVisiblePage = pageNum;
          }
        }
      }

      // 如果当前页大于可见的最大页码，说明还有更多页面
      totalPages = Math.max(maxVisiblePage, currentPage);

      console.log('可见的最大页码:', maxVisiblePage, '推断的总页数:', totalPages);

      // 查找页面大小信息
      const pageSizeElement = document.querySelector('.page-size') ||
                             document.querySelector('[class*="page-size"]') ||
                             document.querySelector('.simpleSelect button');

      const pageSize = pageSizeElement ?
        parseInt(pageSizeElement.textContent?.match(/\d+/)?.[0] || '20') : 20;

      console.log('页面大小:', pageSize, '页面大小元素:', pageSizeElement);

      // 查找总条目数
      const totalElement = document.querySelector('.total-count') ||
                          document.querySelector('[class*="total"]') ||
                          document.querySelector('.result-count');

      const totalItems = totalElement ?
        parseInt(totalElement.textContent?.match(/\d+/)?.[0] || '0') : 0;

      // 检查是否有下一页按钮（更精确的判断）
      let hasNextPage = false;

      // 方法1：通过图标判断
      const nextPageImg = pagination.querySelector('img[src*="next-page"]');
      if (nextPageImg) {
        const nextButton = nextPageImg.closest('button');
        hasNextPage = !!(nextButton && !(nextButton as HTMLButtonElement).disabled);
      }

      // 方法2：通过按钮内容判断（排除最后一页按钮）
      if (!hasNextPage) {
        const buttons = pagination.querySelectorAll('button');
        for (const button of Array.from(buttons)) {
          const hasNextIcon = button.querySelector('img[src*="next"]');
          const hasLastIcon = button.querySelector('img[src*="last"]');

          if (hasNextIcon && !hasLastIcon && !(button as HTMLButtonElement).disabled) {
            hasNextPage = true;
            break;
          }
        }
      }

      // 检查是否有上一页按钮
      const prevPageImg = pagination.querySelector('img[src*="prev"]') ||
                         pagination.querySelector('img[src*="previous"]');
      let hasPrevPage = false;

      if (prevPageImg) {
        const prevButton = prevPageImg.closest('button');
        hasPrevPage = !!(prevButton && !(prevButton as HTMLButtonElement).disabled);
      }

      console.log('分页状态:', { currentPage, totalPages, hasNextPage, hasPrevPage });

      return {
        currentPage,
        totalPages, // 注意：这个值可能不准确，真实值应该从API响应中获取
        pageSize,
        totalItems,
        hasNextPage,
        hasPrevPage
      };

    } catch (error) {
      console.error('获取页面状态失败:', error);
      return null;
    }
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return result;
  } catch (error) {
    console.error('执行获取页面状态脚本失败:', error);
    return null;
  }
}

/**
 * 等待元素出现
 */
export async function waitForElement(tabId: number, config: WaitElementConfig): Promise<DomOperationResult> {
  const { selector, timeout = 10000, checkInterval = 500 } = config;
  
  const scriptFunction = (sel: string, maxTime: number, interval: number) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        const element = document.querySelector(sel);
        
        if (element) {
          resolve({ success: true, message: `元素 ${sel} 已出现` });
          return;
        }
        
        if (Date.now() - startTime > maxTime) {
          resolve({ success: false, message: `等待元素 ${sel} 超时` });
          return;
        }
        
        setTimeout(checkElement, interval);
      };
      
      checkElement();
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction, [selector, timeout, checkInterval]);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行等待元素脚本失败: ${error.message}` };
  }
}

/**
 * 滚动页面到指定位置
 */
export async function scrollToPosition(tabId: number, position: 'top' | 'bottom' | number): Promise<DomOperationResult> {
  const scriptFunction = (pos: 'top' | 'bottom' | number) => {
    try {
      let scrollTop: number;
      
      if (pos === 'top') {
        scrollTop = 0;
      } else if (pos === 'bottom') {
        scrollTop = document.body.scrollHeight;
      } else {
        scrollTop = pos;
      }
      
      window.scrollTo({ top: scrollTop, behavior: 'smooth' });
      
      return { success: true, message: `成功滚动到位置: ${pos}` };
    } catch (error: any) {
      return { success: false, message: `滚动失败: ${error.message}` };
    }
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction, [position]);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行滚动脚本失败: ${error.message}` };
  }
}

/**
 * 检查页面是否为N11产品列表页面
 */
export async function isN11ProductListPage(tabId: number): Promise<boolean> {
  const scriptFunction = () => {
    try {
      const url = window.location.href;
      const hasCorrectUrl = url.includes('so.n11.com') && 
                           (url.includes('product') || url.includes('urun'));
      
      const hasProductList = document.querySelector('.product-list') ||
                            document.querySelector('[class*="product"]') ||
                            document.querySelector('.tabManager');
      
      return hasCorrectUrl && !!hasProductList;
    } catch (error) {
      return false;
    }
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return !!result;
  } catch (error) {
    console.error('检查页面类型失败:', error);
    return false;
  }
} 