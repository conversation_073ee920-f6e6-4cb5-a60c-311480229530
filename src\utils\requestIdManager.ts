class RequestIdManager {
  private static instance: RequestIdManager;
  private usedIds: Set<number> = new Set();
  private readonly MAX_ID = 65535;
  private readonly MIN_ID = 500;

  private constructor() {}

  public static getInstance(): RequestIdManager {
    if (!RequestIdManager.instance) {
      RequestIdManager.instance = new RequestIdManager();
    }
    return RequestIdManager.instance;
  }

  public generateId(): number {
    let id: number;
    do {
      const timestamp = Date.now() % (this.MAX_ID - this.MIN_ID);
      const random = Math.floor(Math.random() * 1000);
      id = this.MIN_ID + (timestamp * 1000 + random) % (this.MAX_ID - this.MIN_ID);
    } while (this.usedIds.has(id));

    this.usedIds.add(id);
    return id;
  }

  public releaseId(id: number): void {
    this.usedIds.delete(id);
  }
}

export const requestIdManager = RequestIdManager.getInstance();
