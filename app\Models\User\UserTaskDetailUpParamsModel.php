<?php

namespace App\Models\User;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTaskDetailUpParamsModel extends BaseModel
{
    protected $table = 'user_task_detail_up_params';

    protected $fillable = [
        'task_detail_id',
        'third_type',
        'params'
    ];

    // 关联任务详情
    public function taskDetail(): BelongsTo
    {
        return $this->belongsTo(UserTaskDetailModel::class, 'task_detail_id', 'id');
    }
} 