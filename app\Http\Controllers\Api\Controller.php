<?php
declare(strict_types=1);
namespace App\Http\Controllers\Api;
use App\Helpers\Traits\ApiResponse;
use App\Http\Controllers\Controller as BaseController;
use Illuminate\Http\Request;

class Controller extends BaseController
{
    use ApiResponse;
    protected Request $request;
    protected int $per_page = 20;
    public function __construct()
    {
        $this->request = request();
        $this->per_page = (int)config('jk.page.admin_per_page');
    }

}
