@echo off
chcp 65001 >nul
title TSA 混淆工具 - 简单测试

echo.
echo ========================================
echo    TSA Chrome扩展 - 简单混淆测试
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 当前目录: %CD%
echo.

echo 📦 检查依赖...
if not exist "node_modules" (
    echo ❌ 依赖未安装，请先运行 install.bat
    pause
    exit /b 1
)

echo ✅ 依赖已安装
echo.

echo 🔍 检查构建目录...
if not exist "..\..\tsa_build" (
    echo ❌ 构建目录不存在: ..\..\tsa_build
    echo 请先运行项目构建命令
    pause
    exit /b 1
)

echo ✅ 构建目录存在
echo.

echo 🚀 开始简单测试...
echo.

npx gulp -f test-simple.js simple-test

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 简单测试完成！
    echo.
    echo 📁 备份位置: ..\..\tsa_build_backup
    echo 📁 混淆文件: ..\..\tsa_build
    echo.
) else (
    echo.
    echo ❌ 测试失败，错误代码: %ERRORLEVEL%
    echo.
)

echo 按任意键退出...
pause >nul 