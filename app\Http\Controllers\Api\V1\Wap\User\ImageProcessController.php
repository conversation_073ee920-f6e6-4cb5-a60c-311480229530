<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\ImageProcessService;
use Illuminate\Http\Request;
use App\Exceptions\MyException;

class ImageProcessController extends Controller
{
    protected $imageProcessService;

    public function __construct()
    {
        $this->imageProcessService = new ImageProcessService();
        parent::__construct();
    }

    /**
     * 处理商品图片本地化
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processGoodsImages(Request $request)
    {
        try {
            $user = request()->attributes->get('user');
            $userId = $user['id'];
            $goodsId = (int)$request->input('goods_id', 0);
            $processStep = (int)$request->input('process_step', 1);

            if ($goodsId <= 0) {
                throw new MyException("商品ID不能为空");
            }

            if ($processStep <= 0) {
                throw new MyException("处理步骤参数错误");
            }

            $result = $this->imageProcessService->processGoodsImages($userId, $goodsId, $processStep);

            return $this->apiSuccess($result);

        } catch (MyException $e) {
            return $this->apiError($e->getMessage());
        } catch (\Exception $e) {
            return $this->apiError('图片处理失败，请重试');
        }
    }
} 