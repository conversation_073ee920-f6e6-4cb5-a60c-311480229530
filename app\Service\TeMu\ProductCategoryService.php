<?php
namespace App\Service\TeMu;

use App\Service\BaseService;
use App\Models\TeMu\ProductCategoryTeMuModel;
use App\Models\TeMu\ProductCategoryWebPageTeMuModel;
use App\Models\User\UserAccountModel;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class ProductCategoryService extends BaseService{

    public function getTemuMallInfo(){
        $user_account_temu = UserAccountModel::query()
                              ->where('user_id',1)
                              ->where('account_type',1)
                              ->where('cookie_status',1)
                              ->orderBy('id')
                              ->first();
        if($user_account_temu){
            return [
                'mallid' => $user_account_temu->account_id,
                'cookie' => $user_account_temu->account_cookie
            ];
        }
        return [
            'mallid' => '',
            'cookie' => ''
        ];
    }

    /**
     * 采集Temu商品分类并保存到数据库
     *
     * @param bool $isConsole 是否是命令行执行
     * @param object|null $command 命令行对象，用于输出进度信息
     * @return array 返回采集结果
     */
    public function collectCategories($isConsole = false, $command = null)
    {
        // 获取Temu商城信息（mallid和cookie）
        $temuInfo = $this->getTemuMallInfo();
        $mallid = $temuInfo['mallid'];
        $cookie = $temuInfo['cookie'];

        // 如果mallid或cookie为空，则不进行采集
        if (empty($mallid) || empty($cookie)) {
            return [
                'status' => 0,
                'message' => '未找到有效的Temu账号信息'
            ];
        }

        try {
            // 命令行模式下输出信息
            if ($isConsole && $command) {
                $command->info('开始获取第一级分类...');
            }

            // 获取第一级分类
            $firstLevelCategories = $this->fetchCategories($mallid, $cookie);

            if (!isset($firstLevelCategories['success']) || $firstLevelCategories['errorCode'] != 1000000) {
                // Cookie可能已过期，更新状态
                $this->updateCookieStatus();

                if ($isConsole && $command) {
                    $command->error('Cookie已过期或API请求失败: ' . ($firstLevelCategories['errorMsg'] ?? '未知错误'));
                }

                return [
                    'status' => 0,
                    'message' => 'Cookie已过期或API请求失败: ' . ($firstLevelCategories['errorMsg'] ?? '未知错误')
                ];
            }

            // 处理第一级分类
            $categoryCount = 0;
            $totalCategories = 0;

            if (isset($firstLevelCategories['result']['categoryNodeVOS']) && is_array($firstLevelCategories['result']['categoryNodeVOS'])) {
                $totalCategories = count($firstLevelCategories['result']['categoryNodeVOS']);

                if ($isConsole && $command) {
                    $command->info("找到 {$totalCategories} 个一级分类，开始处理...");
                    // 创建进度条
                    $bar = $command->getOutput()->createProgressBar($totalCategories);
                    $bar->start();
                }

                foreach ($firstLevelCategories['result']['categoryNodeVOS'] as $index => $category) {
                    // 保存分类到数据库
                    $this->saveCategoryToDatabase($category, 0);
                    $categoryCount++;

                    if ($isConsole && $command) {
                        $command->newLine();
                        $command->info("处理一级分类 [{$index}/{$totalCategories}]: {$category['catName']} (ID: {$category['catId']})");
                    }

                    // 如果不是叶子节点，则递归获取子分类
                    if (!$category['isLeaf']) {
                        if ($isConsole && $command) {
                            $command->info("开始获取 {$category['catName']} 的子分类...");
                        }

                        $childrenCount = $this->processChildCategories(
                            $category['catId'],
                            $mallid,
                            $cookie,
                            $category['catName'],
                            $isConsole,
                            $command
                        );

                        $categoryCount += $childrenCount;

                        if ($isConsole && $command) {
                            $command->info("完成 {$category['catName']} 的子分类采集，共 {$childrenCount} 个子分类");
                        }
                    }

                    if ($isConsole && $command && isset($bar)) {
                        $bar->advance();
                    }
                }

                if ($isConsole && $command && isset($bar)) {
                    $bar->finish();
                    $command->newLine(2);
                }
            }

            return [
                'status' => 1,
                'message' => '分类采集完成',
                'count' => $categoryCount
            ];

        } catch (\Exception $e) {
            Log::error('采集Temu分类失败: ' . $e->getMessage());

            if ($isConsole && $command) {
                $command->error('采集过程中发生错误: ' . $e->getMessage());
            }

            return [
                'status' => 0,
                'message' => '采集过程中发生错误: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 递归处理子分类
     *
     * @param int $parentCatId 父分类ID
     * @param string $mallid 商城ID
     * @param string $cookie Cookie
     * @param string $pathName 分类路径名称
     * @param bool $isConsole 是否是命令行执行
     * @param object|null $command 命令行对象，用于输出进度信息
     * @return int 处理的分类数量
     */
    protected function processChildCategories($parentCatId, $mallid, $cookie, $pathName, $isConsole = false, $command = null)
    {
        $count = 0;

        // 获取子分类
        if ($isConsole && $command) {
            $command->line("  正在获取分类ID: {$parentCatId} 的子分类...");
        }

        $childCategories = $this->fetchCategories($mallid, $cookie, $parentCatId);

        if (isset($childCategories['success']) && $childCategories['errorCode'] == 1000000 &&
            isset($childCategories['result']['categoryNodeVOS']) &&
            is_array($childCategories['result']['categoryNodeVOS'])) {

            $totalChildren = count($childCategories['result']['categoryNodeVOS']);

            if ($isConsole && $command) {
                $command->line("  找到 {$totalChildren} 个子分类，开始处理...");

                // 只有当子分类数量较多时才显示进度条
                if ($totalChildren > 10) {
                    $childBar = $command->getOutput()->createProgressBar($totalChildren);
                    $childBar->start();
                }
            }

            foreach ($childCategories['result']['categoryNodeVOS'] as $index => $category) {
                // 构建分类路径名称
                $currentPathName = $pathName . ',' . $category['catName'];

                // 保存分类到数据库
                $this->saveCategoryToDatabase($category, $parentCatId, $currentPathName);
                $count++;

                if ($isConsole && $command) {
                    // 只有当子分类数量较少时才详细输出每个分类
                    if ($totalChildren <= 10) {
                        $command->line("    处理子分类 [{$index}/{$totalChildren}]: {$category['catName']} (ID: {$category['catId']})");
                    }
                }

                // 如果不是叶子节点，则递归获取子分类
                if (!$category['isLeaf']) {
                    $childrenCount = $this->processChildCategories(
                        $category['catId'],
                        $mallid,
                        $cookie,
                        $currentPathName,
                        $isConsole,
                        $command
                    );
                    $count += $childrenCount;

                    if ($isConsole && $command && $totalChildren <= 10) {
                        $command->line("    完成子分类 {$category['catName']} 的子分类采集，共 {$childrenCount} 个子分类");
                    }
                }

                if ($isConsole && $command && isset($childBar) && $totalChildren > 10) {
                    $childBar->advance();
                }
            }

            if ($isConsole && $command && isset($childBar) && $totalChildren > 10) {
                $childBar->finish();
                $command->newLine();
            }
        } else if ($isConsole && $command) {
            $command->line("  未找到分类ID: {$parentCatId} 的子分类或API请求失败");
        }

        return $count;
    }

    /**
     * 从API获取分类数据
     *
     * @param string $mallid 商城ID
     * @param string $cookie Cookie
     * @param int|null $parentCatId 父分类ID，为null时获取第一级分类
     * @return array API返回的数据
     */
    protected function fetchCategories($mallid, $cookie, $parentCatId = null)
    {
        $client = new Client([
            'timeout' => 30,
            'verify' => false
        ]);

        $headers = [
            'authority' => 'seller.kuajingmaihuo.com',
            'content-type' => 'application/json',
            'cookie' => $cookie,
            'mallid' => $mallid,
            'origin' => 'https://seller.kuajingmaihuo.com',
            'referer' => 'https://seller.kuajingmaihuo.com/goods/product-create/category?from=productList',
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        ];

        $jsonData = [];
        if ($parentCatId !== null) {
            $jsonData['parentCatId'] = $parentCatId;
        }
        $jsonData['showCatEnName'] = true;

        try {
            $response = $client->post(
                'https://seller.kuajingmaihuo.com/bg-anniston-mms/category/children/list',
                [
                    'headers' => $headers,
                    'json' => $jsonData
                ]
            );

            return json_decode($response->getBody()->getContents(), true);
        } catch (GuzzleException $e) {
            Log::error('Temu分类API请求失败: ' . $e->getMessage());
            return [
                'success' => false,
                'errorCode' => 9999999,
                'errorMsg' => $e->getMessage()
            ];
        }
    }

    /**
     * 保存分类到数据库
     *
     * @param array $category 分类数据
     * @param int $parentId 父分类ID
     * @param string|null $pathName 分类路径名称
     * @return ProductCategoryTeMuModel 保存的分类模型
     */
    protected function saveCategoryToDatabase($category, $parentId, $pathName = null)
    {
        // 确定分类层级
        $level = 1;
        if ($parentId > 0) {
            // 获取父分类的层级
            $parentCategory = ProductCategoryTeMuModel::query()->where('id', $parentId)->first();
            if ($parentCategory) {
                $level = $parentCategory->level + 1;
            }
        }

        // 构建分类路径
        $path = $parentId > 0 ? $parentId : $category['catId'];
        if ($parentId > 0) {
            $parentCategory = ProductCategoryTeMuModel::query()->where('id', $parentId)->first();
            if ($parentCategory && !empty($parentCategory->path)) {
                $path = $parentCategory->path . ',' . $category['catId'];
            }
        }

        // 如果没有提供pathName，则使用当前分类名称
        if ($pathName === null) {
            $pathName = $category['catName'];
        }

        // 准备保存的数据
        $data = [
            'id' => $category['catId'],
            'name' => $category['catName'],
            'name_en' => $category['catEnName'] ?? '',
            'parent_id' => $parentId,
            'is_leaf' => $category['isLeaf'] ? 1 : 0,
            'level' => $level,
            'path' => $path,
            'path_name' => $pathName,
            'sort_order' => 0,
            'status' => 1,
        ];

        // 使用updateOrCreate避免重复插入
        return ProductCategoryTeMuModel::query()->updateOrCreate(
            ['id' => $category['catId']],
            $data
        );
    }

    /**
     * 更新Cookie状态为无效
     */
    protected function updateCookieStatus()
    {
        UserAccountModel::query()
            ->where('user_id', 1)
            ->where('account_type', 1)
            ->where('cookie_status', 1)
            ->update([
                'cookie_status' => 0,
                'cookie_time' => time()
            ]);
    }

    /**
     * 从网页JSON文件中采集商品分类并保存到数据库
     *
     * @param bool $isConsole 是否是命令行执行
     * @param object|null $command 命令行对象，用于输出进度信息
     * @return array 返回采集结果
     */
    public function collectCategoriesFromWebPage($isConsole = false, $command = null)
    {
        $jsonFilePath = storage_path('temu/cat_web.json');

        // 检查文件是否存在
        if (!File::exists($jsonFilePath)) {
            $message = "分类数据文件不存在: {$jsonFilePath}";
            if ($isConsole && $command) {
                $command->error($message);
            }
            return [
                'status' => 0,
                'message' => $message
            ];
        }

        try {
            // 读取JSON文件内容
            $jsonContent = File::get($jsonFilePath);
            $categoriesData = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $message = "JSON解析错误: " . json_last_error_msg();
                if ($isConsole && $command) {
                    $command->error($message);
                }
                return [
                    'status' => 0,
                    'message' => $message
                ];
            }

            // 检查数据结构是否符合预期
            if (!isset($categoriesData['result']['data']['opt_list']) || !is_array($categoriesData['result']['data']['opt_list'])) {
                $message = "JSON数据结构不符合预期";
                if ($isConsole && $command) {
                    $command->error($message);
                }
                return [
                    'status' => 0,
                    'message' => $message
                ];
            }

            // 开始处理分类数据
            $categoryCount = 0;
            $optList = $categoriesData['result']['data']['opt_list'];
            $totalCategories = count($optList);

            if ($isConsole && $command) {
                $command->info("开始处理网页分类数据，共 {$totalCategories} 个大分类");
            }

            foreach ($optList as $index => $category) {
                // 保存大分类到数据库并获取保存后的模型
                $savedCategory = $this->saveWebPageCategoryToDatabase($category, 0);

                // 如果返回null，表示该分类被忽略（ID为0），跳过后续处理
                if ($savedCategory === null) {
                    if ($isConsole && $command) {
                        $command->newLine();
                        $command->comment("忽略大分类 [{$index}/{$totalCategories}]: {$category['opt_name']} (ID: {$category['opt_id']})，ID为0或无效");
                    }
                    continue;
                }

                $categoryCount++;

                if ($isConsole && $command) {
                    $command->newLine();
                    $command->info("处理大分类 [{$index}/{$totalCategories}]: {$category['opt_name']} (ID: {$savedCategory->id})");
                }

                // 如果有子分类，则递归处理
                if (isset($category['child_opts']) && is_array($category['child_opts']) && count($category['child_opts']) > 0) {
                    if ($isConsole && $command) {
                        $command->info("开始获取 {$category['opt_name']} 的子分类...");
                    }
                    
                    //var_dump($category['child_opts']);
                    //写入JSON文件
                    /* file_put_contents(storage_path('temu/家居厨房用品.json'), json_encode($category['child_opts'], JSON_PRETTY_PRINT));
                    if($category['opt_name']=="家居厨房用品"){
                        exit();
                    } */
                    $childrenCount = $this->processWebPageChildCategories(
                        $savedCategory->id, // 使用保存后的ID
                        $category['opt_name'],
                        $category['child_opts'][0]['child_opts'],
                        $isConsole,
                        $command
                    );

                    $categoryCount += $childrenCount;

                    if ($isConsole && $command) {
                        $command->info("完成 {$category['opt_name']} 的子分类采集，共 {$childrenCount} 个子分类");
                    }
                }
            }

            return [
                'status' => 1,
                'message' => '网页分类数据采集完成',
                'count' => $categoryCount
            ];

        } catch (\Exception $e) {
            Log::error('网页分类数据采集失败: ' . $e->getMessage());

            if ($isConsole && $command) {
                $command->error('网页分类数据采集失败: ' . $e->getMessage());
            }

            return [
                'status' => 0,
                'message' => '网页分类数据采集失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 递归处理网页子分类
     *
     * @param int $parentId 父分类ID
     * @param string $pathName 分类路径名称
     * @param array $childCategories 子分类数组
     * @param bool $isConsole 是否是命令行执行
     * @param object|null $command 命令行对象，用于输出进度信息
     * @return int 处理的分类数量
     */
    protected function processWebPageChildCategories($parentId, $pathName, $childCategories, $isConsole = false, $command = null)
    {
        $count = 0;
        $totalChildren = count($childCategories);

        if ($isConsole && $command) {
            $command->line("  正在处理 {$pathName} 的 {$totalChildren} 个子分类...");
        }

        // 如果子分类数量较多，显示进度条
        if ($isConsole && $command && $totalChildren > 10) {
            $childBar = $command->getOutput()->createProgressBar($totalChildren);
            $childBar->start();
        }

        foreach ($childCategories as $index => $category) {
            // 检查是否是有效的分类数据
            if (!is_array($category) || !isset($category['opt_name']) || !isset($category['opt_id'])) {
                if ($isConsole && $command) {
                    $command->comment("    跳过无效的子分类数据，索引: {$index}");
                }
                continue;
            }

            // 构建当前分类的完整路径名称
            $currentPathName = $pathName . ',' . $category['opt_name'];

            // 保存分类到数据库并获取保存后的模型
            $savedCategory = $this->saveWebPageCategoryToDatabase($category, $parentId, $currentPathName);

            // 如果返回null，表示该分类被忽略（ID为0或负数），跳过后续处理
            if ($savedCategory === null) {
                if ($isConsole && $command && $totalChildren <= 10) {
                    $command->line("    忽略子分类 [{$index}/{$totalChildren}]: {$category['opt_name']} (ID: {$category['opt_id']})，ID为0或无效");
                }
                continue;
            }

            $count++;

            if ($isConsole && $command && $totalChildren <= 10) {
                $command->line("    处理子分类 [{$index}/{$totalChildren}]: {$category['opt_name']} (ID: {$savedCategory->id})");
            }

            // 如果有子分类，则递归处理
            if (isset($category['child_opts']) && is_array($category['child_opts']) && count($category['child_opts']) > 0) {
                $childrenCount = $this->processWebPageChildCategories(
                    $savedCategory->id, // 使用保存后的ID
                    $currentPathName,
                    $category['child_opts'],
                    $isConsole,
                    $command
                );
                $count += $childrenCount;

                if ($isConsole && $command && $totalChildren <= 10) {
                    $command->line("    完成子分类 {$category['opt_name']} 的子分类采集，共 {$childrenCount} 个子分类");
                }
            }

            if ($isConsole && $command && isset($childBar) && $totalChildren > 10) {
                $childBar->advance();
            }
        }

        if ($isConsole && $command && isset($childBar) && $totalChildren > 10) {
            $childBar->finish();
            $command->newLine();
        }

        return $count;
    }

    /**
     * 保存网页分类到数据库
     *
     * @param array $category 分类数据
     * @param int $parentId 父分类ID
     * @param string|null $pathName 分类路径名称
     * @return ProductCategoryWebPageTeMuModel 保存的分类模型
     */
    protected function saveWebPageCategoryToDatabase($category, $parentId, $pathName = null)
    {
        // 确定分类层级
        $level = 1;
        if ($parentId > 0) {
            // 获取父分类的层级
            $parentCategory = ProductCategoryWebPageTeMuModel::query()->where('id', $parentId)->first();
            if ($parentCategory) {
                $level = $parentCategory->level + 1;
            }
        }

        // 获取分类ID
        $categoryId = (int)$category['opt_id'];

        // 如果当前分类ID为0或负数，则记录日志并返回null，表示不处理该分类
        if ($categoryId === 0 || $categoryId < 0) {
            Log::warning('忽略ID为0或负数的分类, 分类名称: ' . $category['opt_name'] . ', ID: ' . $categoryId);
            return null;
        }

        // 构建分类路径
        $path = $parentId > 0 ? (string)$parentId : (string)$categoryId;
        if ($parentId > 0) {
            $parentCategory = ProductCategoryWebPageTeMuModel::query()->where('id', $parentId)->first();
            if ($parentCategory && !empty($parentCategory->path)) {
                // 检查是否需要拼接的ID为0，如果是则忽略该分类
                if ($categoryId === 0) {
                    Log::warning('忽略需要拼接ID为0的分类, 分类名称: ' . $category['opt_name']);
                    return null;
                }
                $path = $parentCategory->path . ',' . $categoryId;
            }
        }

        // 如果没有提供pathName，则使用当前分类名称
        if ($pathName === null) {
            $pathName = $category['opt_name'];
        }

        // 判断是否为叶子节点
        $isLeaf = (!isset($category['child_opts']) || !is_array($category['child_opts']) || count($category['child_opts']) === 0) ? 1 : 0;

        // 准备保存的数据
        $data = [
            'id' => $categoryId,
            'name' => $category['opt_name'],
            'name_en' => '', // 默认为空字符串
            'parent_id' => $parentId,
            'is_leaf' => $isLeaf,
            'level' => $level,
            'path' => $path,
            'path_name' => $pathName,
            'sort_order' => 0, // 保持原始顺序
            'status' => 1, // 默认启用
        ];

        // 使用updateOrCreate避免重复插入
        return ProductCategoryWebPageTeMuModel::query()->updateOrCreate(
            ['id' => $categoryId],
            $data
        );
    }
}