import config from '@/config';
import { getLocalStorageData } from '@/utils/new/utils';

/**
 * 处理商品图片本地化
 * @param goodsId 商品ID
 * @param processStep 处理步骤
 * @returns Promise<any>
 */
export const processGoodsImages = async (goodsId: number, processStep: number): Promise<any> => {
  const url = (config as any).apiUserProcessGoodsImagesUrl;
  if (!url) {
    console.error('Process goods images URL is not configured.');
    throw new Error('图片处理接口未配置');
  }

  return new Promise<any>(async (resolve, reject) => {
    try {
      console.log('Processing goods images:', { goodsId, processStep });
      
      // 获取本地存储的 token
      const localData = await getLocalStorageData(['token']) as { token?: string };
      const token = localData.token;

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = 'Bearer ' + token;
      }

      const requestData = {
        goods_id: goodsId,
        process_step: processStep
      };

      chrome.runtime.sendMessage({
        funType: 'axios',
        funName: 'processGoodsImagesRequest',
        pramas: requestData,
        headers: headers as any,
        method: 'post',
        url: url
      }, (response: any) => {
        if (response && response[0] && response[0].data) {
          const result = response[0].data;
          console.log('processGoodsImagesRequest 返回结果:', result);
          
          if (result.code === 1) {
            resolve(result);
          } else {
            console.error('图片处理请求失败:', result.errMsg || 'Unknown error');
            reject(new Error(result.errMsg || '图片处理请求失败'));
          }
        } else {
          console.error('图片处理请求失败 - Invalid response structure:', response);
          reject(new Error('Invalid response structure'));
        }
      });
    } catch (error) {
      console.error('Failed to process goods images:', error);
      reject(error);
    }
  });
}; 