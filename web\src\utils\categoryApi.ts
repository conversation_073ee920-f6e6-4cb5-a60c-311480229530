/**
 * 商品分类管理API接口
 */
import { sendRequestViaBackground } from './api'

// 关联的N11分类信息接口
export interface LinkedN11Category {
  id: number
  name: string
  name_tl: string
  path_name: string
  path_name_tl: string
}

// 分类接口类型定义
export interface Category {
  id: number
  name: string
  name_en: string
  parent_id: number
  is_leaf: boolean
  is_linked?: boolean  // 新增：是否已关联第三方平台
  level: number
  path: string
  path_name: string
  sort_order: number
  status: number
  status_text: string
  created_at: string
  updated_at: string
  children?: Category[]
  hasChildren?: boolean  // 新增：是否有子分类（用于懒加载）
  childrenCount?: number // 新增：子分类数量
  linked_n11_category?: LinkedN11Category | null // 新增：关联的N11分类信息
}

export interface CategoryListParams {
  page: number
  pageSize: number
  parent_id?: number
  name?: string
}

// 新增：懒加载分类列表参数
export interface CategoryLazyListParams {
  parent_id: number
  page?: number
  pageSize?: number
  name?: string
}

// 新增：扁平化分类列表参数
export interface CategoryFlatListParams {
  level?: number
  parent_id?: number
  page: number
  pageSize: number
  name?: string
}

export interface CategoryListResponse {
  list: Category[]
  pagination: {
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
    currentPage: number
    pageSize: number
  }
}

export interface MainCategoryListResponse {
  list: Category[]
}

// 新增：已关联分类信息接口
export interface LinkedCategory {
  id: number
  name: string
  name_tl: string
  path_name: string
  path_name_tl: string
  platform_id: number
  platform_code: string
  level: number
  is_leaf: boolean
}

export interface LinkedCategoryListResponse {
  list: LinkedCategory[]
}

// 新增：子分类数量响应接口
export interface ChildrenCountResponse {
  parent_id: number
  count: number
}

export interface ChildrenCountsResponse {
  counts: ChildrenCountResponse[] | { [key: string]: number }  // 支持数组或对象格式
}

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      console.log('获取API地址响应:', response)
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 获取主分类列表（一级分类）
 * @returns 主分类列表响应
 */
export const getMainCategoryList = async (): Promise<MainCategoryListResponse> => {
  const url = await getApiUrl('apiCatTemuMainUrl');
  console.log('获取主分类列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getMainCategoryList',
    url,
    method: 'get',
    auth: true
  });
};

/**
 * 获取分类列表（支持树形结构）
 * @param params 查询参数
 * @returns 分类列表响应
 */
export const getCategoryList = async (params: CategoryListParams): Promise<CategoryListResponse> => {
  const url = await getApiUrl('apiCatTemuListUrl');
  console.log('获取分类列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getCategoryList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 获取懒加载分类列表（只返回指定父分类的直接子分类）
 * @param params 查询参数
 * @returns 分类列表响应
 */
export const getCategoryListLazy = async (params: CategoryLazyListParams): Promise<CategoryListResponse> => {
  const url = await getApiUrl('apiCatTemuListLazyUrl');
  console.log('获取懒加载分类列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getCategoryListLazy',
    url,
    method: 'get',
    params: {
      ...params,
      page: params.page || 1,
      pageSize: params.pageSize || 100
    },
    auth: true
  });
};

/**
 * 获取扁平化分类列表（用于虚拟表格）
 * @param params 查询参数
 * @returns 分类列表响应
 */
export const getCategoryListFlat = async (params: CategoryFlatListParams): Promise<CategoryListResponse> => {
  const url = await getApiUrl('apiCatTemuListflatUrl');
  console.log('获取扁平化分类列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getCategoryListFlat',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 获取单个分类的子分类数量
 * @param parentId 父分类ID
 * @returns 子分类数量
 */
export const getCategoryChildrenCount = async (parentId: number): Promise<ChildrenCountResponse> => {
  const url = await getApiUrl('apiCatTemuChildrenCountUrl');
  console.log('获取子分类数量URL:', url)
  return sendRequestViaBackground({
    funName: 'getCategoryChildrenCount',
    url,
    method: 'get',
    params: { parent_id: parentId },
    auth: true
  });
};

/**
 * 批量获取多个分类的子分类数量
 * @param parentIds 父分类ID数组
 * @returns 子分类数量列表
 */
export const getCategoryChildrenCounts = async (parentIds: number[]): Promise<ChildrenCountsResponse> => {
  const url = await getApiUrl('apiCatTemuChildrenCountsUrl');
  console.log('批量获取子分类数量URL:', url)
  return sendRequestViaBackground({
    funName: 'getCategoryChildrenCounts',
    url,
    method: 'get',
    params: { parent_ids: parentIds },
    auth: true
  });
};

/**
 * 获取已关联的第三方平台分类列表
 * @param temuCategoryId Temu分类ID
 * @returns 已关联分类列表响应
 */
export const getLinkedCategoryList = async (temuCategoryId: number): Promise<LinkedCategoryListResponse> => {
  const url = await getApiUrl('apiCatRelationListUrl');
  console.log('获取已关联分类列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getLinkedCategoryList',
    url,
    method: 'get',
    params: { cat_platform_id: temuCategoryId },
    auth: true
  });
};

/**
 * 获取单个分类的详细信息
 * @param categoryId 分类ID
 * @returns 分类详细信息
 */
export const getCategoryDetail = async (categoryId: number): Promise<Category> => {
  const url = await getApiUrl('apiCatTemuDetailUrl');
  console.log('获取分类详情URL:', url)
  return sendRequestViaBackground({
    funName: 'getCategoryDetail',
    url,
    method: 'get',
    params: { id: categoryId },
    auth: true
  });
}; 