import config from '@/config'
import { N11RejectedProduct } from './n11RejectedApi'

// 导入Chrome扩展消息发送函数
declare const chrome: any;

/**
 * 通过background页面发送请求
 */
function sendRequestViaBackground(url: string, options: any): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      url: url,
      method: options.method || 'GET',
      pramas: options.body ? JSON.parse(options.body) : {},
      headers: options.headers || {},
      auth: options.auth || false,
      encrypto: options.encrypto || false,
      timeout: 15000
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      
      if (response && response[0] && response[0].data) {
        const result = response[0].data;
        if (result.code == 1) {
          resolve({ success: true, data: result.data });
        } else {
          reject(new Error(`请求失败: ${result.status}`));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
}

// 批量保存响应类型
export interface BatchSaveResponse {
  inserted_count: number
  duplicate_count: number
  pending_total: number
  message: string
}

// 待处理商品类型
export interface PendingProduct {
  id: number
  product_id: number
  stock_code: string
  title: string
  sales_price: number
  quantity: number
}

// 统计信息类型
export interface ProcessingStatistics {
  pending_count: number
  completed_count: number
  total_count: number
}

/**
 * 批量保存被拒绝商品到后端
 * @param products 商品数据数组
 * @returns 保存结果统计
 */
export async function batchSaveRejectedProducts(products: N11RejectedProduct[]): Promise<BatchSaveResponse> {
  try {
    // 转换数据格式
    const formattedProducts = products.map(product => ({
      product_id: product.id,
      sku_id: product.skuId,
      title: product.title,
      commission: product.commission,
      image_url: product.imageUrl,
      brand: product.brand,
      commission_rate_value: product.commissionRateValue,
      stock_code: product.stockCode,
      barcode: product.barcode,
      product_main_id: product.productMainId,
      sales_price: product.salesPrice,
      list_price: product.listPrice,
      quantity: product.quantity,
      status_original: product.statusOriginal,
      group_id: product.groupId,
      category_name: product.categoryName,
      preparing_time: product.preparingTime,
      catalog_id: product.catalogId,
      shipment_template: product.shipmentTemplate,
      in_approval_reason: product.inApprovalReason,
      reject_info: product.rejectInfo,
      vat_rate: product.vatRate
    }))

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ products: formattedProducts }),
      auth: true,
      encrypto: true
    }

    const response = await sendRequestViaBackground(
      config.apiN11RejectedProductBatchSaveUrl,
      options
    )

    if (response.success && response.data) {
      return response.data
    } else {
      throw new Error(response.message || '批量保存失败')
    }
  } catch (error) {
    console.error('批量保存被拒绝商品失败:', error)
    throw error
  }
}

/**
 * 获取待处理商品数量
 * @returns 待处理商品数量
 */
export async function getPendingCount(): Promise<number> {
  try {
    const options = {
      method: 'GET',
      auth: true,
      encrypto: true
    }

    const response = await sendRequestViaBackground(
      config.apiN11RejectedProductPendingCountUrl,
      options
    )

    if (response.success && response.data) {
      return response.data.pending_count || 0
    } else {
      throw new Error(response.message || '获取待处理数量失败')
    }
  } catch (error) {
    console.error('获取待处理商品数量失败:', error)
    throw error
  }
}

/**
 * 获取下一个待处理商品
 * @returns 待处理商品信息
 */
export async function getNextPendingProduct(): Promise<PendingProduct | null> {
  try {
    const options = {
      method: 'GET',
      auth: true,
      encrypto: true
    }

    const response = await sendRequestViaBackground(
      config.apiN11RejectedProductNextPendingUrl,
      options
    )

    if (response.success && response.data) {
      return response.data.product
    } else {
      throw new Error(response.message || '获取待处理商品失败')
    }
  } catch (error) {
    console.error('获取下一个待处理商品失败:', error)
    throw error
  }
}

/**
 * 更新商品处理状态
 * @param productId 商品ID
 * @param status 状态（0=待处理，1=已完成）
 * @returns 更新结果
 */
export async function updateProductStatus(productId: string|number, status: number): Promise<boolean> {
  if(typeof productId === 'number'){
    productId = productId.toString()
  }
  try {
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        product_id: productId, 
        status: status 
      }),
      auth: true,
      encrypto: true
    }

    const response = await sendRequestViaBackground(
      config.apiN11RejectedProductUpdateStatusUrl,
      options
    )

    if (response.success) {
      return true
    } else {
      throw new Error(response.message || '更新状态失败')
    }
  } catch (error) {
    console.error('更新商品状态失败:', error)
    throw error
  }
}

/**
 * 获取处理统计信息
 * @returns 统计信息
 */
export async function getProcessingStatistics(): Promise<ProcessingStatistics> {
  try {
    const options = {
      method: 'GET',
      auth: true,
      encrypto: true
    }

    const response = await sendRequestViaBackground(
      config.apiN11RejectedProductStatisticsUrl,
      options
    )

    if (response.success && response.data) {
      return response.data
    } else {
      throw new Error(response.message || '获取统计信息失败')
    }
  } catch (error) {
    console.error('获取处理统计信息失败:', error)
    throw error
  }
} 