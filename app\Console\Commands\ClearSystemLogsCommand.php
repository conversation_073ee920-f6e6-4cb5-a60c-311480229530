<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearSystemLogsCommand extends Command
{
    /**
     * 命令名称和参数

     *
     * @var string
     */
    protected $signature = 'system:log_clear';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理2天前的日志记录';

    /**
     * 执行命令
     */
    public function handle()
    {
        try {
            // 获取2天前的时间
            $date = date('Y-m-d', strtotime('-2 days'));    
            // 删除数据库旧日志
            $count = DB::table('system_log')->where('add_time', '<', $date)->delete();
            $this->info("成功清理 {$count} 条2天前的日志记录");

            // 清理日志文件
            $logPath = storage_path('logs/sql');
            if (!is_dir($logPath)) {
                $this->info("日志目录 {$logPath} 不存在");
                return;
            }

            $deletedFiles = 0;
            $files = glob($logPath . '/sql-*.log');

            foreach ($files as $file) {
                if (preg_match('/sql-(\d{4}-\d{2}-\d{2})\.log/', basename($file), $matches)) {
                    $fileDate = $matches[1];
                    if ($fileDate < $date) {
                        unlink($file);
                        $deletedFiles++;
                        $this->info("已删除日志文件: " . basename($file));
                    }
                }
            }
            
            $this->info("成功清理 {$deletedFiles} 个日志文件");

        } catch (\Exception $e) {
            $this->error("清理日志时发生错误: " . $e->getMessage());
        }
    }
} 