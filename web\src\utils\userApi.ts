/**
 * 用户相关API接口
 */
import { sendRequestViaBackground } from './api'

// 用户店铺信息接口类型定义
export interface UserStoreInfo {
  is_admin: number
  phone: string
  is_vip: number
  vip_end_time: string
  number_all: number
  number_used: number
  number_left: number
  store_list: Array<{
    id: number
    account_name: string
  }>
}

/**
 * 获取配置中的API地址
 * @param configKey 配置键名
 * @returns Promise<string> API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      console.log('获取API地址响应:', response)
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 获取用户店铺信息
 * @returns 用户店铺信息响应
 */
export const getUserStoreInfo = async (): Promise<UserStoreInfo> => {
  const url = await getApiUrl('apiUserStoreInfoUrl');
  console.log('获取用户店铺信息URL:', url)
  return sendRequestViaBackground({
    funName: 'getUserStoreInfo',
    url,
    method: 'get',
    auth: true
  });
}; 