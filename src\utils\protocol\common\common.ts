import {useChrome,useChromeNew } from "@/hooks";
import config from '@/config/index';
import { ElNotification } from 'element-plus';


interface SecurityTokens {
  msToken: string;
  a_bogus: string;
}

export const generateSecurityTokens = (): SecurityTokens => {
  const msTokenChars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_=';
  const bogusChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const generateRandomString = (length: number, chars: string): string => {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };
  const msToken = generateRandomString(140, msTokenChars);
  const a_bogus = generateRandomString(44, bogusChars);

  return {
    msToken,
    a_bogus
  };
};


export const validateAccountList = (accountList: any[]): any[] => {
  if (!Array.isArray(accountList)) {
    return [];
  }
  return accountList.filter((account:any) => !account.isCurrentAccount);
};

export const buildDouyinAvatarUrl = (avatarUri: string): string => {
  if (!avatarUri) {
    console.error('无效的头像URI');
    return '';
  }
  const baseUrl = 'https://p11.douyinpic.com/img/';
  const suffix = '~c5_168x168.webp?from=';
  return `${baseUrl}${avatarUri}${suffix}`;
};







interface ApiResponse {
  code: number;
  msg?: string;
  message?: string;
  data: any;
}

export const handleApiResponse = (response: any): any => {
  if (response && response[0] && response[0]['data']) {
    const result = response[0]['data'] as ApiResponse;
    if (result.code === 1) {
      return result.data;
    } else {
      // 关闭所有已存在的通知
      ElNotification.closeAll();
      console.log('---------------请求失败-------------------',result);
      ElNotification({
        title: '提示',
        message: result.msg || result.message || '请求失败',
        type: 'error',
      });
      return null;
    }
  }
  return null;
};


export const handleThirdPartyApiResponse = (response: any): any => {
  if (response && response[0] && response[0]['data']) {
    const result = response[0]['data'];
    if (response[0].status === 200) {
      return result;
    } else {
      // 关闭所有已存在的通知
      ElNotification.closeAll();
      ElNotification({
        title: '提示',
        message: result.msg ||  '请求失败',
        type: 'error',
      });
      return null;
    }
  }
  return null;
};


//获取COOKIE  返回的是字符串
export const getWebCookie = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    useChrome("cookies.getAll", {
      url: window.location.href
    }, (res:any) => {
      try {
        let cookiesArray = [];
        console.log(res);
        res = res || [];
        for (let i in res) {
          let cookie = res[i];
          cookiesArray.push(cookie.name + '=' + cookie.value);
        }
        let cookieString = cookiesArray.join('; ');
        resolve(cookieString);
      } catch (error) {
        console.log("获取cookie错误",error)
        reject(null);
      }
    });
  });
}

export const getDyUserInfoFromIesUid = (ies_uid:string):Promise<any>  => {
  const url = 'https://live.douyin.com/webcast/user/?aid=6383&target_uid=' + ies_uid;
  const headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Referer': 'https://www.douyin.com/',
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 DiDiPhone/3.x NetType/WIFI',
  };
  const data = {
    ies_uid
  };

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      funName: 'getDyUserInfoFromIesUidRequest',
      pramas: data,
      headers,
      method: 'get',
      url
    }, (response:any) => {
      if (response && response[0] && response[0].data) {
        resolve(response[0].data);
      } else {
        reject(null);
      }
    });
  });
}

//根据ies_uniq_id获取用户信息
export const getDyUserInfoFromUniqId = (ies_uniq_id:string):Promise<any>  => {
  const url = 'https://www.iesdouyin.com/web/api/v2/user/info/';
  const headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Referer': 'https://www.douyin.com/',
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 DiDiPhone/3.x NetType/WIFI',
  };
  const data = {
    unique_id: ies_uniq_id
  };

  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      funName: 'getDyUserInfoFromUniqIdRequest',
      pramas: data,
      headers,
      method: 'get',
      url
    }, (response:any) => {
      if (response && response[0] && response[0].data) {
        resolve(response[0].data);
      } else {
        reject(null);
      }
    });
  });
}

interface Cookie {
  domain: string;
  path: string;
  name: string;
  storeId: string;
  secure?: boolean;
  httpOnly?: boolean;
}

interface CookieStore {
  id: string;
  tabIds: number[];
}

export const forceRemoveAllCookies = async (): Promise<void> => {
  try {
    console.log('开始强制清除所有Cookie');

    // 获取所有Cookie
    const cookies = await getAllCookies();
    console.log(`找到 ${cookies.length} 个Cookie`);

    // 清除所有Cookie
    await Promise.all(
      cookies.map(cookie => removeCookie(cookie))
    );

    console.log('所有Cookie已成功清除');
  } catch (error) {
    console.error('清除Cookie时发生错误:', error);
  }
};

const getAllCookies = (): Promise<chrome.cookies.Cookie[]> => {
  return new Promise((resolve, reject) => {
    chrome.cookies.getAll({}, (cookies) => {
      if (chrome.runtime.lastError) {
        return reject(chrome.runtime.lastError);
      }
      resolve(cookies);
    });
  });
};

const removeCookie = (cookie: chrome.cookies.Cookie): Promise<void> => {
  return new Promise((resolve, reject) => {
    const url = `${cookie.secure ? 'https://' : 'http://'}${cookie.domain.replace(/^\./, '')}${cookie.path}`;
    chrome.cookies.remove({
      url,
      name: cookie.name,
      storeId: cookie.storeId
    }, (removedCookie) => {
      if (chrome.runtime.lastError) {
        console.error(`删除Cookie失败 (${cookie.name}):`, chrome.runtime.lastError);
        return resolve(); // 继续处理其他Cookie
      }
      if (removedCookie) {
        //console.log(`成功删除Cookie: ${cookie.name} from ${url}`);
      }
      resolve();
    });
  });
};

export const clearDomainCookies = (domain: string): Promise<boolean> => {
  return new Promise(async (resolve) => {
    try {
      // 移除开头的点号,统一域名格式
      const normalizedDomain = domain.replace(/^\./, '');
      console.log('开始清除域名的Cookie:', normalizedDomain);

      let retryCount = 0;
      const maxRetries = 3;

      const attemptClearCookies = async (): Promise<boolean> => {
        return new Promise((resolveAttempt) => {
          useChrome("cookies.getAll", { domain: normalizedDomain }, async (cookies: Cookie[]) => {
            if (!cookies || cookies.length === 0) {
              console.log('没有找到需要清除的Cookie');
              resolveAttempt(true);
              return;
            }

            console.log(`找到 ${cookies.length} 个Cookie需要清除:`, cookies);
            let removedCount = 0;
            const totalCookies = cookies.length;
            let hasError = false;

            for (const cookie of cookies) {
              try {
                // 尝试使用多种方式删除Cookie
                const domains = [
                  normalizedDomain,
                  `.${normalizedDomain}`,
                  cookie.domain,
                  cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain
                ];

                for (const d of domains) {
                  const removeDetails = {
                    url: `https://${d}${cookie.path}`,
                    name: cookie.name,
                    storeId: cookie.storeId
                  };

                  await new Promise<void>(resolveRemove => {
                    useChrome("cookies.remove", removeDetails, () => {
                      if (chrome.runtime.lastError) {
                        console.error(`尝试删除Cookie失败 (${cookie.name} on ${d}):`, chrome.runtime.lastError);
                      } else {
                        //console.log(`成功删除Cookie: ${cookie.name} from ${d}`);
                      }
                      resolveRemove();
                    });
                  });
                }
              } catch (error) {
                console.error(`删除Cookie时发生错误:`, error);
                hasError = true;
              }

              removedCount++;
              console.log(`当前进度: ${removedCount}/${totalCookies}`);
            }

            resolveAttempt(!hasError);
          });
        });
      };

      // 尝试清除Cookie，如果失败则重试
      let success = await attemptClearCookies();
      while (!success && retryCount < maxRetries) {
        console.log(`清除Cookie失败，第${retryCount + 1}次重试...`);
        retryCount++;
        success = await attemptClearCookies();
      }

      // 如果常规方法失败，尝试强制清除
      if (!success) {
        console.log('常规清除方法失败，尝试强制清除所有Cookie...');
        useChromeNew('forceRemoveAllCookies', (response: any) => {
          console.log('强制清除所有Cookie:', response);
        });
        success = true;
      }

      resolve(success);
    } catch (error) {
      console.error('清除Cookie过程中发生错误:', error);
      resolve(false);
    }
  });
};

export const clearSpecificCookies = (domain: string, cookieKeys: string[]): Promise<boolean> => {
  return new Promise((resolve) => {
    useChrome("cookies.getAll", { domain }, async (cookies: Cookie[]) => {
      try {
        if (!cookies || cookies.length === 0) {
          console.log('No cookies found for the domain');
          resolve(true);
          return;
        }

        const targetCookies = cookies.filter(cookie => cookieKeys.includes(cookie.name));
        if (targetCookies.length === 0) {
          console.log('No matching cookies found to clear');
          resolve(true);
          return;
        }

        const clearPromises = targetCookies.map(cookie => {
          return new Promise<void>((resolveSet) => {
            const url = `${cookie.secure ? 'https' : 'http'}://${cookie.domain}${cookie.path}`;
            console.log('清除cookie对应的url:',url);
            // 先尝试移除cookie
            console.log('准备先移除cookie:',cookie);
            useChrome("cookies.remove", {
              url,
              name: cookie.name,
              storeId: cookie.storeId
            }, () => {
              // 检查移除是否有错误
              if(chrome.runtime.lastError) {
                console.warn(`Remove cookie ${cookie.name} failed:`, chrome.runtime.lastError);
              }else{
                console.log('移除cookie成功,准备设置新cookie:',cookie);
              }
              // 然后设置新cookie
              useChrome("cookies.set", {
                url,
                name: cookie.name,
                value: '',
                domain: cookie.domain,
                path: cookie.path,
                secure: cookie.secure,
                httpOnly: cookie.httpOnly,
                sameSite: cookie.sameSite || 'Lax', // 显式设置SameSite
                storeId: cookie.storeId,
                expirationDate: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
              }, (result) => {
                if(chrome.runtime.lastError) {
                  console.error(`Set cookie ${cookie.name} failed:`, chrome.runtime.lastError);
                }else{
                  console.log('设置新cookie成功:',result);
                }
                resolveSet();
              });
            });
          });
        });

        await Promise.all(clearPromises);
        resolve(true);
      } catch (error) {
        console.error('Error clearing specific cookies:', error);
        resolve(false);
      }
    });
  });
};

export const updateCookie = (domain: string, cookieString: string): Promise<boolean> => {
  return new Promise(async (resolve, reject) => {
    try {
      // 先清除所有已存在的cookie
      const isCleaned = await clearDomainCookies(domain);
      if (!isCleaned) {
        console.error("清除旧cookie失败");
        reject(false);
        return;
      }
      // 设置新的cookie
      const cookies = cookieString.split('; ').map(cookie => cookie.trim());
      const promises = cookies.map(cookie => {
        const [name, value] = cookie.split('=');
        return new Promise((resolveInner) => {
          useChrome("cookies.set", {
            url: 'https://' + domain,
            name,
            value,
            domain,
            path: '/',
            secure: true,
            httpOnly: false
          }, (result: any) => {
            resolveInner(result);
          });
        });
      });

      const results = await Promise.all(promises);
      console.log("更新cookie结果:", results);
      resolve(results.every(result => result !== null && result !== undefined));
    } catch (error) {
      ElNotification({
        title: '提示',
        message: '切换账号失败,请手动刷新页面,重新操作',
        type: 'error',
      });
      reject(false);
    }
  });
}


export const getMemberList = async(website_id:number,crx_identify:string,user_name:string='',phone:string='',user_id:number=0) => {
  const url = config.apiMemberList;
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36',
  }
  const data = {
    website_id,
    crx_identify,
    user_name,
    phone,
    user_id
  }
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      funName: 'getMemberListRequest',
      pramas: data,
      headers,
      method: 'get',
      url
    }, (response:any) => {
      if (response && response[0] && response[0]['data']) {
        resolve(response[0]['data']); // 解析结果
      } else {
        reject(null);
      }
    });
  });
}


export const sendDataToAiService = (data:{},callback:(data:any)=>void) => {
  const url = config.apiAiSendDataUrl;
  const headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36'
  }
  chrome.runtime.sendMessage({
    funType: 'axios',
    funName: 'sendDataToAiServiceRequest',
    pramas: data,
    headers,
    method: 'post',
    url
  }, (response:any) => {
    console.log(' ai send service response',response);
    if (response && response[0]) {
      if(response[0].status == 200){
        callback(response[0].data)
      }else{
        callback({
          code: -1,
          msg: '请求失败'
        });
      }
    } else {
      callback(null);
    }
  });
}

/**
 * 获取认证请求头
 * 从本地存储中获取token，并添加到请求头中
 * @returns Promise<{Authorization: string}> 包含认证信息的请求头对象
 */
export const getAuthHeaders = async (): Promise<{Authorization?: string}> => {
  try {
    // 从chrome.storage.local获取token
    return new Promise((resolve) => {
      chrome.storage.local.get(['token'], (result) => {
        if (result.token) {
          resolve({ Authorization: `Bearer ${result.token}` });
        } else {
          resolve({});
        }
      });
    });
  } catch (error) {
    console.error('获取认证头信息失败:', error);
    return {};
  }
}

export const getCurrentTimestamp = () => {
  return Date.now();
}

/**
 * 获取当前时间戳的16进制表示
 */
export const getTimestampHex = (): string => {
  const timestamp = Math.floor(Date.now() / 1000);
  return timestamp.toString(16);
};

/**
 * 生成8位随机16进制字符串
 */
export const generateRandomHex = (): string => {
  return Array.from({length: 8}, () =>
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
};

/**
 * 生成由时间戳和随机字符串组成的唯一标识符
 */
export const generateUniqueId = (): string => {
  return `${getTimestampHex()}-${generateRandomHex()}`;
};

// 清除指定域名的cookie 也不设置为空  仅移除
export const clearSpecificDomainCookies = (domain: string, cookieKeys: string[]): Promise<boolean> => {
  return new Promise((resolve) => {
    try {
      // 移除开头的点号,统一域名格式
      const normalizedDomain = domain.replace(/^\./, '');
      console.log('开始清除指定的Cookie:', normalizedDomain, cookieKeys);

      useChrome("cookies.getAll", { domain: normalizedDomain }, async (cookies: Cookie[]) => {
        if (!cookies || cookies.length === 0) {
          console.log('没有找到需要清除的Cookie');
          resolve(true);
          return;
        }

        // 过滤出需要清除的cookie
        const targetCookies = cookies.filter(cookie => cookieKeys.includes(cookie.name));
        console.log(`找到 ${targetCookies.length} 个匹配的Cookie需要清除`);

        if (targetCookies.length === 0) {
          resolve(true);
          return;
        }

        const clearPromises = targetCookies.map(cookie => {
          return new Promise<void>((resolveRemove) => {
            const url = `${cookie.secure ? 'https' : 'http'}://${cookie.domain}${cookie.path}`;

            useChrome("cookies.remove", {
              url,
              name: cookie.name,
              storeId: cookie.storeId
            }, () => {
              if (chrome.runtime.lastError) {
                console.error(`删除Cookie失败 (${cookie.name}):`, chrome.runtime.lastError);
              } else {
                console.log(`成功删除Cookie: ${cookie.name}`);
              }
              resolveRemove();
            });
          });
        });

        await Promise.all(clearPromises);
        resolve(true);
      });
    } catch (error) {
      console.error('清除Cookie过程中发生错误:', error);
      resolve(false);
    }
  });
};

export const setSpecificDomainCookies = (domain: string, cookieKeys: string[], cookieString: string): Promise<boolean> => {
  return new Promise((resolve) => {
    try {
      // 移除开头的点号,统一域名格式
      const normalizedDomain = domain.replace(/^\./, '');
      console.log('开始设置指定的Cookie:', normalizedDomain, cookieKeys);

      // 解析cookie字符串
      const cookiePairs = cookieString.split('; ').reduce((acc, pair) => {
        const [name, value] = pair.split('=');
        acc[name.trim()] = value;
        return acc;
      }, {} as { [key: string]: string });

      // 过滤出需要设置的cookie
      const targetCookies = cookieKeys.filter(key => key in cookiePairs);
      console.log(`找到 ${targetCookies.length} 个匹配的Cookie需要设置`);

      if (targetCookies.length === 0) {
        console.log('没有找到需要设置的Cookie');
        resolve(true);
        return;
      }

      const setPromises = targetCookies.map(cookieName => {
        return new Promise<void>((resolveSet) => {
          useChrome("cookies.set", {
            url: 'https://' + normalizedDomain,
            name: cookieName,
            value: cookiePairs[cookieName],
            domain: normalizedDomain,
            path: '/',
            secure: true,
            httpOnly: false
          }, (result) => {
            if (chrome.runtime.lastError) {
              console.error(`设置Cookie失败 (${cookieName}):`, chrome.runtime.lastError);
            } else {
              console.log(`成功设置Cookie: ${cookieName}=${cookiePairs[cookieName]}`);
            }
            resolveSet();
          });
        });
      });

      Promise.all(setPromises)
        .then(() => {
          console.log('所有指定Cookie设置完成');
          resolve(true);
        })
        .catch(error => {
          console.error('设置Cookie过程中发生错误:', error);
          resolve(false);
        });

    } catch (error) {
      console.error('设置Cookie过程中发生错误:', error);
      resolve(false);
    }
  });
};

export const updateSpecificDomainCookie = async (domain: string, cookieKeys: string[], cookieString: string): Promise<boolean> => {
  const removeCookie = await clearSpecificDomainCookies(domain, cookieKeys);
  console.log("removeCookie",removeCookie);
  if(!removeCookie){
    return false;
  }
  const result = await setSpecificDomainCookies(domain, cookieKeys, cookieString);
  console.log("更新cookie结果:",result);
  return result;
}
