<?php
declare(strict_types=1);

namespace App\Models\User;
use App\Models\BaseModel;
use App\Models\TeMu\ProductCategoryTeMuModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\TeMu\ProductCategoryWebPageTeMuModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\System\CatRelationSystemModel;

class GoodsModel extends BaseModel
{
    protected $table = 'user_goods';
    
    protected $fillable = [
        'user_id',
        'directory_id',
        'type',
        'source_url',
        'cat_id',
        'front_cat_id_1',
        'front_cat_id_2',
        'front_cat_desc',
        'mall_id',
        'goods_id',
        'goods_name',
        'goods_detail',
        'goods_video',
        'goods_pic',
        'goods_sku',
        'goods_sku_num',
        'goods_property',
        'goods_sold_quantity',
        'goods_score',
        'status',
    ];

    // protected $casts = [
    //     'user_id' => 'integer',
    //     'type' => 'integer',
    //     'cat_id' => 'integer',
    //     'front_cat_id_1' => 'integer',
    //     'front_cat_id_2' => 'integer',
    //     'mall_id' => 'integer',
    //     'goods_id' => 'integer',
    //     'goods_sku_num' => 'integer',
    //     'status' => 'integer',
    //     'created_at' => 'datetime',
    //     'updated_at' => 'datetime',
    //     'goods_pic' => 'array',
    //     'goods_sku' => 'array',
    //     'goods_property' => 'string',
    // ];

    // 商品类型常量
    const TYPE_TEMU = 1;

    // 商品类型映射
    public static function getTypes(): array
    {
        return [
            self::TYPE_TEMU => 'Temu',
        ];
    }

    // 获取商品类型名称
    public function getTypeName(): string
    {
        $types = self::getTypes();
        return $types[$this->type] ?? '未知';
    }

    // 检查是否属于指定用户
    public function belongsToUser(int $userId): bool
    {
        return $this->user_id === $userId;
    }

    // 关联商品SKU
    public function skus(): HasMany
    {
        return $this->hasMany(GoodsSkuModel::class, 'user_goods_id', 'id');
    }

    // 关联前端分类1
    public function frontCategory1(): BelongsTo
    {
        return $this->belongsTo(ProductCategoryWebPageTeMuModel::class, 'front_cat_id_1', 'id');
    }

    // 关联前端分类2 
    // 20250605 强制改成 cat_id
    public function frontCategory2(): BelongsTo
    {
        return $this->belongsTo(ProductCategoryTeMuModel::class, 'cat_id', 'id');
    }

    // 关联分类关系系统表
    public function catRelations(): HasMany
    {
        return $this->hasMany(CatRelationSystemModel::class, 'cat_platform_id', 'cat_id')
                    ->where('platform_id', 1);
    }

    // 关联用户商品目录
    public function directory(): BelongsTo
    {
        return $this->belongsTo(UserGoodsDirectoryModel::class, 'directory_id', 'id');
    }

    // 作用域：按用户ID筛选
    public function scopeByUserId($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    // 作用域：按商品类型筛选
    public function scopeByType($query, int $type)
    {
        return $query->where('type', $type);
    }

    // 作用域：按状态筛选
    public function scopeByStatus($query, int $status)
    {
        return $query->where('status', $status);
    }

    // 作用域：活跃状态
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    // 作用域：按目录ID筛选
    public function scopeByDirectoryId($query, int $directoryId)
    {
        return $query->where('directory_id', $directoryId);
    }
}