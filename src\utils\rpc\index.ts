// RPC模块统一导出文件
declare const chrome: any;

export * from './domOperations';
export * from './networkListener';
export * from './pageController';

// 主要功能导出
export { getAllRejectedProductsViaRPC } from './pageController';

// 类型定义导出
export type {
  DomOperationResult,
  PageStatus,
  WaitElementConfig
} from './domOperations';

export type {
  NetworkListenerConfig,
  NetworkListenerResult,
  CapturedRequestData,
  CapturedResponseData
} from './networkListener';

export type {
  PageControllerConfig,
  DataFetchProgress,
  DataFetchResult
} from './pageController';

// 配置选项
export interface RPCConfig {
  // DOM操作配置
  maxRetries?: number;          // 最大重试次数，默认3
  pageWaitTime?: number;        // 页面切换等待时间，默认2000ms
  requestTimeout?: number;      // 请求超时时间，默认10000ms
  
  // 网络监听配置
  networkTimeout?: number;      // 网络监听超时时间，默认15000ms
  
  // 页面检测配置
  elementWaitTimeout?: number;  // 元素等待超时时间，默认10000ms
  elementCheckInterval?: number; // 元素检查间隔，默认500ms
  
  // 调试器配置
  suppressDebuggerNotification?: boolean; // 是否抑制调试器通知，默认true
  autoCleanupOnError?: boolean; // 错误时是否自动清理，默认true
}

// 默认配置
export const DEFAULT_RPC_CONFIG: Required<RPCConfig> = {
  maxRetries: 3,
  pageWaitTime: 2000,
  requestTimeout: 10000,
  networkTimeout: 15000,
  elementWaitTimeout: 10000,
  elementCheckInterval: 500,
  suppressDebuggerNotification: true,
  autoCleanupOnError: true
};

// 错误类型定义
export class RPCError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'RPCError';
  }
}

export class NetworkListenerError extends RPCError {
  constructor(message: string, details?: any) {
    super(message, 'NETWORK_LISTENER_ERROR', details);
    this.name = 'NetworkListenerError';
  }
}

export class DOMOperationError extends RPCError {
  constructor(message: string, details?: any) {
    super(message, 'DOM_OPERATION_ERROR', details);
    this.name = 'DOMOperationError';
  }
}

export class PageValidationError extends RPCError {
  constructor(message: string, details?: any) {
    super(message, 'PAGE_VALIDATION_ERROR', details);
    this.name = 'PageValidationError';
  }
}

export class DebuggerConflictError extends RPCError {
  constructor(message: string, details?: any) {
    super(message, 'DEBUGGER_CONFLICT_ERROR', details);
    this.name = 'DebuggerConflictError';
  }
}

/**
 * 调试器状态管理器
 */
class DebuggerManager {
  private static attachedTabs: Set<number> = new Set();
  private static conflictHandlers: Map<number, () => void> = new Map();
  private static suppressNotifications: boolean = DEFAULT_RPC_CONFIG.suppressDebuggerNotification;
  
  /**
   * 设置是否抑制调试器通知
   */
  static setSuppressNotifications(suppress: boolean): void {
    this.suppressNotifications = suppress;
    console.log(`调试器通知抑制: ${suppress ? '已启用' : '已禁用'}`);
  }
  
  /**
   * 注册标签页为已附加调试器
   */
  static registerAttached(tabId: number): void {
    this.attachedTabs.add(tabId);
    console.log(`标签页 ${tabId} 已注册为调试器附加状态`);
  }
  
  /**
   * 取消注册标签页的调试器附加状态
   */
  static unregisterAttached(tabId: number): void {
    this.attachedTabs.delete(tabId);
    this.conflictHandlers.delete(tabId);
    console.log(`标签页 ${tabId} 已取消调试器附加状态注册`);
  }
  
  /**
   * 检查标签页是否已附加调试器
   */
  static isAttached(tabId: number): boolean {
    return this.attachedTabs.has(tabId);
  }
  
  /**
   * 强制清理指定标签页的调试器
   */
  static async forceCleanup(tabId: number): Promise<boolean> {
    try {
      console.log(`开始强制清理标签页 ${tabId} 的调试器...`);
      
      // 尝试分离多次
      for (let i = 0; i < 3; i++) {
        try {
          await new Promise<void>((resolve) => {
            chrome.debugger.detach({ tabId }, () => {
              resolve(); // 忽略错误
            });
          });
          
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (error) {
          // 忽略单次清理错误
        }
      }
      
      this.unregisterAttached(tabId);
      console.log(`标签页 ${tabId} 调试器强制清理完成`);
      return true;
      
    } catch (error) {
      console.error(`强制清理标签页 ${tabId} 调试器失败:`, error);
      return false;
    }
  }
  
  /**
   * 清理所有附加的调试器
   */
  static async cleanupAll(): Promise<void> {
    console.log('开始清理所有附加的调试器...');
    
    const cleanupPromises = Array.from(this.attachedTabs).map(tabId => 
      this.forceCleanup(tabId)
    );
    
    await Promise.allSettled(cleanupPromises);
    this.attachedTabs.clear();
    this.conflictHandlers.clear();
    
    console.log('所有调试器清理完成');
  }
  
  /**
   * 处理调试器冲突
   */
  static async handleConflict(tabId: number, retryCallback?: () => Promise<void>): Promise<boolean> {
    try {
      console.log(`处理标签页 ${tabId} 的调试器冲突...`);
      
      // 先尝试强制清理
      const cleanupSuccess = await this.forceCleanup(tabId);
      
      if (cleanupSuccess && retryCallback) {
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log(`标签页 ${tabId} 冲突处理完成，开始重试...`);
        
        try {
          await retryCallback();
          return true;
        } catch (retryError) {
          console.error(`标签页 ${tabId} 重试失败:`, retryError);
          return false;
        }
      }
      
      return cleanupSuccess;
      
    } catch (error) {
      console.error(`处理标签页 ${tabId} 调试器冲突失败:`, error);
      return false;
    }
  }
  
  /**
   * 显示用户友好的调试器提示
   */
  static showDebuggerNotification(tabId: number, action: 'attached' | 'detached' | 'conflict'): void {
    if (this.suppressNotifications) {
      return; // 如果设置了抑制通知，则不显示
    }
    
    const messages = {
      attached: `网络监听已启动 (标签页 ${tabId})`,
      detached: `网络监听已停止 (标签页 ${tabId})`, 
      conflict: `检测到调试器冲突，正在自动处理... (标签页 ${tabId})`
    };
    
    console.log(`📋 ${messages[action]}`);
    
    // 可以在这里添加用户通知逻辑，比如显示友好的提示而不是Chrome的原生提示
    // 例如：显示一个自定义的提示框或者状态指示器
  }
}

// 工具函数
/**
 * 检查当前环境是否支持RPC操作
 */
export function isRPCSupported(): boolean {
  try {
    // 检查Chrome扩展环境
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      return false;
    }

    // 检查必要的API
    const requiredAPIs = [
      'chrome.scripting',
      'chrome.debugger',
      'chrome.tabs'
    ];

    for (const api of requiredAPIs) {
      const apiPath = api.split('.');
      let obj = chrome;
      for (const path of apiPath.slice(1)) {
        if (!obj || typeof obj[path] === 'undefined') {
          console.warn(`缺少必要的API: ${api}`);
          return false;
        }
        obj = obj[path];
      }
    }

    return true;
  } catch (error) {
    console.error('检查RPC支持时发生错误:', error);
    return false;
  }
}

/**
 * 初始化RPC环境，设置默认配置
 */
export function initializeRPC(config: Partial<RPCConfig> = {}): void {
  const finalConfig = { ...DEFAULT_RPC_CONFIG, ...config };
  
  // 设置调试器管理器
  DebuggerManager.setSuppressNotifications(finalConfig.suppressDebuggerNotification);
  
  console.log('RPC环境已初始化，配置:', finalConfig);
}

/**
 * 清理所有RPC资源
 */
export async function cleanupAllRPCResources(): Promise<void> {
  try {
    console.log('开始清理所有RPC资源...');
    
    // 清理调试器
    await DebuggerManager.cleanupAll();
    
    // 清理网络监听器
    const { cleanupAllNetworkListeners } = await import('./networkListener');
    await cleanupAllNetworkListeners();
    
    console.log('所有RPC资源清理完成');
    
  } catch (error) {
    console.error('清理RPC资源时发生错误:', error);
  }
}

/**
 * 导出调试器管理器（用于高级用法）
 */
export { DebuggerManager };

/**
 * 检查页面是否为N11产品列表页面（便捷函数）
 */
export async function checkN11ProductListPage(): Promise<boolean> {
  try {
    // 基础环境检查
    if (typeof chrome === 'undefined' || !chrome.runtime || !chrome.tabs || !chrome.scripting) {
      console.log('Chrome API环境不完整');
      return false;
    }

    // 获取当前活动标签页
    const tabs = await new Promise<any[]>((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(result);
        }
      });
    });

    if (!tabs || tabs.length === 0) {
      console.log('未找到活动标签页');
      return false;
    }

    const tabId = tabs[0].id;
    const url = tabs[0].url;
    
    // 首先通过URL进行基础检查
    if (!url || !url.includes('so.n11.com')) {
      console.log('当前页面不是N11网站');
      return false;
    }

    // 通过chrome.scripting检查DOM结构
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          try {
            const url = window.location.href;
            const hasCorrectUrl = url.includes('so.n11.com') && 
                                 (url.includes('product') || url.includes('urun'));
            
            const hasProductList = document.querySelector('.product-list') ||
                                  document.querySelector('[class*="product"]') ||
                                  document.querySelector('.tabManager');
            
            return hasCorrectUrl && !!hasProductList;
          } catch (error) {
            return false;
          }
        }
      });
      
      return !!(results && results[0] && results[0].result);
    } catch (scriptError) {
      console.error('执行页面检查脚本失败:', scriptError);
      // 如果脚本执行失败，回退到URL检查
      return url.includes('so.n11.com') && (url.includes('product') || url.includes('urun'));
    }
  } catch (error) {
    console.error('检查N11页面时发生错误:', error);
    return false;
  }
}

/**
 * 获取RPC功能状态信息
 */
export interface RPCStatus {
  supported: boolean;
  chromeAPIs: {
    scripting: boolean;
    debugger: boolean;
    tabs: boolean;
  };
  currentPage: {
    isN11: boolean;
    url?: string;
    tabId?: number;
  };
  permissions: {
    debugger: boolean;
    scripting: boolean;
    activeTab: boolean;
  };
}

/**
 * 获取RPC功能详细状态 - 安全版本（避免在content script中直接使用Chrome API）
 */
export async function getRPCStatus(): Promise<RPCStatus & { message?: string; error?: string }> {
  const status: RPCStatus & { message?: string; error?: string } = {
    supported: false,
    chromeAPIs: {
      scripting: false,
      debugger: false,
      tabs: false
    },
    currentPage: {
      isN11: false
    },
    permissions: {
      debugger: false,
      scripting: false,
      activeTab: false
    }
  };

  try {
    console.log('开始检查RPC状态...');
    
    // 检查基本Chrome环境
    if (typeof chrome === 'undefined') {
      status.message = 'Chrome对象未定义';
      status.error = 'Chrome extension environment not detected';
      console.error('Chrome环境不可用：chrome对象未定义');
      return status;
    }

    if (!chrome.runtime) {
      status.message = 'chrome.runtime不可用';
      status.error = 'chrome.runtime API not available';
      console.error('Chrome运行时环境不可用');
      return status;
    }

    console.log('Chrome运行时环境检查通过');

    // 检测运行环境类型
    const isBackgroundContext = !!(chrome.tabs && chrome.scripting && chrome.debugger);
    const isContentScriptContext = typeof window !== 'undefined' && typeof document !== 'undefined';
    
    console.log('环境检测结果:', { isBackgroundContext, isContentScriptContext });

    if (isBackgroundContext) {
      // 在background环境中，可以直接检查API
      console.log('在Background环境中执行RPC状态检查');
      
      status.chromeAPIs.scripting = !!(chrome.scripting && chrome.scripting.executeScript);
      status.chromeAPIs.debugger = !!(chrome.debugger && chrome.debugger.attach);
      status.chromeAPIs.tabs = !!(chrome.tabs && chrome.tabs.query);
      
      console.log('Chrome API检查结果:', status.chromeAPIs);

      // 检查权限
      if (chrome.permissions) {
        try {
          const permissions = await new Promise<any>((resolve, reject) => {
            chrome.permissions.getAll((result: any) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(result);
              }
            });
          });

          status.permissions.debugger = permissions.permissions?.includes('debugger') || false;
          status.permissions.scripting = permissions.permissions?.includes('scripting') || false;
          status.permissions.activeTab = permissions.permissions?.includes('activeTab') || false;
          
          console.log('权限检查结果:', status.permissions);
        } catch (permError) {
          console.error('权限检查失败:', permError);
          status.error = `权限检查失败: ${permError}`;
        }
      }

      // 检查当前页面
      if (chrome.tabs) {
        try {
          const tabs = await new Promise<any[]>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(result);
              }
            });
          });

          if (tabs && tabs.length > 0) {
            const activeTab = tabs[0];
            status.currentPage.url = activeTab.url;
            status.currentPage.tabId = activeTab.id;
            
            if (activeTab.url) {
              status.currentPage.isN11 = activeTab.url.includes('so.n11.com') && 
                                       (activeTab.url.includes('product') || activeTab.url.includes('urun'));
            }
          }
        } catch (tabError) {
          console.error('获取当前页面信息失败:', tabError);
          status.error = `获取页面信息失败: ${tabError}`;
        }
      }

    } else if (isContentScriptContext) {
      // 在content script环境中，使用消息传递方式获取状态
      console.log('在Content Script环境中，通过消息传递获取RPC状态');
      
      try {
        const result = await new Promise<any>((resolve, reject) => {
          chrome.runtime.sendMessage({
            funType: 'rpcPageControl',
            action: 'getRPCStatus'
          }, (response: any) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(response);
            }
          });
        });

        if (result) {
          Object.assign(status, result);
          console.log('从Background获取的RPC状态:', result);
        } else {
          status.message = '未能从Background获取RPC状态';
        }
      } catch (msgError) {
        console.error('消息传递获取RPC状态失败:', msgError);
        status.message = '消息传递失败';
        status.error = `消息传递错误: ${msgError}`;
      }

      // 在content script中也可以检查当前页面URL
      if (typeof window !== 'undefined' && window.location) {
        const url = window.location.href;
        status.currentPage.url = url;
        status.currentPage.isN11 = url.includes('so.n11.com') && 
                                 (url.includes('product') || url.includes('urun'));
        console.log('Content Script页面检查结果:', status.currentPage.isN11);
      }

    } else {
      // 其他环境（如popup）
      console.log('在其他环境中，尝试通过消息传递获取RPC状态');
      
      status.message = '当前环境不支持直接RPC操作';
      status.error = '需要在Background或Content Script环境中执行';
    }

    // 综合判断是否支持
    const apiSupported = status.chromeAPIs.scripting && 
                        status.chromeAPIs.debugger && 
                        status.chromeAPIs.tabs;
    
    const permissionsGranted = status.permissions.debugger &&
                              status.permissions.scripting;
    
    status.supported = apiSupported && permissionsGranted;
    
    console.log('最终RPC支持状态:', status.supported);
    
    if (!status.supported && !status.message) {
      const reasons = [];
      if (!apiSupported) reasons.push('Chrome API不完整');
      if (!permissionsGranted) reasons.push('权限不足');
      status.message = reasons.join(', ');
    }

    return status;
  } catch (error: any) {
    console.error('获取RPC状态时发生错误:', error);
    status.message = '状态检查失败';
    status.error = error.message || '未知错误';
    return status;
  }
} 