<template>
  <div class="login-expired-container">
    <div class="login-expired-card">
      <div class="icon-container">
        <div class="warning-icon">⚠️</div>
      </div>
      
      <h2 class="title">登录已失效</h2>
      
      <p class="message">
        您的登录状态已过期，请重新登录。
      </p>
      
      <div class="instructions">
        <h3>操作步骤：</h3>
        <ol>
          <li>点击浏览器工具栏中的"跨境蜂"扩展图标</li>
          <li>在弹出窗口中重新登录您的账户</li>
          <li>登录成功后，重新打开用户中心</li>
        </ol>
      </div>
      
      <div class="status-info">
        <p class="status-text">
          <span class="status-icon">🔍</span>
          正在等待您重新登录...
        </p>
        <p class="status-detail">
          系统将自动检测您的登录状态，无需手动刷新页面
        </p>
      </div>
      
      <div class="actions">
        <button class="refresh-btn" @click="refreshPage">
          <span class="refresh-icon">🔄</span>
          刷新页面
        </button>
      </div>
      
      <div class="footer">
        <p class="footer-text">
          如果问题持续存在，请联系技术支持
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { setLoginExpired } from '../utils/loginState'
import { getUserInfo } from '../utils/storage'

const isChecking = ref(false)
let storageListener: ((changes: any, namespace: any) => void) | null = null
let messageListener: ((message: any, sender: any, sendResponse: any) => void) | null = null

const refreshPage = () => {
  window.location.reload();
};

// 检查登录状态恢复（一次性检测）
const checkLoginRecovery = async () => {
  if (isChecking.value) return // 防止重复检测
  
  try {
    isChecking.value = true
    const userInfo = await getUserInfo()
    
    // 如果有token且登录状态为true，说明用户已经重新登录
    if (userInfo.token && userInfo.isLogin) {
      console.log('检测到用户已重新登录，重置登录状态并跳转到首页')
      setLoginExpired(false)
      
      // 清理所有监听器
      cleanup()
      
      // 跳转到首页
      window.location.hash = '#/dashboard'
      return true
    }
  } catch (error) {
    console.error('检查登录恢复状态失败:', error)
  } finally {
    isChecking.value = false
  }
  return false
}

// 处理登录成功消息
const handleLoginSuccess = async () => {
  console.log('收到登录成功消息，立即检查登录状态')
  await checkLoginRecovery()
}

// 清理资源
const cleanup = () => {
  // 清理storage监听器
  if (storageListener && chrome.storage && chrome.storage.onChanged) {
    chrome.storage.onChanged.removeListener(storageListener)
    storageListener = null
  }
  
  // 清理消息监听器
  if (messageListener && chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.removeListener(messageListener)
    messageListener = null
  }
}

onMounted(async () => {
  // 首次检查登录状态
  const recovered = await checkLoginRecovery()
  if (recovered) return // 如果已经恢复，直接返回
  
  // 设置消息监听器 - 监听来自popup的登录成功消息
  if (chrome.runtime && chrome.runtime.onMessage) {
    messageListener = (message: any, sender: any, sendResponse: any) => {
      if (message.type === 'USER_LOGIN_SUCCESS') {
        console.log('收到用户登录成功消息:', message)
        handleLoginSuccess()
      }
    }
    chrome.runtime.onMessage.addListener(messageListener)
    console.log('已设置消息监听器，等待登录成功通知')
  }
  
  // 设置storage监听器 - 作为备用机制
  if (chrome.storage && chrome.storage.onChanged) {
    storageListener = (changes: any, namespace: any) => {
      // 监听 sync storage 中的登录状态变化
      if (namespace === 'sync') {
        if (changes.is_login && changes.is_login.newValue === true) {
          console.log('检测到storage中登录状态变为true，重新检查登录状态')
          handleLoginSuccess()
        }
      }
      // 监听 local storage 中的token变化
      if (namespace === 'local' && changes.token && changes.token.newValue) {
        console.log('检测到storage中token更新，重新检查登录状态')
        handleLoginSuccess()
      }
    }
    
    chrome.storage.onChanged.addListener(storageListener)
    console.log('已设置storage监听器作为备用检测机制')
  }
  
  // 如果chrome API不可用，降级使用单次定时检查
  if (!chrome.runtime || !chrome.storage) {
    console.log('Chrome API不可用，使用降级方案')
    setTimeout(checkLoginRecovery, 3000) // 3秒后检查一次
  }
})

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.login-expired-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-expired-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.icon-container {
  margin-bottom: 24px;
}

.warning-icon {
  font-size: 4rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.title {
  color: #333;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 16px;
  margin-top: 0;
}

.message {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 32px;
}

.instructions {
  text-align: left;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
}

.instructions h3 {
  color: #333;
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 12px;
}

.instructions ol {
  color: #555;
  line-height: 1.6;
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
}

.status-info {
  margin-bottom: 32px;
}

.status-text {
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.status-detail {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.status-icon {
  margin-right: 8px;
}

.actions {
  margin-bottom: 32px;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.refresh-btn:active {
  transform: translateY(0);
}

.refresh-icon {
  font-size: 1.1rem;
}

.footer {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.footer-text {
  color: #999;
  font-size: 0.9rem;
  margin: 0;
}
</style> 