<?php
declare(strict_types=1);

namespace App\Utils;

class MyDateTime
{
    /*
     * 第一个参数 可以是日期格式  也可以是时间戳
     * */

    public static function format_time($format='Y-m-d H:i:s',$time=0)
    {
        if(is_numeric($format) && $format > 0){
            $time = $format;
            $format = 'Y-m-d H:i:s';
        }
        if($time<=0){
            $time = time();
        }

        return date($format,$time);
    }
}
