<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心开发环境 - 跨境蜂</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #eef4ff;
        }

        /* 加载动画 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #1890ff;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }

        .loading-text {
            color: #666;
            font-size: 16px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 开发环境提示 */
        .dev-notice {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: #ff9800;
            color: white;
            text-align: center;
            padding: 8px;
            font-size: 14px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- 开发环境提示 -->
    <div class="dev-notice">
        🚧 开发环境 - 模拟Chrome扩展环境 🚧
    </div>
    
    <div id="app">
        <!-- 加载动画，在Vue应用加载前显示 -->
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载用户中心...</div>
        </div>
    </div>

    <!-- 模拟Chrome API -->
    <script>
        // 模拟Chrome扩展API用于开发环境
        if (!window.chrome) {
            window.chrome = {
                runtime: {
                    getManifest: () => ({
                        name: "跨境蜂助手",
                        version: "1.0.0"
                    }),
                    lastError: null,
                    sendMessage: (message, callback) => {
                        console.log('模拟chrome.runtime.sendMessage:', message);
                        // 模拟异步响应
                        setTimeout(() => {
                            if (callback) {
                                callback([{ data: { success: true } }]);
                            }
                        }, 100);
                    }
                },
                storage: {
                    sync: {
                        get: (keys, callback) => {
                            console.log('模拟chrome.storage.sync.get:', keys);
                            // 模拟用户数据
                            const mockData = {
                                is_login: true,
                                phone: '138****8888',
                                expiryDate: '2024-12-31',
                                isVip: true
                            };
                            setTimeout(() => callback(mockData), 50);
                        },
                        set: (data, callback) => {
                            console.log('模拟chrome.storage.sync.set:', data);
                            setTimeout(() => callback && callback(), 50);
                        }
                    },
                    local: {
                        get: (keys, callback) => {
                            console.log('模拟chrome.storage.local.get:', keys);
                            // 模拟token数据
                            const mockData = {
                                token: 'mock_token_for_development'
                            };
                            setTimeout(() => callback(mockData), 50);
                        },
                        set: (data, callback) => {
                            console.log('模拟chrome.storage.local.set:', data);
                            setTimeout(() => callback && callback(), 50);
                        }
                    }
                }
            };
        }
    </script>
    
    <script src="main.js"></script>
</body>
</html> 