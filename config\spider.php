<?php
return [
    'header_user_agent_mobile' => 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/108.0.1462.76',
    'header_user_agent_pc' =>'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept_encoding' => 'gzip,deflate',
    'source_url_pre' => 'http://www.test.com/attachment'
];
