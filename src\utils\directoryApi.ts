/**
 * 目录API接口
 */

export interface CreateDirectoryData {
  name: string
  description?: string
  status: number
}

/**
 * 获取配置中的API地址
 */
const getApiUrl = (configKey: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'getConfig',
      configKey
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      if (response?.url) {
        resolve(response.url);
      } else {
        resolve('');
      }
    });
  });
};

/**
 * 发送请求到background
 */
const sendRequest = (config: any): Promise<any> => {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      ...config,
      auth: true,
      encrypto: true
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      
      if (response && response[0] && response[0].data) {
        const result = response[0].data;
        if (result.code === 1) {
          resolve(result.data);
        } else {
          reject(new Error(result.errMsg || '请求失败'));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
};

/**
 * 创建目录
 */
export const createDirectory = async (data: CreateDirectoryData): Promise<any> => {
  const url = await getApiUrl('apiGoodsDirectoryCreateUrl');
  
  return sendRequest({
    url,
    method: 'post',
    pramas: data,
    funName: 'createDirectory'
  });
}; 