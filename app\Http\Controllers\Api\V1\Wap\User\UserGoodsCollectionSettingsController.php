<?php

namespace App\Http\Controllers\Api\V1\Wap\User;

use App\Http\Controllers\Api\Controller;
use App\Service\User\UserGoodsCollectionSettingsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserGoodsCollectionSettingsController extends Controller
{
    protected UserGoodsCollectionSettingsService $settingsService;

    public function __construct(UserGoodsCollectionSettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
        parent::__construct();
    }

    /**
     * 获取用户采集设置
     */
    public function getSettings(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $result = $this->settingsService->getUserCollectionSettings($userId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 保存用户采集设置
     */
    public function saveSettings(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $data = $request->only([
            'default_directory_id', 
            'collection_mode', 
            'no_remind_24h'
        ]);
        
        $result = $this->settingsService->saveUserCollectionSettings($userId, $data);
        
        return $this->apiSuccess($result);
    }

    /**
     * 获取用户可用目录列表
     */
    public function getAvailableDirectories(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $result = $this->settingsService->getUserAvailableDirectories($userId);
        
        return $this->apiSuccess($result);
    }

    /**
     * 检查用户是否需要设置采集参数
     */
    public function checkNeedSetup(Request $request): JsonResponse
    {
        $user = $request->attributes->get('user');
        $userId = $user['id'];
        
        $result = $this->settingsService->checkUserNeedSetup($userId);
        
        return $this->apiSuccess($result);
    }
}