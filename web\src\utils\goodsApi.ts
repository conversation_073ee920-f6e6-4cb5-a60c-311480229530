/**
 * 商品管理API接口
 * 参考storeApi.ts的结构，通过background页面发送请求
 */
import { sendRequestViaBackground } from './api'
import { getApiUrl } from './apiConfig'

// 商品接口类型定义
export interface Goods {
  id?: number
  type_name: string
  source_url: string
  mall_id: number
  goods_id: number
  goods_name: string
  goods_thumb:string
  goods_pic:Array<string>
  goods_video:string
  goods_sku_num:number
  first_sku:string
  first_sku_price:number
  first_sku_currentcy:string
  formatted_skus:Array<{
    sku:string
    price:number
    currentcy:string
  }>
  cat_id:number
  cat_name:string,
  front_cat_id_1:number
  front_cat_id_2:number
  front_cat_desc:string
  front_cat_2_path_name:string
  status: number  // 状态：1正常 0禁用
  created_at?: string
  updated_at?: string
}

export interface GoodsListParams {
  page: number
  pageSize: number
  goods_name?: string
  goods_id?: number
  status?: number  // 状态搜索参数：1正常 0禁用
  directory_id?: number  // 目录ID筛选参数
}

export interface GoodsListResponse {
  list: Goods[]
  pagination: {
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
    currentPage: number
    pageSize: number
  }
}

export interface BatchUpdateParams {
  ids: number[]
  status?: number  // 批量状态设置
  goods_price?: number  // 批量价格设置
}



/**
 * 获取商品列表
 * @param params 查询参数
 * @returns 商品列表响应
 */
export const getGoodsList = async (params: GoodsListParams): Promise<GoodsListResponse> => {
  const url = await getApiUrl('apiGoodsListUrl');
  console.log('获取商品列表URL:', url)
  return sendRequestViaBackground({
    funName: 'getGoodsList',
    url,
    method: 'get',
    params,
    auth: true
  });
};

/**
 * 创建商品
 * @param goods 商品信息
 * @returns 创建结果
 */
export const createGoods = async (goods: Omit<Goods, 'id' | 'createTime' | 'updateTime'>): Promise<Goods> => {
  const url = await getApiUrl('apiGoodsAddUrl');
  
  return sendRequestViaBackground({
    funName: 'createGoods',
    url,
    method: 'post',
    data: goods,
    auth: true
  });
};

/**
 * 更新商品
 * @param goods 商品信息
 * @returns 更新结果
 */
export const updateGoods = async (goods: Partial<Goods>): Promise<Goods> => {
  const baseUrl = await getApiUrl('apiGoodsUpdateUrl');
  const url = `${baseUrl}`;
  
  return sendRequestViaBackground({
    funName: 'updateGoods',
    url,
    method: 'post',
    data: goods,
    auth: true
  });
};

/**
 * 删除商品
 * @param id 商品ID
 * @returns 删除结果
 */
export const deleteGoods = async (id: number): Promise<void> => {
  const baseUrl = await getApiUrl('apiGoodsDeleteUrl');
  const url = `${baseUrl}/${id}`;
  
  return sendRequestViaBackground({
    funName: 'deleteGoods',
    url,
    method: 'delete',
    auth: true
  });
};

/**
 * 批量更新商品
 * @param params 批量更新参数
 * @returns 更新结果
 */
export const batchUpdateGoods = async (params: BatchUpdateParams): Promise<void> => {
  const url = await getApiUrl('apiGoodsBatchUpdateUrl');
  
  return sendRequestViaBackground({
    funName: 'batchUpdateGoods',
    url,
    method: 'post',
    data: params,
    auth: true
  });
};

/**
 * 获取商品详情
 * @param id 商品ID
 * @returns 商品详情
 */
export const getGoodsDetail = async (id: number): Promise<Goods> => {
  const baseUrl = await getApiUrl('apiGoodsDetailUrl');
  const url = `${baseUrl}/${id}`;
  
  return sendRequestViaBackground({
    funName: 'getGoodsDetail',
    url,
    method: 'get',
    auth: true
  });
};

 