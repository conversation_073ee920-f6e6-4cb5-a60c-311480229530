import { DOMFinderConfig, DOMFinderResult } from '@/types/domFinder';

export class DOM<PERSON>inder {
  private config: DOMFinderConfig;
  private shadowRoot: ShadowRoot | null = null;

  constructor(config: DOMFinderConfig) {
    this.config = config;
  }

  public updateConfig(newConfig: DOMFinderConfig): void {
    this.config = newConfig;
  }

  private getShadowRoot(): ShadowRoot | null {
    const wujieApp = document.querySelector(this.config.shadowRoot.selector);

    // 如果配置了属性验证，进行验证
    if (this.config.shadowRoot.attribute && wujieApp) {
      if (!wujieApp.hasAttribute(this.config.shadowRoot.attribute)) {
        console.warn('shadowRoot容器缺少必要的属性:', this.config.shadowRoot.attribute);
        return null;
      }
    }

    return wujieApp?.shadowRoot || null;
  }

  private findElementBySelectors(selectors: string[], context: Element | ShadowRoot = document.body): Element | null {
    for (const selector of selectors) {
      try {
        const element = context.querySelector(selector);
        if (element) {
          return element;
        }
      } catch (error) {
        console.warn(`选择器 ${selector} 查找失败:`, error);
      }
    }
    return null;
  }

  private validateElement(element: Element): boolean {
    if (!element) return false;

    // 验证属性
    for (const attr of this.config.validation.attributes) {
      if (!element.hasAttribute(attr)) {
        return false;
      }
    }

    // 验证DOM结构
    for (const structureRule of this.config.validation.structure) {
      if (!element.matches(structureRule)) {
        return false;
      }
    }

    return true;
  }

  public findTargetElement(): DOMFinderResult {
    try {
      // 1. 获取shadowRoot
      this.shadowRoot = this.getShadowRoot();
      if (!this.shadowRoot) {
        return {
          element: null,
          success: false,
          error: '找不到 Shadow DOM',
          strategy: 'shadow-root'
        };
      }

      // 2. 尝试使用主选择器
      let element = this.findElementBySelectors(this.config.selectors.primary, this.shadowRoot);
      if (element && this.validateElement(element)) {
        return {
          element,
          success: true,
          strategy: 'primary'
        };
      }

      // 3. 如果主选择器失败，尝试使用备选选择器
      element = this.findElementBySelectors(this.config.selectors.fallback, this.shadowRoot);
      if (element && this.validateElement(element)) {
        return {
          element,
          success: true,
          strategy: 'fallback'
        };
      }

      // 4. 如果都失败了，尝试在上下文中查找
      for (const contextSelector of this.config.selectors.context) {
        const context = this.shadowRoot.querySelector(contextSelector);
        if (!context) continue;

        element = this.findElementBySelectors(this.config.selectors.primary, context);
        if (element && this.validateElement(element)) {
          return {
            element,
            success: true,
            strategy: 'context-primary'
          };
        }
      }

      return {
        element: null,
        success: false,
        error: '无法找到目标元素',
        strategy: 'all-failed'
      };

    } catch (error) {
      return {
        element: null,
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        strategy: 'error'
      };
    }
  }
}
