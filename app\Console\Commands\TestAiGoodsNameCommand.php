<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Service\User\UserTaskService;
use App\Models\User\UserTaskDetailModel;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;

class TestAiGoodsNameCommand extends Command
{
    /**
     * The name and signature of the console command.
     * php artisan test:ai-goods-name 228
     * @var string
     */
    protected $signature = 'test:ai-goods-name {task_detail_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test AI goods name generation for a given task detail id.';

    protected UserTaskService $userTaskService;
    protected UserTaskDetailModel $userTaskDetailModel;
    protected GoodsModel $goodsModel;
    protected GoodsSkuModel $goodsSkuModel;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UserTaskService $userTaskService,
        UserTaskDetailModel $userTaskDetailModel,
        GoodsModel $goodsModel,
        GoodsSkuModel $goodsSkuModel
    )
    {
        parent::__construct();
        $this->userTaskService = $userTaskService;
        $this->userTaskDetailModel = $userTaskDetailModel;
        $this->goodsModel = $goodsModel;
        $this->goodsSkuModel = $goodsSkuModel;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $taskDetailId = $this->argument('task_detail_id');
        if (!$taskDetailId) {
            $this->error("Task detail ID is required.");
            return Command::FAILURE;
        }
        $detail = null;

        if ($taskDetailId) {
            // 尝试根据 task_detail_id 获取任务详情
            // 假设 user_id 为 1，实际应用中应根据登录用户获取
            $detail = $this->userTaskDetailModel
                        ->where('id', $taskDetailId)
                        ->with(['goods','goodsSku', 'store'])
                        ->first();
            if (!$detail) {
                $this->error("Task detail with ID {$taskDetailId} not found for user ID 3.");
                return Command::FAILURE;
            }
            $goods = GoodsModel::find($detail->user_goods_id);
        }

        try {
            // 调用 UserTaskService 中的 formatTaskDetailData 方法来获取 AI 重写后的商品名称
            $formattedDetail = $this->userTaskService->formatTaskDetailData($detail);
            $aiGoodsName = $formattedDetail['goods_name'];

            $this->info("Original Goods Name: {$goods->goods_name}");
            $this->info("AI Rewritten Goods Name: {$aiGoodsName}");
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Error testing AI goods name: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}