<?php

namespace App\Service\User;

use App\Service\BaseService;
use App\Models\User\UserGoodsDirectoryModel;
use App\Models\User\GoodsModel;
use App\Exceptions\MyException;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class UserGoodsDirectoryService extends BaseService
{
    /**
     * 获取目录列表（分页）
     */
    public function getDirectoryList(int $userId, array $params): array
    {
        // 获取分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 10)));
        
        // 获取筛选参数
        $status = $params['status'] ?? null;
        $name = $params['name'] ?? null;
        $startDate = $params['start_date'] ?? null;
        $endDate = $params['end_date'] ?? null;

        // 构建查询
        $query = UserGoodsDirectoryModel::byUserId($userId);

        // 按状态筛选
        if (is_numeric($status) && in_array($status, [0, 1])) {
            $query->byStatus($status);
        }

        // 按名称筛选
        if ($name) {
            $query->where('name', 'like', "%{$name}%");
        }

        // 按创建时间范围筛选
        if ($startDate || $endDate) {
            $query->byDateRange($startDate, $endDate);
        }

        // 排序
        $query->ordered();

        // 分页查询
        $total = $query->count();
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        
        $directories = $query->offset($offset)
                            ->limit($pageSize)
                            ->get()
                            ->map(function($item) {
                                return $this->formatDirectoryData($item);
                            });

        return [
            'list' => $directories,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'total' => $total,
                'totalPages' => $totalPages,
                'hasNext' => $page < $totalPages,
                'hasPrevious' => $page > 1,
            ]
        ];
    }

    /**
     * 创建目录
     */
    public function createDirectory(int $userId, array $data): array
    {
        // 验证数据
        $this->validateDirectoryData($data, true);

        // 检查同一用户下是否已存在相同名称的目录
        $exists = UserGoodsDirectoryModel::byUserId($userId)
            ->where('name', $data['name'])
            ->exists();

        if ($exists) {
            throw new MyException('该目录名称已存在');
        }

        // 创建目录
        $directoryData = array_merge($data, [
            'user_id' => $userId,
            'status' => $data['status'] ?? 1,
            'sort_order' => $data['sort_order'] ?? 0,
            'goods_count' => 0,
        ]);

        $directory = UserGoodsDirectoryModel::create($directoryData);

        return [
            'id' => $directory->id,
            'message' => '目录创建成功'
        ];
    }

    /**
     * 更新目录
     */
    public function updateDirectory(int $userId, array $data): array
    {
        // 验证数据
        $this->validateDirectoryData($data, false);

        if (!isset($data['id'])) {
            throw new MyException('目录ID不能为空');
        }

        // 查找目录并验证所有权
        $directory = UserGoodsDirectoryModel::find($data['id']);
        
        if (!$directory) {
            throw new MyException('目录不存在');
        }

        if (!$directory->belongsToUser($userId)) {
            throw new MyException('无权限操作此目录');
        }

        // 如果更新了名称，检查是否重复
        if (isset($data['name']) && $data['name'] !== $directory->name) {
            $exists = UserGoodsDirectoryModel::byUserId($userId)
                ->where('name', $data['name'])
                ->where('id', '!=', $directory->id)
                ->exists();

            if ($exists) {
                throw new MyException('该目录名称已存在');
            }
        }

        // 更新目录
        $updateData = $data;
        unset($updateData['id']);

        $directory->update($updateData);

        return [
            'message' => '目录更新成功'
        ];
    }

    /**
     * 批量更新目录
     */
    public function batchUpdateDirectory(int $userId, array $data): array
    {
        $count = UserGoodsDirectoryModel::where('user_id', $userId)->whereIn('id', $data['ids'])->count();
        if ($count != count($data['ids'])) {
            throw new MyException('参数错误');
        }
        
        $ids = $data['ids'];
        unset($data['ids']);
        
        if (count($data) == 0) {
            throw new MyException('请至少选择一项');
        }
        
        UserGoodsDirectoryModel::where('user_id', $userId)->whereIn('id', $ids)->update($data);
        
        return [
            'message' => '目录批量更新成功'
        ];
    }

    /**
     * 删除目录
     */
    public function deleteDirectory(int $userId, int $directoryId): array
    {
        // 验证目录ID
        if (!$directoryId) {
            throw new MyException('目录ID不能为空');
        }

        // 查找目录并验证所有权
        $directory = UserGoodsDirectoryModel::find($directoryId);
        
        if (!$directory) {
            throw new MyException('目录不存在');
        }

        if (!$directory->belongsToUser($userId)) {
            throw new MyException('无权限操作此目录');
        }

        // 检查目录下是否有商品
        $goodsCount = GoodsModel::byUserId($userId)
            ->byDirectoryId($directoryId)
            ->count();

        if ($goodsCount > 0) {
            throw new MyException("该目录下还有 {$goodsCount} 个商品，无法删除");
        }

        // 删除目录
        $directory->delete();

        return [
            'message' => '目录删除成功'
        ];
    }

    /**
     * 获取目录详情
     */
    public function getDirectoryDetail(int $userId, int $directoryId): array
    {
        // 验证目录ID
        if (!$directoryId) {
            throw new MyException('目录ID不能为空');
        }

        // 查找目录并验证所有权
        $directory = UserGoodsDirectoryModel::find($directoryId);
        
        if (!$directory) {
            throw new MyException('目录不存在');
        }

        if (!$directory->belongsToUser($userId)) {
            throw new MyException('无权限查看此目录');
        }

        return $this->formatDirectoryDetailData($directory);
    }

    /**
     * 更新目录商品数量
     * 使用统计计算的方法更新值，不使用increment/decrement
     */
    public function updateDirectoryGoodsCount(int $directoryId): void
    {
        $directory = UserGoodsDirectoryModel::find($directoryId);
        if ($directory) {
            // 统计该目录下的有效商品数量
            $goodsCount = GoodsModel::where('user_id', $directory->user_id)
                ->where('directory_id', $directoryId)
                ->where('status', 1)
                ->count();
            
            // 更新目录商品数量
            $directory->update(['goods_count' => $goodsCount]);
        }
    }

    /**
     * 批量更新所有目录的商品数量
     * 使用统计计算的方法更新值，不使用increment/decrement
     */
    public function updateAllDirectoryGoodsCount(int $userId): void
    {
        UserGoodsDirectoryModel::updateAllGoodsCountByUser($userId);
    }

    /**
     * 验证目录数据
     */
    private function validateDirectoryData(array $data, bool $isCreate = false): void
    {
        $rules = [];
        
        if ($isCreate) {
            $rules = [
                'name' => 'required|string|max:100',
            ];
        } else {
            $rules = [
                'id' => 'required|integer',
                'name' => 'sometimes|string|max:100',
            ];
        }

        // 通用验证规则
        $commonRules = [
            'description' => 'nullable|string|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'status' => 'sometimes|integer|in:0,1',
        ];

        $rules = array_merge($rules, $commonRules);

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new MyException($validator->errors()->first());
        }
    }

    /**
     * 格式化目录数据（列表用）
     */
    private function formatDirectoryData($directory): array
    {
        return [
            'id' => $directory->id,
            'user_id' => $directory->user_id,
            'name' => $directory->name,
            'description' => $directory->description,
            'sort_order' => $directory->sort_order,
            'status' => $directory->status,
            'status_name' => $directory->getStatusName(),
            'goods_count' => $directory->goods_count,
            'created_at' => $directory->created_at,
            'updated_at' => $directory->updated_at,
        ];
    }

    /**
     * 格式化目录详情数据
     */
    private function formatDirectoryDetailData($directory): array
    {
        return [
            'id' => $directory->id,
            'user_id' => $directory->user_id,
            'name' => $directory->name,
            'description' => $directory->description,
            'sort_order' => $directory->sort_order,
            'status' => $directory->status,
            'status_name' => $directory->getStatusName(),
            'goods_count' => $directory->goods_count,
            'created_at' => $directory->created_at,
            'updated_at' => $directory->updated_at,
        ];
    }
} 