const fs = require('fs').promises;
const path = require('path');

const mode = process.env.NODE_ENV ?? 'development';
const isProduction = mode === 'production';

console.log('clearOutputDir mode=============', mode);
console.log('clearOutputDir isProduction=====', isProduction);
console.log('clearOutputDir BTYPE=====', process.env.BTYPE);

const outputDir = isProduction ? 'tsa_build' : 'tsa';
const dirPath = path.join(__dirname, '..', outputDir);

console.log('-------clearOutputDir outputDir--------', outputDir);
console.log('-------clearOutputDir dirPath--------', dirPath);

async function clearDirectory(directory) {
    try {
        // 检查目录是否存在
        const exists = await fs.access(directory).then(() => true).catch(() => false);
        if (!exists) {
            console.log(`目录不存在，跳过清理: ${directory}`);
            return;
        }

        const entries = await fs.readdir(directory, { withFileTypes: true });
        
        console.log(`🧹 开始清理输出目录: ${directory}`);
        console.log(`📊 发现 ${entries.length} 个项目需要清理`);

        let deletedCount = 0;
        
        for (const entry of entries) {
            const entryPath = path.join(directory, entry.name);

            if (entry.isDirectory()) {
                // 递归删除子目录
                await clearDirectory(entryPath);
                try {
                    await fs.rmdir(entryPath);
                    deletedCount++;
                    console.log(`🗂️  已删除目录: ${entry.name}`);
                } catch (err) {
                    console.error(`❌ 删除目录失败 ${entryPath}:`, err.message);
                }
            } else if (entry.isFile()) {
                // 删除文件
                try {
                    await fs.unlink(entryPath);
                    deletedCount++;
                    console.log(`📄 已删除文件: ${entry.name}`);
                } catch (err) {
                    console.error(`❌ 删除文件失败 ${entryPath}:`, err.message);
                }
            }
        }

        console.log(`✅ 清理完成！共删除 ${deletedCount} 个项目`);
        
    } catch (err) {
        console.error(`❌ 清理目录失败 ${directory}:`, err.message);
        throw err;
    }
}

// 执行清理
clearDirectory(dirPath).catch(error => {
    console.error('清理过程出错:', error);
    process.exit(1);
}); 