import * as utils_new from '@/utils/new/utils';
import config from "@/config";
import { handleApiResponse, getWebCookie } from '../common/common';

/**
 * 获取图片验证码
 * @returns Promise<{image_key: string, image_base64: string}> 验证码结果
 */
export const getCaptcha = async (): Promise<{image_key: string, image_base64: string}> => {
  const url = config.apiCaptchaUrl;
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  }

  return new Promise<{image_key: string, image_base64: string}>((resolve) => {
    // 创建请求对象
    const request = {
      funType: 'axios',
      funName: 'getCaptchaRequest',
      pramas: {},
      headers,
      method: 'get',
      url,
      auth: false    // 不需要认证
      // 不需要指定encrypto参数，会自动根据url判断
    };

    chrome.runtime.sendMessage(request, (response: any) => {
      console.log("-----------获取验证码-----------");
      console.log('response', response);
      const result = handleApiResponse(response);
      console.log('result', result);
      resolve(result);
    });
  });
}

/**
 * 用户注册方法
 * @param phone 手机号
 * @param password 密码
 * @param captcha 验证码
 * @param imageKey 验证码key
 * @returns Promise<{any}> 注册结果
 */
export const registerUser = async (phone: string, password: string, captcha: string, imageKey: string): Promise<any> => {
  const url = config.apiRegisterUrl;
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  }

  const data = {
    phone,
    password,
    captcha,
    imageKey,
    version: chrome.runtime.getManifest().version
  }

  return new Promise((resolve) => {
    // 创建请求对象
    const request = {
      funType: 'axios',
      funName: 'userRegisterRequest',
      pramas: data,
      headers,
      method: 'post',
      url,
      auth: false     // 不需要认证
    };

    chrome.runtime.sendMessage(request, (response: any) => {
      console.log("-----------用户注册-----------");
      console.log('response', response);
      const result = handleApiResponse(response);
      console.log('result', result);
      resolve(result);
    });
  });
}

/**
 * 用户登录方法
 * @param phone 手机号
 * @param password 密码
 * @returns Promise< {phone: string, is_vip: number, vip_end_time: string, is_admin: number, token: string}> 登录结果
 */
export const loginUser = async (phone: string, password: string): Promise<{phone: string, is_vip: number, vip_end_time: string, is_admin: number, token: string}> => {
  const url = config.apiLoginUrl;
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  }

  const data = {
    phone,
    password,
    version: chrome.runtime.getManifest().version
  }

  return new Promise<{phone: string, is_vip: number, vip_end_time: string, is_admin: number, token: string}>((resolve) => {
    // 创建请求对象
    const request = {
      funType: 'axios',
      funName: 'userLoginRequest',
      pramas: data,
      headers,
      method: 'post',
      url,
      auth: false     // 不需要认证
    };

    chrome.runtime.sendMessage(request, (response: any) => {
      console.log("-----------用户登录-----------");
      console.log('response', response);
      const result = handleApiResponse(response);
      console.log('result', result);
      resolve(result);
    });
  });
}